package com.megabank.olp.client.sender.otp;

import java.io.UnsupportedEncodingException;
import java.net.URI;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.BaseJsonClient;

public abstract class BaseOtpClient<TArg extends BaseOtpArgBean, TReq extends BaseOtpReqBean, TRes extends BaseOtpResBean, TResult extends BaseOtpResultBean>
			extends BaseJsonClient<TArg, TReq, TRes, TResult>
{
	public static final String SYSTEM_NAME = "otp";

	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlThirdpartyOtp() + "/megaOTPSMS/sms/api" + getSuffixUrl() );
	}

	@Override
	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.POST;
	}

	protected abstract String[] getNotCaseErrorReturnCode();

	protected abstract ErrorEnum getOtpCommonErrorEnum();

	protected abstract TResult getResultBean( String returnCode );

	protected abstract String getSuffixUrl();

	@Override
	protected String getSystemName()
	{
		return SYSTEM_NAME;
	}

	@Override
	protected TResult transRes2Result( TRes resBean, HttpHeaders httpHeaders )
	{
		String returnCode = resBean.getReturnCode();

		if( !ArrayUtils.contains( getNotCaseErrorReturnCode(), returnCode ) )
			throw new MyRuntimeException( getOtpCommonErrorEnum(), new String[]{ returnCode, resBean.getReturnDesc() } );

		return getResultBean( returnCode );
	}

}
