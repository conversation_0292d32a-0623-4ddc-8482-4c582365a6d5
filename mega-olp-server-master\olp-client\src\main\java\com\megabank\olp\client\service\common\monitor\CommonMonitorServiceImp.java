package com.megabank.olp.client.service.common.monitor;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.layer.BaseService;
import com.megabank.olp.base.threadlocal.RequestInfoThreadLocal;
import com.megabank.olp.client.sender.monitor.exception.MonitorExceptionClient;
import com.megabank.olp.client.sender.monitor.exception.bean.MonitorExceptionArgBean;

@Service
@Transactional
@Profile( { "sit", "stress", "uat", "prod" } )
public class CommonMonitorServiceImp extends BaseService implements CommonMonitorService
{
	@Autowired
	private MonitorExceptionClient exceptionClient;
	
	@Autowired
	private RequestInfoThreadLocal requestInfoThreadLocal;

	public static final int MONITOR_ERROR_MSG_LENGTH = 999;

	@Override
	public void sendByException( String errorCode, String errorMsg, Exception ex, Long tranLogId, Long errorLogId )
	{
		MonitorExceptionArgBean argBean = new MonitorExceptionArgBean();

		argBean.setErrorMsg( getMonitorErrorMsg( errorCode, errorMsg, ex, tranLogId ) );
		argBean.setOccurService( requestInfoThreadLocal.get().getLastRequestUrl() );
		argBean.setErrorLogId( errorLogId );

		exceptionClient.send( argBean );
	}

	private String getMonitorErrorMsg( String errorCode, String errorMsg, Exception ex, Long tranLogId ){
		String monitorErrorMsg = String.format( "errorCode:%s, errroMsg:%s, tranLogId:%s, exception:%s, exceptionStackTrace:%s", errorCode, errorMsg, tranLogId, ex.getClass().getName(), ExceptionUtils.getStackTrace( ex ) );
		if ( monitorErrorMsg.length() <= MONITOR_ERROR_MSG_LENGTH ){
			return monitorErrorMsg;
		} else {
			return monitorErrorMsg.substring( 0, MONITOR_ERROR_MSG_LENGTH );
		}
	}
}
