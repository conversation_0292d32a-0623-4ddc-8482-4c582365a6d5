package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeTransmissionStatus;

import org.springframework.stereotype.Repository;

/**
 * The CodeTransmissionStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeTransmissionStatusDAO extends BasePojoDAO<CodeTransmissionStatus, String>
{
	@Override
	protected Class<CodeTransmissionStatus> getPojoClass()
	{
		return CodeTransmissionStatus.class;
	}
}
