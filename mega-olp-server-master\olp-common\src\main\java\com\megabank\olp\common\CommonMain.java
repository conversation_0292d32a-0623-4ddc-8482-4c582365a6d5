package com.megabank.olp.common;

import java.util.TimeZone;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.Import;

import com.megabank.olp.base.BaseMain;
import com.megabank.olp.base.config.BaseMicroWebConfig;
import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.system.config.BaseSecurityConfig;
import com.megabank.olp.system.config.SystemConfig;

@EnableAutoConfiguration( exclude = { HibernateJpaAutoConfiguration.class } )
@Import( { CommonConfig.class, BaseMicroWebConfig.class, BaseSecurityConfig.class, SystemConfig.class } )
public class CommonMain extends BaseMain
{
	public static void main( String[] args )
	{
		TimeZone.setDefault( TimeZone.getTimeZone( "Asia/Taipei" ) );

		SpringApplication.run( CommonMain.class, args );
	}

	@Override
	protected String[] getOtherKyes()
	{
		return env.getProperty( "print.properties.common" ).split( "," );
	}
}
