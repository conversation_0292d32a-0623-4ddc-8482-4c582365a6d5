package com.megabank.olp.client.sender.micro.apply.management.houseloan.bean;

import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanTransmissionUpdatedArgBean extends BaseBean
{
	private String employeeId;

	private String employeeName;

	private Long id;

	private String action;

	public HouseLoanTransmissionUpdatedArgBean()
	{
		// default constructor
	}

	public String getAction()
	{
		return action;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public Long getId()
	{
		return id;
	}

	public void setAction( String action )
	{
		this.action = action;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setId( Long id )
	{
		this.id = id;
	}

}
