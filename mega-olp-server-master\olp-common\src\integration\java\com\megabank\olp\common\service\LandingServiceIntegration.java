package com.megabank.olp.common.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.common.service.bean.landing.BaseColumnResBean;
import com.megabank.olp.common.service.bean.landing.RiskInfoResBean;

@SpringBootTest
@ContextConfiguration( classes = CommonConfig.class )
public class LandingServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private LandingService service;

	@Test
	public void getColumnList()
	{
		String templateName = "question-answer";

		List<List<BaseColumnResBean>> result = service.getColumnList( templateName );

		logger.info( "result:{}", result );
	}

	@Test
	public void getRiskInfo()
	{
		String riskLevel = "low";

		RiskInfoResBean resBean = service.getRiskInfo( riskLevel );

		logger.info( "resBean:{}", resBean );
	}

}
