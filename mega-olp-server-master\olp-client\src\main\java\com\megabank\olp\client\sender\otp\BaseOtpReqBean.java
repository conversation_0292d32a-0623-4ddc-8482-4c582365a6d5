package com.megabank.olp.client.sender.otp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class BaseOtpReqBean extends BaseBean
{
	@JsonProperty( "PhoneNumber" )
	private String mobileNumber;

	@JsonProperty( "CountryCode" )
	private final String countryCode = "886";

	@JsonProperty( "Channel" )
	private final String channel = "OPL";

	@JsonProperty( "SignData" )
	private final String signData = "mySignData";

	public BaseOtpReqBean()
	{}

	public String getChannel()
	{
		return channel;
	}

	public String getCountryCode()
	{
		return countryCode;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getSignData()
	{
		return signData;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

}
