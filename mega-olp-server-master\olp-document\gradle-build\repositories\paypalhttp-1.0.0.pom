<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.paypal</groupId>
  <artifactId>paypalhttp</artifactId>
  <version>1.0.0</version>
  <name>paypalhttp</name>
  <description>This is Paypal's generic http library for generated SDKs</description>
  <url>https://github.com/paypal/paypalhttp_java.git</url>
  <licenses>
    <license>
      <name>MIT</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>devs</id>
      <name>PayPal</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/paypal/paypalhttp_java.git</connection>
    <url>https://github.com/paypal/paypalhttp_java.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.paypal</groupId>
      <artifactId>paypalhttp-testutils</artifactId>
      <version>1.0.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
