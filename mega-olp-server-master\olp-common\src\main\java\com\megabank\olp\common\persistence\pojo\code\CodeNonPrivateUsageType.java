package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeNonPrivateUsageType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_non_private_usage_type" )
public class CodeNonPrivateUsageType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_non_private_usage_type";

	public static final String NON_PRIVATE_USAGE_TYPE_CONSTANT = "nonPrivateUsageType";

	public static final String NAME_CONSTANT = "name";

	private String nonPrivateUsageType;

	private String name;

	public CodeNonPrivateUsageType()
	{}

	public CodeNonPrivateUsageType( String nonPrivateUsageType )
	{
		this.nonPrivateUsageType = nonPrivateUsageType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "non_private_usage_type", unique = true, nullable = false, length = 20 )
	public String getNonPrivateUsageType()
	{
		return nonPrivateUsageType;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNonPrivateUsageType( String nonPrivateUsageType )
	{
		this.nonPrivateUsageType = nonPrivateUsageType;
	}
}