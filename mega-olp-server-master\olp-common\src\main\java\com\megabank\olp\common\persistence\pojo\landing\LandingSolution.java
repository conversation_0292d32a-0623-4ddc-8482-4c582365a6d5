package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The LandingSolution is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_solution" )
public class LandingSolution extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_solution";

	public static final String SOLUTION_ID_CONSTANT = "solutionId";

	public static final String LANDING_TEMPLATE_CONSTANT = "landingTemplate";

	public static final String NAME_CONSTANT = "name";

	public static final String SOLUTION_VERSION_CONSTANT = "solutionVersion";

	public static final String APPLY_DATE_CONSTANT = "applyDate";

	public static final String DELETED_CONSTANT = "deleted";

	public static final String UPDATED_BY_EMPLOYEE_NAME_CONSTANT = "updatedByEmployeeName";

	public static final String UPDATED_BY_EMPLOYEE_ID_CONSTANT = "updatedByEmployeeId";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_BY_EMPLOYEE_NAME_CONSTANT = "createdByEmployeeName";

	public static final String CREATED_BY_EMPLOYEE_ID_CONSTANT = "createdByEmployeeId";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	public static final String LANDING_SOLUTION_IMAGES_CONSTANT = "landingSolutionImages";

	public static final String LANDING_SOLUTION_VALUES_CONSTANT = "landingSolutionValues";

	private Long solutionId;

	private transient LandingTemplate landingTemplate;

	private String name;

	private int solutionVersion;

	private Date applyDate;

	private boolean deleted;

	private String updatedByEmployeeName;

	private String updatedByEmployeeId;

	private Date updatedDate;

	private String createdByEmployeeName;

	private String createdByEmployeeId;

	private Date createdDate;

	private transient Set<LandingSolutionImage> landingSolutionImages = new HashSet<>( 0 );

	private transient Set<LandingSolutionValue> landingSolutionValues = new HashSet<>( 0 );

	public LandingSolution()
	{}

	public LandingSolution( LandingTemplate landingTemplate, String name, int solutionVersion, boolean deleted, String updatedByEmployeeName,
							String updatedByEmployeeId, Date updatedDate, String createdByEmployeeName, String createdByEmployeeId, Date createdDate )
	{
		this.landingTemplate = landingTemplate;
		this.name = name;
		this.solutionVersion = solutionVersion;
		this.deleted = deleted;
		this.updatedByEmployeeName = updatedByEmployeeName;
		this.updatedByEmployeeId = updatedByEmployeeId;
		this.updatedDate = updatedDate;
		this.createdByEmployeeName = createdByEmployeeName;
		this.createdByEmployeeId = createdByEmployeeId;
		this.createdDate = createdDate;
	}

	public LandingSolution( Long solutionId )
	{
		this.solutionId = solutionId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "apply_date", length = 10 )
	public Date getApplyDate()
	{
		return applyDate;
	}

	@Column( name = "created_by_employee_id", nullable = false, length = 30 )
	public String getCreatedByEmployeeId()
	{
		return createdByEmployeeId;
	}

	@Column( name = "created_by_employee_name", nullable = false )
	public String getCreatedByEmployeeName()
	{
		return createdByEmployeeName;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingSolution" )
	public Set<LandingSolutionImage> getLandingSolutionImages()
	{
		return landingSolutionImages;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingSolution" )
	public Set<LandingSolutionValue> getLandingSolutionValues()
	{
		return landingSolutionValues;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "template_id", nullable = false )
	public LandingTemplate getLandingTemplate()
	{
		return landingTemplate;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "solution_id", unique = true, nullable = false )
	public Long getSolutionId()
	{
		return solutionId;
	}

	@Column( name = "solution_version", nullable = false, precision = 5, scale = 0 )
	public int getSolutionVersion()
	{
		return solutionVersion;
	}

	@Column( name = "updated_by_employee_id", nullable = false, length = 30 )
	public String getUpdatedByEmployeeId()
	{
		return updatedByEmployeeId;
	}

	@Column( name = "updated_by_employee_name", nullable = false )
	public String getUpdatedByEmployeeName()
	{
		return updatedByEmployeeName;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "updated_date", nullable = false, length = 23 )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "deleted", nullable = false, precision = 1, scale = 0 )
	public boolean isDeleted()
	{
		return deleted;
	}

	public void setApplyDate( Date applyDate )
	{
		this.applyDate = applyDate;
	}

	public void setCreatedByEmployeeId( String createdByEmployeeId )
	{
		this.createdByEmployeeId = createdByEmployeeId;
	}

	public void setCreatedByEmployeeName( String createdByEmployeeName )
	{
		this.createdByEmployeeName = createdByEmployeeName;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setDeleted( boolean deleted )
	{
		this.deleted = deleted;
	}

	public void setLandingSolutionImages( Set<LandingSolutionImage> landingSolutionImages )
	{
		this.landingSolutionImages = landingSolutionImages;
	}

	public void setLandingSolutionValues( Set<LandingSolutionValue> landingSolutionValues )
	{
		this.landingSolutionValues = landingSolutionValues;
	}

	public void setLandingTemplate( LandingTemplate landingTemplate )
	{
		this.landingTemplate = landingTemplate;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSolutionId( Long solutionId )
	{
		this.solutionId = solutionId;
	}

	public void setSolutionVersion( int solutionVersion )
	{
		this.solutionVersion = solutionVersion;
	}

	public void setUpdatedByEmployeeId( String updatedByEmployeeId )
	{
		this.updatedByEmployeeId = updatedByEmployeeId;
	}

	public void setUpdatedByEmployeeName( String updatedByEmployeeName )
	{
		this.updatedByEmployeeName = updatedByEmployeeName;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}
}