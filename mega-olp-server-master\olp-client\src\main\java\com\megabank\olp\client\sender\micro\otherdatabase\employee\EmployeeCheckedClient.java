package com.megabank.olp.client.sender.micro.otherdatabase.employee;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.employee.bean.EmployeeCheckedArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class EmployeeCheckedClient extends BaseOtherDatabaseClient<EmployeeCheckedArgBean, Boolean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/nbots/employee/checkIsEmployee";
	}

}
