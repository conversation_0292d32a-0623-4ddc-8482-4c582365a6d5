package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyDetailResultBean extends BaseBean
{
	@JsonProperty( "surveyInfo" )
	private LoanSurveyInfoBean loanSurveyInfoBean;

	@JsonProperty( "contactInfo" )
	private LoanSurveyContactBean loanSurveyContactBean;

	public LoanSurveyDetailResultBean()
	{
		// default constructor
	}

	public LoanSurveyContactBean getLoanSurveyContactBean()
	{
		return loanSurveyContactBean;
	}

	public LoanSurveyInfoBean getLoanSurveyInfoBean()
	{
		return loanSurveyInfoBean;
	}

	public void setLoanSurveyContactBean( LoanSurveyContactBean loanSurveyContactBean )
	{
		this.loanSurveyContactBean = loanSurveyContactBean;
	}

	public void setLoanSurveyInfoBean( LoanSurveyInfoBean loanSurveyInfoBean )
	{
		this.loanSurveyInfoBean = loanSurveyInfoBean;
	}

}