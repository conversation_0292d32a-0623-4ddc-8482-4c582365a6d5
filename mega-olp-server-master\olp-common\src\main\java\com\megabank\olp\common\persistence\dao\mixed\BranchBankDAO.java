package com.megabank.olp.common.persistence.dao.mixed;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BaseDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeBranchBank;
import com.megabank.olp.common.persistence.pojo.code.CodeCity;
import com.megabank.olp.common.persistence.pojo.code.CodeTown;

@Repository
public class BranchBankDAO extends BaseDAO
{

	private static final String LOAN_TYPE_CONSTANT = "loanType";

	private static final String CITY_CODE_CONSTANT = "cityCode";

	private static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";
	
	private static final String BANK_CODE = "bankCode";

	private static final String HEAD_OFFICE_CONSTANT = "headOffice";

	private static final String TOWN_CODE_CONSTANT = "townCode";

	private static final String ALLOWED_USER_CONSTANT = "allowedUser";
	
	private static final String EMP_ID_CONSTANT = "empId";

	public List<CodeCity> getBranchBankCity( String loanType, List<Integer> allowedUser )
	{
		Validate.notEmpty( allowedUser );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getCodeCity" );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameterList( ALLOWED_USER_CONSTANT, allowedUser, Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeCity.class );

		return nativeQuery.getResultList();
	}

	public List<CodeTown> getBranchBankTownByCity( String loanType, String cityCode, List<Integer> allowedUser )
	{
		Validate.notBlank( cityCode );
		Validate.notEmpty( allowedUser );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getCodeTownByCity" );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( CITY_CODE_CONSTANT, cityCode, String.class );
		nativeQuery.setParameterList( ALLOWED_USER_CONSTANT, allowedUser, Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeTown.class );

		return nativeQuery.getResultList();
	}

	public List<CodeBranchBank> getList( String loanType )
	{
		return getList( loanType, null, false );
	}

	public List<CodeBranchBank> getList( String loanType, Long branchBankId, Boolean headOffice )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getList" );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( BRANCH_BANK_ID_CONSTANT, branchBankId, Long.class );
		nativeQuery.setParameter( HEAD_OFFICE_CONSTANT, headOffice, Boolean.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeBranchBank.class );

		return nativeQuery.getResultList();
	}

	public List<CodeBranchBank> getListByTownCode( String loanType, String townCode, List<Integer> allowedUser )
	{
		Validate.notEmpty( allowedUser );

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getListByTownCode" );
		nativeQuery.setParameter( LOAN_TYPE_CONSTANT, loanType, String.class );
		nativeQuery.setParameter( TOWN_CODE_CONSTANT, townCode, String.class );
		nativeQuery.setParameterList( ALLOWED_USER_CONSTANT, allowedUser, Integer.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeBranchBank.class );

		return nativeQuery.getResultList();
	}
	
	public List<CodeBranchBank> getBranchBankByEmpId( String empId )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "branchbank.getBranchBankByEmpId" );
		nativeQuery.setParameter( EMP_ID_CONSTANT, empId, String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeBranchBank.class );

		return nativeQuery.getResultList();
	}
}
