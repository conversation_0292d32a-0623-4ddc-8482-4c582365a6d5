package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeList;

/**
 * The CodeListDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeListDAO extends BasePojoDAO<CodeList, Long>
{

	public List<CodeList> getPojosByCodeType( String codeType )
	{

		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeList.CODE_TYPE_CONSTANT, codeType ),
														  new NameValueBean( CodeList.CODE_DISABLED_CONSTANT, false ) };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeList.CODE_DISPLAY_ORDER_CONSTANT ) };

		return this.getPojosByPropertiesOrderBy( conditions, orderBeans );
	}

	@Override
	protected Class<CodeList> getPojoClass()
	{
		return CodeList.class;
	}
}
