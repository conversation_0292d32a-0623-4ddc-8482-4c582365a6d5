print:
  properties:
    common: service.url.signing
service:
  url:
    signing: true
    mydata: true
    ixml: true
serviceName: common
---
logging:
  config: classpath:log4j2/log4j2-dev.xml
server:
  port: 9030
spring:
  config:
    activate:
      on-profile: dev
---
logging:
  config: classpath:log4j2/log4j2-sit.xml
spring:
  config:
    activate:
      on-profile: sit
---
logging:
  config: classpath:log4j2/log4j2-uat.xml
spring:
  config:
    activate:
      on-profile: uat
---
logging:
  config: classpath:log4j2/log4j2-prod.xml
spring:
  config:
    activate:
      on-profile: prod
---
logging:
  config: classpath:log4j2/log4j2-stress.xml
spring:
  config:
    activate:
      on-profile: stress