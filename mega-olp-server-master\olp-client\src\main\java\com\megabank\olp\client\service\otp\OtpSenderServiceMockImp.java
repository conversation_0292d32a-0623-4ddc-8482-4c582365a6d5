package com.megabank.olp.client.service.otp;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedResultBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev", "sit", "stress" } )
public class OtpSenderServiceMockImp implements OtpSenderService
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public OtpGettedResultBean getOtp( String mobileNumber )
	{
		logger.info( "mocked otp getted success." );

		return new OtpGettedResultBean();
	}

	@Override
	public OtpVerifiedResultBean verifyOtp( String mobileNumber, String captcha )
	{
		logger.info( "mocked otp verified success." );

		OtpVerifiedResultBean resultBean = new OtpVerifiedResultBean();
		resultBean.setVerifiedResult( true );

		return resultBean;
	}

}
