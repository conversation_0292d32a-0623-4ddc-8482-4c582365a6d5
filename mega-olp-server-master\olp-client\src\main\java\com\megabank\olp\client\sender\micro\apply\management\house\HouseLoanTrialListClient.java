package com.megabank.olp.client.sender.micro.apply.management.house;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialListedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialListedResultBean;

@Component
public class HouseLoanTrialListClient extends BaseApplyClient<HouseLoanTrialListedArgBean, HouseLoanTrialListedResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/houseloantrial/list";
	}
}
