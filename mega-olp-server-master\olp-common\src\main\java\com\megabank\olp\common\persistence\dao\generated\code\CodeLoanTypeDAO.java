package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeLoanType;

/**
 * The CodeLoanTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanTypeDAO extends BasePojoDAO<CodeLoanType, String>
{
	public List<CodeLoanType> getAllPojosOrderByDisplay()
	{
		OrderBean orderBean = new OrderBean( CodeLoanType.DISPLAY_ORDER_CONSTANT );

		return this.getAllPojosOrderBy( orderBean );
	}

	public CodeLoanType read( String loanType )
	{
		Validate.notBlank( loanType );

		return getPojoByPK( loanType, CodeLoanType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeLoanType> getPojoClass()
	{
		return CodeLoanType.class;
	}
}
