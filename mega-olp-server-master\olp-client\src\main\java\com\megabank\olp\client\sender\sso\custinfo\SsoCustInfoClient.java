/**
 *
 */
package com.megabank.olp.client.sender.sso.custinfo;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.sso.BaseSsoClient;
import com.megabank.olp.client.sender.sso.custinfo.bean.SsoCustInfoArgBean;
import com.megabank.olp.client.sender.sso.custinfo.bean.SsoCustInfoReqBean;
import com.megabank.olp.client.sender.sso.custinfo.bean.SsoCustInfoResBean;
import com.megabank.olp.client.sender.sso.custinfo.bean.SsoCustInfoResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class SsoCustInfoClient extends BaseSsoClient<SsoCustInfoArgBean, SsoCustInfoReqBean, SsoCustInfoResBean, SsoCustInfoResultBean>
{
	private Date transRocString2Date( String value )
	{
		Date rocDate = CommonDateStringUtils.transString2Date( value, "TTTMMdd" );
		Calendar rocCalendar = DateUtils.toCalendar( rocDate );

		return rocCalendar.getTime();
	}

	@Override
	protected void doReqHeaders( HttpHeaders httpHeaders, SsoCustInfoArgBean argBean )
	{
		httpHeaders.add( HttpHeaders.AUTHORIZATION, "Bearer " + argBean.getAccessToken() );
		httpHeaders.add( "x-auth-token", argBean.getXAuthToken() );
	}

	@Override
	protected String getSimulatorCode( SsoCustInfoArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/robotAdapter/api/json";
	}

	@Override
	protected SsoCustInfoReqBean transArg2Req( SsoCustInfoArgBean argBean )
	{
		String timestamp = Long.toString( new Date().getTime() );

		SsoCustInfoReqBean reqBean = new SsoCustInfoReqBean();
		reqBean.setTrackingIxd( argBean.getTrackingIxd() );
		reqBean.setResource( "/fco/fco00001/custinfo" );
		reqBean.setPageId( "robot" );
		reqBean.setPageNo( "custinfo" );
		reqBean.setDeviceIxd( "none" );
		reqBean.setFromSys( "1" );
		reqBean.setClientNo( timestamp );
		reqBean.setClientTime( timestamp );

		return reqBean;
	}

	@Override
	protected SsoCustInfoResultBean transRes2Result( SsoCustInfoResBean resBean, HttpHeaders httpHeaders )
	{
		SsoCustInfoResultBean resultBean = new SsoCustInfoResultBean();
		resultBean.setSys( resBean.getSys() );
		resultBean.setResource( resBean.getResource() );
		resultBean.setCode( resBean.getReturnCode() );
		resultBean.setDesc( resBean.getReturnDesc() );
		resultBean.setClientTime( resBean.getResponseDate() );

		if( resBean.getRespDataBean() != null )
		{
			resultBean.setIdNo( resBean.getRespDataBean().getIdNo() );
			resultBean.setBirthDate( transRocString2Date( resBean.getRespDataBean().getBirthDate() ) );
			resultBean.setMobileNumber( resBean.getRespDataBean().getMobileNumber() );
		}

		return resultBean;
	}
}
