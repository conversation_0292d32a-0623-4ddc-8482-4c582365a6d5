<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>
  <groupId>com.google.api-client</groupId>
  <artifactId>google-api-client-parent</artifactId>
  <version>1.24.1</version>
  <packaging>pom</packaging>
  <name>Parent for the Google API Client Library for Java</name>

  <url>https://github.com/google/google-api-java-client</url>

  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/google/google-api-java-client/issues</url>
  </issueManagement>

  <inceptionYear>2010</inceptionYear>

  <prerequisites>
    <maven>2.0.9</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://github.com/google/google-api-java-client.git</connection>
    <developerConnection>scm:git:ssh:**************:google/google-api-java-client.git</developerConnection>
    <url>https://github.com/google/google-api-java-client</url>
  </scm>

  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>yanivi</id>
      <name>Yaniv Inbar</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <organizationUrl>http://www.google.com</organizationUrl>
      <roles>
        <role>owner</role>
        <role>developer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
  </developers>

  <!--
    If you add a new module, make sure to also add it in the following places:
    * below in the dependencyManagement and maven-javadoc-plugin sections
    * google-api-client-assembly/classpath-include
    * google-api-client-assembly/pom.xml
    * google-api-client-assembly/readme.html
    * google-api-client-assembly/dependencies/<name>-dependencies.html
        (use mvn project-info-reports:dependencies and copy from
        google-api-client-<name>/target/site/dependencies.html)
    * google-api-client-assembly/android-properties/*.properties
  -->
  <modules>
    <module>google-api-client</module>
    <module>google-api-client-servlet</module>
    <module>google-api-client-android</module>
    <module>google-api-client-appengine</module>
    <module>google-api-client-assembly</module>
    <module>google-api-client-gson</module>
    <module>google-api-client-jackson2</module>
    <module>google-api-client-java6</module>
    <module>google-api-client-protobuf</module>
    <module>google-api-client-xml</module>
  </modules>

  <pluginRepositories>
    <pluginRepository>
      <releases>
        <updatePolicy>never</updatePolicy>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
    <pluginRepository>
      <id>protoc-plugin</id>
      <url>https://dl.bintray.com/sergei-ivanov/maven/</url>
    </pluginRepository>
  </pluginRepositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-tools-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-testing</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-labs</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-stubs</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>xpp3</groupId>
        <artifactId>xpp3</artifactId>
        <version>${project.xpp3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${project.httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.jdo</groupId>
        <artifactId>jdo2-api</artifactId>
        <version>${project.jdo2-api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>2.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-assembly</artifactId>
        <version>${project.http.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-android</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-appengine</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-gson</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-jackson2</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-protobuf</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-jackson</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-xml</artifactId>
        <version>${project.http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client</artifactId>
        <version>${project.oauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-assembly</artifactId>
        <version>${project.oauth.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-appengine</artifactId>
        <version>${project.oauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-java6</artifactId>
        <version>${project.oauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-java7</artifactId>
        <version>${project.oauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-servlet</artifactId>
        <version>${project.oauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-appengine</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-servlet</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-android</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-java6</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-gson</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-jackson2</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-protobuf</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-xml</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- See README.md about installing this jar -->
      <dependency>
        <groupId>com.google.android.google-play-services</groupId>
        <artifactId>google-play-services</artifactId>
        <version>1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <!--This plugin configuration is present to override the configuration specified in the
          parent's pom.xml.
          This is a workaround for known bug: https://issues.sonatype.org/browse/CENTRALSRV-35 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <useReleaseProfile>true</useReleaseProfile>
            <arguments>-Psonatype-oss-release ${arguments}</arguments>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.2</version>
          <configuration>
            <source>1.5</source>
            <target>1.5</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <artifactId>maven-ear-plugin</artifactId>
          <version>2.4.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-ejb-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.1</version>
          <configuration>
            <archive>
              <addMavenDescriptor>false</addMavenDescriptor>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-rar-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.4.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>2.1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.12.4</version>
          <configuration>
            <argLine>-Xmx1024m</argLine>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-war-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.9</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>2.7</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>aggregate</id>
            <goals>
              <goal>aggregate</goal>
            </goals>
            <phase>site</phase>
            <configuration>
              <links>
                <link>http://download.oracle.com/javase/6/docs/api/</link>
                <link>http://cloud.google.com/appengine/docs/java/javadoc</link>
                <link>https://developers.google.com/api-client-library/java/google-http-java-client/reference/${project.http.version}</link>
                <link>https://developers.google.com/api-client-library/java/google-http-java-client/reference/${project.oauth.version}</link>
              </links>
              <doctitle>Google API Client Library for Java ${project.version}</doctitle>
              <overview>${basedir}/overview.html</overview>
              <groups>
                <group>
                  <title>google-api-client</title>
                  <packages>com.google.api.client.googleapis*</packages>
                </group>
                <group>
                  <title>google-api-client-android</title>
                  <packages>com.google.api.client.googleapis.extensions.android.*</packages>
                </group>
                <group>
                  <title>google-api-client-appengine</title>
                  <packages>com.google.api.client.googleapis.extensions.appengine*</packages>
                </group>
                <group>
                  <title>google-api-client-gson</title>
                  <packages>com.google.api.client.googleapis.notifications.json.gson.*</packages>
                </group>
                <group>
                  <title>google-api-client-jackson2</title>
                  <packages>com.google.api.client.googleapis.notifications.json.jackson2.*</packages>
                </group>
                <group>
                  <title>google-api-client-java6</title>
                  <packages>com.google.api.client.googleapis.extensions.java6.*</packages>
                </group>
                <group>
                  <title>google-api-client-protobuf</title>
                  <packages>com.google.api.client.googleapis.services.protobuf*:com.google.api.client.googleapis.testing.services.protobuf*</packages>
                </group>
                <group>
                  <title>google-api-client-servlet</title>
                  <packages>com.google.api.client.googleapis.extensions.servlet*</packages>
                </group>
                <group>
                  <title>google-api-client-xml</title>
                  <packages>com.google.api.client.googleapis.xml.*</packages>
                </group>
              </groups>
              <windowtitle>google-api-java-client ${project.version}</windowtitle>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <configLocation>checkstyle.xml</configLocation>
          <consoleOutput>true</consoleOutput>
          <suppressionsLocation>${basedir}/../checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>${basedir}/../findbugs-exclude.xml</excludeFilterFile>
          <plugins>
            <plugin>
              <groupId>com.google.http-client</groupId>
              <artifactId>google-http-client-findbugs</artifactId>
              <version>${project.http.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <comparisonVersion>1.23.0</comparisonVersion>
          <ignoredDifferencesFile>${basedir}/../clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java15</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <properties>
    <!--
      NOTE: if you make a change to these versions, you MUST make the same change to:
      - google-api-client-assembly/android-properties (make the filenames match the version here)
      - google-oauth-java-client/pom.xml (versions must match)
      - google-oauth-java-client/google-oauth-client-assembly/android-properties (make the filenames match the version here)
      - google-http-java-client/pom.xml (versions must match)
      - google-http-java-client/google-http-client-assembly/android-properties (make the filenames match the version here)
      - Internally, update the default features.json file
    -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.http.version>1.24.1</project.http.version>
    <project.oauth.version>1.24.1</project.oauth.version>
    <project.jsr305.version>3.0.2</project.jsr305.version>
    <project.gson.version>2.1</project.gson.version>
    <project.jackson-core-asl.version>1.9.11</project.jackson-core-asl.version>
    <project.jackson-core2.version>2.9.2</project.jackson-core2.version>
    <project.protobuf-java.version>2.6.1</project.protobuf-java.version>
    <project.guava.version>20.0</project.guava.version>
    <project.appengine.version>1.7.7</project.appengine.version>
    <project.xpp3.version>1.1.4c</project.xpp3.version>
    <project.commons-logging.version>1.1.1</project.commons-logging.version>
    <project.httpclient.version>4.5.3</project.httpclient.version>
    <project.httpcore.version>4.0.1</project.httpcore.version>
    <project.jdo2-api.version>2.3-eb</project.jdo2-api.version>
    <project.transaction-api.version>1.1</project.transaction-api.version>
  </properties>
</project>
