package com.megabank.olp.client.service.otp;

import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedResultBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public interface OtpSenderService
{
	public OtpGettedResultBean getOtp( String mobileNumber );

	public OtpVerifiedResultBean verifyOtp( String mobileNumber, String captcha );
}
