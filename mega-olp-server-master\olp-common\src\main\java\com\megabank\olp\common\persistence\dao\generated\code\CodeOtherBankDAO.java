package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeOtherBank;

/**
 * The CodeOtherBankDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeOtherBankDAO extends BasePojoDAO<CodeOtherBank, String>
{
	public void create( String code, String name )
	{
		Validate.notNull( code );
		Validate.notNull( name );

		CodeOtherBank pojo = new CodeOtherBank();
		pojo.setOtherBankCode( code );
		pojo.setName( name );

		super.createPojo( pojo );
	}

	@Override
	public int deleteAllPojos()
	{
		List<CodeOtherBank> pojos = getAllPojos();

		return super.deletePojos( pojos );
	}

	public List<CodeOtherBank> getPojosByDisable( boolean disabled )
	{
		NameValueBean condition = new NameValueBean( "disabled", disabled );

		return getPojosByProperty( condition );
	}

	public CodeOtherBank read( String code )
	{
		Validate.notBlank( code );

		return getPojoByPK( code );
	}

	public String updateBankDisable( String code )
	{
		Validate.notNull( code );

		CodeOtherBank pojo = read( code );
		pojo.setDisabled( true );

		return pojo.getOtherBankCode();
	}

	@Override
	protected Class<CodeOtherBank> getPojoClass()
	{
		return CodeOtherBank.class;
	}
}
