package com.megabank.olp.common.service.bean.landing;

import java.util.Collections;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class TextGroupBean extends BaseBean
{
	private String name;

	private List<String> elements = Collections.emptyList();

	public TextGroupBean()
	{
		// default constructor
	}

	public List<String> getElements()
	{
		return elements;
	}

	public String getName()
	{
		return name;
	}

	public void setElements( List<String> elements )
	{
		this.elements = elements;
	}

	public void setName( String name )
	{
		this.name = name;
	}

}
