package com.megabank.olp.client.utility.enums;

import com.megabank.olp.base.enums.ErrorEnum;

public enum ClientMyDataErrorEnum implements ErrorEnum
{
	GETTED_TXID_COMMON( "01001" ),

	GETTED_MY_DATA_COMMON( "01002" ),

	REPORTED_MY_DATA_COMMON( "01003" ),

	WEBCOMM_MY_DATA_TYPE1( "01004" );

	private String errorCode;

	private ClientMyDataErrorEnum( String errorCode )
	{
		this.errorCode = errorCode;
	}

	@Override
	public String getCode()
	{
		return "CLIENT-MY-DATA" + errorCode;
	}
}
