package com.megabank.olp.client.sender;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.megabank.olp.client.sender.micro.BaseScheduleClient;
import jakarta.xml.bind.JAXBException;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.util.Collection;

public abstract class BaseScheduleJsonClient<TArg, TReq, TRes, TResult> extends BaseScheduleClient<TArg, TReq, TRes, TResult>
{
	private final Class resClass;

	@Autowired
	@Qualifier( "clientObjectMapper" )
	protected ObjectMapper mapper;

	public BaseScheduleJsonClient()
	{
		resClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseScheduleJsonClient.class )[ 2 ];
	}

	protected Class<?> getBeanClassByCollection()
	{
		throw new NotImplementedException( "you need to implement the getBeanClassByCollection() when response is collection" );
	}

	@Override
	protected String getContentType()
	{
		return MediaType.APPLICATION_JSON_VALUE;
	}

	@Override
	protected String transReq2ReqBody( TReq reqBean ) throws IOException, JAXBException
	{
		return mapper.writeValueAsString( reqBean );
	}

	@Override
	protected TRes transResBody2Res( String resBody ) throws IOException
	{
		if( Collection.class.isAssignableFrom( resClass ) )
			return mapper.readValue( resBody, mapper.getTypeFactory().constructCollectionType( resClass, getBeanClassByCollection() ) );

		return ( TRes )mapper.readValue( resBody, resClass );
	}

}
