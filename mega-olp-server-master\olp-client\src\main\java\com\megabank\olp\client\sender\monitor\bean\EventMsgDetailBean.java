package com.megabank.olp.client.sender.monitor.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "parameters", "contentLength", "content" } )
public class EventMsgDetailBean extends BaseBean
{
	@XmlElement( name = "PARAMETERS" )
	private String parameters;

	@XmlElement( name = "CONTENT_LENGTH" )
	private Integer contentLength;

	@XmlElement( name = "CONTENT" )
	private String content;

	public EventMsgDetailBean()
	{}

	public String getContent()
	{
		return content;
	}

	public Integer getContentLength()
	{
		return contentLength;
	}

	public String getParameters()
	{
		return parameters;
	}

	public void setContent( String content )
	{
		this.content = content;
	}

	public void setContentLength( Integer contentLength )
	{
		this.contentLength = contentLength;
	}

	public void setParameters( String parameters )
	{
		this.parameters = parameters;
	}
}
