/**
 *
 */
package com.megabank.olp.client.sender.micro.apply.mydata.bean;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class MyDataTaskExecutedArgBean extends BaseBean
{

	private String txId;

	public MyDataTaskExecutedArgBean()
	{
		// default constructor
	}

	public String getTxId()
	{
		return txId;
	}

	public void setTxId( String txId )
	{
		this.txId = txId;
	}

}
