package com.megabank.olp.common.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeTemplateType;

/**
 * The CodeTemplateTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeTemplateTypeDAO extends BasePojoDAO<CodeTemplateType, String>
{

	public CodeTemplateType read( String templateType )
	{
		Validate.notBlank( templateType );

		return getPojoByPK( templateType, CodeTemplateType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeTemplateType> getPojoClass()
	{
		return CodeTemplateType.class;
	}
}
