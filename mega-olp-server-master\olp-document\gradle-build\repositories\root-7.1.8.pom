<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.itextpdf</groupId>
  <artifactId>root</artifactId>
  <version>7.1.8</version>
  <packaging>pom</packaging>

  <name>iText 7</name>
  <description>A Free Java-PDF library</description>
  <url>https://itextpdf.com/</url>
  <inceptionYear>1998</inceptionYear>
  <organization>
    <name>iText Group NV</name>
    <url>https://itextpdf.com/</url>
  </organization>
  <licenses>
    <license>
      <name>GNU Affero General Public License v3</name>
      <url>http://www.fsf.org/licensing/licenses/agpl-3.0.html</url>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>itext</id>
      <name>iText Software</name>
      <email><EMAIL></email>
      <url>https://www.itextpdf.com</url>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>iText on StackOverflow</name>
      <subscribe>https://stackoverflow.com/questions/tagged/itext7</subscribe>
      <archive>https://stackoverflow.com/questions/tagged/itext7</archive>
      <otherArchives>
        <otherArchive>http://news.gmane.org/gmane.comp.java.lib.itext.general</otherArchive>
        <otherArchive>http://itext-general.2136553.n4.nabble.com/</otherArchive>
        <otherArchive>http://www.junlu.com/2.html</otherArchive>
        <otherArchive>http://sourceforge.net/mailarchive/forum.php?forum_id=3273</otherArchive>
        <otherArchive>http://www.mail-archive.com/itext-questions%40lists.sourceforge.net/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <modules>
    <module>barcodes</module>
    <module>font-asian</module>
    <module>forms</module>
    <module>hyph</module>
    <module>io</module>
    <module>itextcore</module>
    <module>kernel</module>
    <module>layout</module>
    <module>pdfa</module>
    <module>pdftest</module>
    <module>sign</module>
    <module>styled-xml-parser</module>
    <module>svg</module>
  </modules>

  <scm>
    <connection>scm:git:ssh://************************:7999/i7j/itextcore.git</connection>
    <url>https://git.itextsupport.com/projects/I7J/repos/itextcore</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://jira.itextsupport.com/</url>
  </issueManagement>
  <ciManagement>
    <system>jenkins-ci</system>
    <url>https://jenkins.itextsupport.com/</url>
  </ciManagement>

  <properties>
    <argLine>-Xmx1024m</argLine>
    <bouncycastle.version>1.49</bouncycastle.version>
    <checkstyle.version>2.15</checkstyle.version>
    <failsafe.version>3.0.0-M3</failsafe.version>
    <fb-contrib.version>7.4.3</fb-contrib.version>
    <findsecbugs.version>1.8.0</findsecbugs.version>
    <hsqldb.version>2.3.3</hsqldb.version>
    <integrationtests>com.itextpdf.test.annotations.type.IntegrationTest</integrationtests>
    <itext.legacy.version>5.5.13.1</itext.legacy.version>
    <jacoco.version>0.8.4</jacoco.version>
    <java.version>1.7</java.version>
    <javadoc-additionalOptions />
    <javadoc-link>https://docs.oracle.com/javase/8/docs/api/</javadoc-link>
    <javadoc.version>3.0.1</javadoc.version>
    <jfreechart.version>1.0.19</jfreechart.version>
    <junit.version>4.12</junit.version>
    <junitparams.version>1.0.4</junitparams.version>
    <logback.version>1.1.3</logback.version>
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>
    <maven.jar.version>3.1.0</maven.jar.version>
    <maven.source.version>3.0.1</maven.source.version>
    <performancetests>com.itextpdf.test.annotations.type.PerformanceTest</performancetests>
    <pitest.version>1.1.11</pitest.version>
    <pmd.version>3.11.0</pmd.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <sampletests>com.itextpdf.test.annotations.type.SampleTest</sampletests>
    <skipTests>true</skipTests>
    <slf4j.version>1.7.13</slf4j.version>
    <slowtests>com.itextpdf.test.annotations.type.SlowTest</slowtests>
    <sonar.clirr.reportPath>${project.build.directory}/clirr-report.txt</sonar.clirr.reportPath>
    <spotbugs.version>3.1.11</spotbugs.version>
    <surefire.version>3.0.0-M3</surefire.version>
    <unittests>com.itextpdf.test.annotations.type.UnitTest</unittests>
  </properties>

  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>itext-snapshot</id>
      <name>iText Repository - snapshots</name>
      <url>https://repo.itextsupport.com/snapshot</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>itext-releases</id>
      <name>iText Repository - releases</name>
      <url>https://repo.itextsupport.com/releases</url>
    </repository>
  </repositories>

  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>pl.pragmatists</groupId>
      <artifactId>JUnitParams</artifactId>
      <version>${junitparams.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>itext7-${project.artifactId}-${project.version}</finalName>
    <plugins>
      <plugin>
        <groupId>com.github.ekryd.sortpom</groupId>
        <artifactId>sortpom-maven-plugin</artifactId>
        <version>2.9.0</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>sort</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <encoding>${project.build.sourceEncoding}</encoding>
          <expandEmptyElements>false</expandEmptyElements>
          <sortDependencies>scope,groupId,artifactId</sortDependencies>
          <sortPlugins>groupId,artifactId</sortPlugins>
          <sortProperties>true</sortProperties>
          <sortModules>true</sortModules>
          <createBackupFile>false</createBackupFile>
          <lineSeparator>\n</lineSeparator>
          <keepBlankLines>true</keepBlankLines>
          <nrOfIndentSpace>2</nrOfIndentSpace>
          <skip>${skipSortPom}</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.version}</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <failOnError>false</failOnError>
          <plugins>
            <plugin>
              <groupId>com.mebigfatguy.fb-contrib</groupId>
              <artifactId>fb-contrib</artifactId>
              <version>${fb-contrib.version}</version>
            </plugin>
            <plugin>
              <groupId>com.h3xstream.findsecbugs</groupId>
              <artifactId>findsecbugs-plugin</artifactId>
              <version>${findsecbugs.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>4.2.0</version>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <unpackBundle>true</unpackBundle>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-artifact</id>
            <phase>package</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>${project.groupId}</groupId>
                  <artifactId>${project.artifactId}</artifactId>
                  <version>${project.version}</version>
                  <type>${project.packaging}</type>
                  <destFileName>itext7-${project.artifactId}-${project.version}.jar</destFileName>
                </artifactItem>
              </artifactItems>
              <outputDirectory>../target</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- Run integration tests -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${failsafe.version}</version>
        <configuration>
          <skipTests>${skipTests}</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven.jar.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${javadoc.version}</version>
        <executions>
          <execution>
            <id>site-javadoc</id>
            <phase>site</phase>
            <goals>
              <goal>aggregate</goal>
            </goals>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
          </execution>
          <execution>
            <id>attach-javadocs</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <failOnError>false</failOnError>
          <quiet>true</quiet>
          <source>7</source>
          <detectLinks>true</detectLinks>
          <additionalOptions>${javadoc-additionalOptions}</additionalOptions>
          <links>
            <link>${javadoc-link}</link>
          </links>
          <subpackages>com.itextpdf.barcodes:com.itextpdf.forms:com.itextpdf.io:com.itextpdf.kernel:com.itextpdf.layout:com.itextpdf.pdfa:com.itextpdf.signatures:com.itextpdf.styledxmlparser:com.itextpdf.svg:com.itextpdf.test</subpackages>
          <groups>
            <group>
              <title>Barcodes</title>
              <packages>com.itextpdf.barcodes*</packages>
            </group>
            <group>
              <title>Asian Fonts</title>
              <packages>com.itextpdf.font-asian*</packages>
            </group>
            <group>
              <title>Forms</title>
              <packages>com.itextpdf.forms*</packages>
            </group>
            <group>
              <title>Hyphenation</title>
              <packages>com.itextpdf.hyph*</packages>
            </group>
            <group>
              <title>Input / Output</title>
              <packages>com.itextpdf.io*</packages>
            </group>
            <group>
              <title>Kernel</title>
              <packages>com.itextpdf.kernel*</packages>
            </group>
            <group>
              <title>Layout</title>
              <packages>com.itextpdf.layout*</packages>
            </group>
            <group>
              <title>PDF/A</title>
              <packages>com.itextpdf.pdfa*</packages>
            </group>
            <group>
              <title>Signatures</title>
              <packages>com.itextpdf.signatures*</packages>
            </group>
            <group>
              <title>Styled XML Parser</title>
              <packages>com.itextpdf.styledxmlparser*</packages>
            </group>
            <group>
              <title>SVG</title>
              <packages>com.itextpdf.svg*</packages>
            </group>
          </groups>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven.source.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <excludeResources>true</excludeResources>
        </configuration>
      </plugin>
      <!-- Run unit tests -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire.version}</version>
        <configuration>
          <skipTests>${skipTests}</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>tidy-maven-plugin</artifactId>
        <version>1.0.0</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>pom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.version}</version>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <plugins>
            <plugin>
              <groupId>com.mebigfatguy.fb-contrib</groupId>
              <artifactId>fb-contrib</artifactId>
              <version>${fb-contrib.version}</version>
            </plugin>
            <plugin>
              <groupId>com.h3xstream.findsecbugs</groupId>
              <artifactId>findsecbugs-plugin</artifactId>
              <version>${findsecbugs.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changelog-plugin</artifactId>
        <version>2.3</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.version}</version>
        <configuration>
          <linkXref>true</linkXref>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${javadoc.version}</version>
        <configuration>
          <failOnError>false</failOnError>
          <quiet>true</quiet>
          <source>7</source>
          <additionalOptions>${javadoc-additionalOptions}</additionalOptions>
          <links>
            <link>${javadoc-link}</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.5</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-linkcheck-plugin</artifactId>
        <version>1.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${pmd.version}</version>
        <configuration>
          <linkXref>true</linkXref>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.8</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${surefire.version}</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <comparisonVersion>7.0.1</comparisonVersion>
          <textOutputFile>${project.build.directory}/clirr-report.txt</textOutputFile>
          <linkXRef>false</linkXRef>
          <failOnError>false</failOnError>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>java9+</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <javadoc-additionalOptions>-html5</javadoc-additionalOptions>
        <javadoc-link>https://docs.oracle.com/javase/9/docs/api/</javadoc-link>
      </properties>
    </profile>
    <profile>
      <id>develop</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>qa</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.github.siom79.japicmp</groupId>
            <artifactId>japicmp-maven-plugin</artifactId>
            <version>0.14.0</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>cmp</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <parameter>
                <onlyModified>true</onlyModified>
                <onlyBinaryIncompatible>true</onlyBinaryIncompatible>
                <ignoreMissingClassesByRegularExpressions>
                  <ignoreMissingClassesByRegularExpression>org.bouncycastle.*</ignoreMissingClassesByRegularExpression>
                  <ignoreMissingClassesByRegularExpression>com.itextpdf.kernel.pdf.tagutils.IAccessibleElement</ignoreMissingClassesByRegularExpression>
                </ignoreMissingClassesByRegularExpressions>
              </parameter>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.felix</groupId>
            <artifactId>maven-bundle-plugin</artifactId>
            <executions>
              <execution>
                <id>bundle-manifest</id>
                <phase />
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${checkstyle.version}</version>
            <executions>
              <execution>
                <id>validate</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <consoleOutput>true</consoleOutput>
                  <logViolationsToConsole>true</logViolationsToConsole>
                  <failOnViolation>false</failOnViolation>
                  <failsOnError>false</failsOnError>
                  <linkXRef>false</linkXRef>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <executions>
              <execution>
                <id>default-testCompile</id>
                <phase />
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-failsafe-plugin</artifactId>
            <configuration>
              <skip>true</skip>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>${pmd.version}</version>
            <executions>
              <execution>
                <id>validate</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <consoleOutput>true</consoleOutput>
                  <printFailingErrors>true</printFailingErrors>
                  <failOnViolation>false</failOnViolation>
                  <failsOnError>false</failsOnError>
                  <linkXRef>false</linkXRef>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-resources-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>default-resources</id>
                <phase />
              </execution>
              <execution>
                <id>default-testResources</id>
                <phase />
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <configuration>
              <skipSource>true</skipSource>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <executions>
              <execution>
                <id>default-test</id>
                <phase />
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <version>2.8</version>
            <executions>
              <execution>
                <id>clirr</id>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <comparisonVersion>7.1.0</comparisonVersion>
              <textOutputFile>${project.build.directory}/clirr-report.txt</textOutputFile>
              <linkXRef>false</linkXRef>
              <failOnError>false</failOnError>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.revapi</groupId>
            <artifactId>revapi-maven-plugin</artifactId>
            <version>0.11.0</version>
            <executions>
              <execution>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>org.revapi</groupId>
                <artifactId>revapi-java</artifactId>
                <version>0.19.0</version>
              </dependency>
            </dependencies>
            <configuration>
              <checkDependencies>false</checkDependencies>
              <failBuildOnProblemsFound>false</failBuildOnProblemsFound>
              <reportSeverity>breaking</reportSeverity>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>test</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-maven-plugin</artifactId>
            <version>${spotbugs.version}</version>
            <executions>
              <execution>
                <id>default</id>
                <configuration>
                  <skip>true</skip>
                </configuration>
              </execution>
              <execution>
                <id>spotbugs</id>
                <configuration>
                  <skip>false</skip>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- Run integration tests -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <version>${failsafe.version}</version>
            <executions>
              <execution>
                <goals>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <skipTests>false</skipTests>
              <includes>
                <include>**/*Test.java</include>
              </includes>
              <groups>${slowtests}</groups>
              <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
              <argLine>@{jacoco.agent.argLine}</argLine>
              <threadCount>1</threadCount>
            </configuration>
          </plugin>
          <!-- Run unit tests -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${surefire.version}</version>
            <dependencies>
              <dependency>
                <groupId>org.apache.maven.surefire</groupId>
                <artifactId>surefire-junit47</artifactId>
                <version>${surefire.version}</version>
                <scope>compile</scope>
              </dependency>
            </dependencies>
            <configuration>
              <skipTests>false</skipTests>
              <includes>
                <include>**/*Test.java</include>
              </includes>
              <groups>${unittests}</groups>
              <argLine>@{jacoco.agent.argLine}</argLine>
            </configuration>
          </plugin>
          <!-- Compute test coverage for Sonar BEWARE: Sonar
                                  doesn't run the verify phase, it has to be forced by setting -Dsonar.phase=verify -->
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${jacoco.version}</version>
            <executions>
              <execution>
                <id>agent</id>
                <goals>
                  <goal>prepare-agent</goal>
                </goals>
              </execution>
              <execution>
                <id>report</id>
                <phase>site</phase>
                <goals>
                  <goal>report</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <propertyName>jacoco.agent.argLine</propertyName>
              <!-- default: argLine -->
              <includes>
                <include>com/itextpdf/**</include>
              </includes>
              <destFile>${project.build.directory}/jacoco-integration.exec</destFile>
              <!-- agent -->
              <dataFile>${project.build.directory}/jacoco-integration.exec</dataFile>
              <!-- report -->
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.pitest</groupId>
            <artifactId>pitest-maven</artifactId>
            <version>${pitest.version}</version>
            <configuration>
              <targetClasses>
                <param>com.itextpdf*</param>
              </targetClasses>
              <targetTests>
                <param>com.itextpdf*</param>
              </targetTests>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
