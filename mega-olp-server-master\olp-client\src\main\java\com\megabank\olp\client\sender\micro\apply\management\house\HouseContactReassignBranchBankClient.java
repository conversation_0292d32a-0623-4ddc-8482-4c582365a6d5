package com.megabank.olp.client.sender.micro.apply.management.house;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactReassignBranchBankResultBean;

@Component
public class HouseContactReassignBranchBankClient extends BaseApplyClient<HouseContactArgBean, List<HouseContactReassignBranchBankResultBean>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return HouseContactReassignBranchBankResultBean.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/management/housecontact/getReassignBranchBank";
	}
}
