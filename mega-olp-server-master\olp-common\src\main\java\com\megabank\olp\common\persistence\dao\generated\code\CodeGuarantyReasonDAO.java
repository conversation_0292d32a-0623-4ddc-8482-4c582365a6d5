package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeGuarantyReason;

import org.springframework.stereotype.Repository;

/**
 * The CodeGuarantyReasonDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeGuarantyReasonDAO extends BasePojoDAO<CodeGuarantyReason, String>
{
	@Override
	protected Class<CodeGuarantyReason> getPojoClass()
	{
		return CodeGuarantyReason.class;
	}
}
