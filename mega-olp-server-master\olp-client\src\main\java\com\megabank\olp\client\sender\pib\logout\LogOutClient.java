/**
 *
 */
package com.megabank.olp.client.sender.pib.logout;

import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.pib.BasePibClient;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutArgBean;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutReqBean;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutResBean;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class LogOutClient extends BasePibClient<PibLogOutArgBean, PibLogOutReqBean, PibLogOutResBean, PibLogOutResultBean>
{
	@Override
	protected void doReqHeaders( HttpHeaders httpHeaders, PibLogOutArgBean argBean )
	{
		httpHeaders.add( HttpHeaders.AUTHORIZATION, "Bearer " + argBean.getAccessToken() );
		httpHeaders.add( "x-auth-token", argBean.getSessionId() );
	}

	@Override
	protected String getSimulatorCode( PibLogOutArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/openAPI/v1.0.0/logout";
	}

	@Override
	protected PibLogOutReqBean transArg2Req( PibLogOutArgBean argBean )
	{
		PibLogOutReqBean reqBean = new PibLogOutReqBean();
		reqBean.setClientIp( argBean.getClientIp() );
		reqBean.setTrackingIxd( argBean.getTrackingIxd() );

		return reqBean;
	}

	@Override
	protected PibLogOutResultBean transRes2Result( PibLogOutResBean resBean, HttpHeaders httpHeaders )
	{
		PibLogOutResultBean resultBean = new PibLogOutResultBean();
		resultBean.setSuccess( true );

		return resultBean;
	}

}
