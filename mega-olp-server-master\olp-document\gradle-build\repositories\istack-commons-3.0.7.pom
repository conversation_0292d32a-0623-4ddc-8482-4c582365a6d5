<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2018 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>5</version>
    </parent>

    <groupId>com.sun.istack</groupId>
    <artifactId>istack-commons</artifactId>
    <version>3.0.7</version>
    <packaging>pom</packaging>

    <name>istack common utility code</name>
    <description>istack common utility code</description>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <developers>
        <developer>
            <name>Lukas Jungmann</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Iaroslav Savytskyi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Martin Grebac</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>CDDL 1.1</name>
            <url>https://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <modules>
        <module>buildtools</module>
        <module>runtime</module>
        <module>test</module>
        <module>tools</module>
        <module>maven-plugin</module>
        <module>import-properties-plugin</module>
        <module>soimp</module>
    </modules>

    <scm>
        <connection>scm:git:ssh://**************/javaee/jaxb-istack-commons.git</connection>
        <developerConnection>scm:git:ssh://**************/javaee/jaxb-istack-commons.git</developerConnection>
        <url>https://github.com/javaee/jaxb-istack-commons</url>
        <tag>HEAD</tag>
    </scm>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.tmatesoft.svnkit</groupId>
                            <artifactId>svnkit</artifactId>
                            <version>1.8.14</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>com.trilead</groupId>
                                    <artifactId>trilead-ssh2</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                        <dependency>
                            <groupId>com.trilead</groupId>
                            <artifactId>trilead-ssh2</artifactId>
                            <version>1.0.0-build220</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                        <providerImplementations>
                            <svn>javasvn</svn>
                        </providerImplementations>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <revisionOnScmFailure>false</revisionOnScmFailure>
                        <useLastCommittedRevision>true</useLastCommittedRevision>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.6.1</version>
                    <configuration>
                        <source>1.7</source>
                        <target>1.7</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <formats>
                            <format>xml</format>
                        </formats>
                        <check>
                            <totalLineRate>45</totalLineRate>
                            <packageLineRate>45</packageLineRate>
                            <haltOnFailure>true</haltOnFailure>
                        </check>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.40</version>
                    <configuration>
                        <templateFile>copyright.txt</templateFile>
                        <excludeFile>copyright-exclude</excludeFile>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.4</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            exclude-common.xml,${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>1.7</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <useDefaultManifestFile>true</useDefaultManifestFile>
                        <archive>
                            <manifestEntries>
                                <Implementation-Build-Id>${scmBranch}-${buildNumber}, ${timestamp}</Implementation-Build-Id>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <deployAtEnd>true</deployAtEnd>
                        <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.2.0</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <properties>
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>Low</findbugs.threshold>
        <findbugs.exclude>exclude.xml</findbugs.exclude>
        <netbeans.hint.jdkPlatform>JDK_1.7</netbeans.hint.jdkPlatform>
        <maven.api.version>3.5.2</maven.api.version>
    </properties>

    <profiles>
        <profile>
            <id>jvnet-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.0.0-M1</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>3.0.1</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
