package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeOtherBank is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_other_bank" )
public class CodeOtherBank extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_other_bank";

	public static final String OTHER_BANK_CODE_CONSTANT = "otherBankCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISABLED_CONSTANT = "disabled";

	private String otherBankCode;

	private String name;

	private boolean disabled;

	public CodeOtherBank()
	{}

	public CodeOtherBank( String otherBankCode )
	{
		this.otherBankCode = otherBankCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "other_bank_code", unique = true, nullable = false, length = 3 )
	public String getOtherBankCode()
	{
		return otherBankCode;
	}

	@Column( name = "disabled", nullable = false, precision = 1, scale = 0 )
	public boolean isDisabled()
	{
		return disabled;
	}

	public void setDisabled( boolean disabled )
	{
		this.disabled = disabled;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherBankCode( String otherBankCode )
	{
		this.otherBankCode = otherBankCode;
	}
}