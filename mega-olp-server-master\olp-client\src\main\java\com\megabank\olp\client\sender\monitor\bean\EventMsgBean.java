package com.megabank.olp.client.sender.monitor.bean;

import java.util.Collections;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "msgCode", "occurDate", "hostName", "subSystem", "tjpstNoId", "tstIdName", "pgcName", "ctiCode", "platform", "subCategoryId",
						"eventSource", "dataType", "isOverwrite", "myTokenNo", "parentTokenNo", "peopleType", "peopleId", "placeType", "placeId",
						"paramCount", "status", "eventMsgDetailBeans" } )
public class EventMsgBean extends BaseBean
{
	@XmlElement( name = "MSG_CODE" )
	private String msgCode;

	@XmlElement( name = "OCCUR_DATE" )
	private String occurDate;

	@XmlElement( name = "HOSTNAME" )
	private String hostName;

	@XmlElement( name = "SUB_SYSTEM" )
	private String subSystem = "";

	@XmlElement( name = "TJPST_NO_ID" )
	private String tjpstNoId = "";

	@XmlElement( name = "TST_ID_NAME" )
	private String tstIdName = "";

	@XmlElement( name = "PGC_NAME" )
	private String pgcName;

	@XmlElement( name = "CTI_CODE" )
	private String ctiCode;

	@XmlElement( name = "PLAT_FORM" )
	private String platform;

	@XmlElement( name = "SUBCATEGORY_ID" )
	private String subCategoryId = "";

	@XmlElement( name = "EVENT_SOURCE" )
	private String eventSource;

	@XmlElement( name = "DATA_TYPE" )
	private String dataType;

	@XmlElement( name = "IS_OVERWRITE" )
	private String isOverwrite;

	@XmlElement( name = "MYSELF_TOKEN_NO" )
	private String myTokenNo = "";

	@XmlElement( name = "PARENT_TOKEN_NO" )
	private String parentTokenNo = "";

	@XmlElement( name = "PEOPLE_TYPE" )
	private String peopleType = "";

	@XmlElement( name = "PEOPLE_ID" )
	private String peopleId = "";

	@XmlElement( name = "PLACE_TYPE" )
	private String placeType = "";

	@XmlElement( name = "PLACE_ID" )
	private String placeId = "";

	@XmlElement( name = "PARAM_COUNT" )
	private Integer paramCount;

	@XmlElement( name = "STATUS" )
	private String status;

	@XmlElementWrapper( name = "msgDetail" )
	@XmlElement( name = "ITMSG_EVENT_MSG_D" )
	private List<EventMsgDetailBean> eventMsgDetailBeans = Collections.emptyList();

	public EventMsgBean()
	{}

	public String getCtiCode()
	{
		return ctiCode;
	}

	public String getDataType()
	{
		return dataType;
	}

	public List<EventMsgDetailBean> getEventMsgDetailBeans()
	{
		return eventMsgDetailBeans;
	}

	public String getEventSource()
	{
		return eventSource;
	}

	public String getHostName()
	{
		return hostName;
	}

	public String getIsOverwrite()
	{
		return isOverwrite;
	}

	public String getMsgCode()
	{
		return msgCode;
	}

	public String getMyTokenNo()
	{
		return myTokenNo;
	}

	public String getOccurDate()
	{
		return occurDate;
	}

	public Integer getParamCount()
	{
		return paramCount;
	}

	public String getParentTokenNo()
	{
		return parentTokenNo;
	}

	public String getPeopleId()
	{
		return peopleId;
	}

	public String getPeopleType()
	{
		return peopleType;
	}

	public String getPgcName()
	{
		return pgcName;
	}

	public String getPlaceId()
	{
		return placeId;
	}

	public String getPlaceType()
	{
		return placeType;
	}

	public String getPlatform()
	{
		return platform;
	}

	public String getStatus()
	{
		return status;
	}

	public String getSubCategoryId()
	{
		return subCategoryId;
	}

	public String getSubSystem()
	{
		return subSystem;
	}

	public String getTjpstNoId()
	{
		return tjpstNoId;
	}

	public String getTstIdName()
	{
		return tstIdName;
	}

	public void setCtiCode( String ctiCode )
	{
		this.ctiCode = ctiCode;
	}

	public void setDataType( String dataType )
	{
		this.dataType = dataType;
	}

	public void setEventMsgDetailBeans( List<EventMsgDetailBean> eventMsgDetailBeans )
	{
		this.eventMsgDetailBeans = eventMsgDetailBeans;
	}

	public void setEventSource( String eventSource )
	{
		this.eventSource = eventSource;
	}

	public void setHostName( String hostName )
	{
		this.hostName = hostName;
	}

	public void setIsOverwrite( String isOverwrite )
	{
		this.isOverwrite = isOverwrite;
	}

	public void setMsgCode( String msgCode )
	{
		this.msgCode = msgCode;
	}

	public void setMyTokenNo( String myTokenNo )
	{
		this.myTokenNo = myTokenNo;
	}

	public void setOccurDate( String occurDate )
	{
		this.occurDate = occurDate;
	}

	public void setParamCount( Integer paramCount )
	{
		this.paramCount = paramCount;
	}

	public void setParentTokenNo( String parentTokenNo )
	{
		this.parentTokenNo = parentTokenNo;
	}

	public void setPeopleId( String peopleId )
	{
		this.peopleId = peopleId;
	}

	public void setPeopleType( String peopleType )
	{
		this.peopleType = peopleType;
	}

	public void setPgcName( String pgcName )
	{
		this.pgcName = pgcName;
	}

	public void setPlaceId( String placeId )
	{
		this.placeId = placeId;
	}

	public void setPlaceType( String placeType )
	{
		this.placeType = placeType;
	}

	public void setPlatform( String platform )
	{
		this.platform = platform;
	}

	public void setStatus( String status )
	{
		this.status = status;
	}

	public void setSubCategoryId( String subCategoryId )
	{
		this.subCategoryId = subCategoryId;
	}

	public void setSubSystem( String subSystem )
	{
		this.subSystem = subSystem;
	}

	public void setTjpstNoId( String tjpstNoId )
	{
		this.tjpstNoId = tjpstNoId;
	}

	public void setTstIdName( String tstIdName )
	{
		this.tstIdName = tstIdName;
	}

}
