package com.megabank.olp.client.sender.mydata.webcomm.type1.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.client.sender.webcomm.mydata.WebCommMyDataType1ResBean;

public class MyDataType1ResBean extends WebCommMyDataType1ResBean
{
	private Integer status;

	private String msg;

	@JsonProperty( "results" )
	private ResultsBean resultsBean;

	public MyDataType1ResBean()
	{}
	
	public Integer getStatus() 
	{
		return status;
	}

	public void setStatus(Integer status) 
	{
		this.status = status;
	}

	public String getMsg() 
	{
		return msg;
	}

	public void setMsg(String msg) 
	{
		this.msg = msg;
	}

	public ResultsBean getResultsBean() 
	{
		return resultsBean;
	}

	public void setResultsBean(ResultsBean resultsBean) 
	{
		this.resultsBean = resultsBean;
	}
	
}
