package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeIdentityType;

import org.springframework.stereotype.Repository;

/**
 * The CodeIdentityTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeIdentityTypeDAO extends BasePojoDAO<CodeIdentityType, String>
{
	@Override
	protected Class<CodeIdentityType> getPojoClass()
	{
		return CodeIdentityType.class;
	}
}
