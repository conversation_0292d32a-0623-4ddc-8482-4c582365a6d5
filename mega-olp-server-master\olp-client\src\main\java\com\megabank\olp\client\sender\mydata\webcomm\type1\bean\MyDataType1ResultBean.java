package com.megabank.olp.client.sender.mydata.webcomm.type1.bean;

import com.megabank.olp.client.sender.webcomm.mydata.WebCommMyDataType1ResultBean;

public class MyDataType1ResultBean extends WebCommMyDataType1ResultBean
{
	private Integer status;

	private String msg;

	private ResultsBean results;

	public MyDataType1ResultBean()
	{}

	public Integer getStatus() 
	{
		return status;
	}

	public void setStatus(Integer status) 
	{
		this.status = status;
	}

	public String getMsg() 
	{
		return msg;
	}

	public void setMsg(String msg) 
	{
		this.msg = msg;
	}

	public ResultsBean getResults() 
	{
		return results;
	}

	public void setResults(ResultsBean results) 
	{
		this.results = results;
	}

}
