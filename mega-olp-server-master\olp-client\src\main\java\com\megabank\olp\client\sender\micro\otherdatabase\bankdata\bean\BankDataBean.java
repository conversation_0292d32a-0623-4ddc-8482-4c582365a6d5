/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.bankdata.bean;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class BankDataBean extends BaseBean
{
	private String code;

	private String name;

	private String dcrosverifyFlag;
	
	private String branchCode;
	
	private String fullName;

	public String getCode()
	{
		return code;
	}

	public String getDcrosverifyFlag()
	{
		return dcrosverifyFlag;
	}

	public String getName()
	{
		return name;
	}

	public String getBranchCode()
	{
		return branchCode;
	}
	
	public String getFullName()
	{
		return fullName;
	}
	
	public void setCode( String code )
	{
		this.code = code;
	}

	public void setDcrosverifyFlag( String dcrosverifyFlag )
	{
		this.dcrosverifyFlag = dcrosverifyFlag;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}
	
	public void setFullName( String fullName )
	{
		this.fullName = fullName;
	}
}
