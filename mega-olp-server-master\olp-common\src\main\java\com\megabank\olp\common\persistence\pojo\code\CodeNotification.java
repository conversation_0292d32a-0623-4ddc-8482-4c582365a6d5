package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeNotification is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_notification" )
public class CodeNotification extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_notification";

	public static final String NOTIFICATION_CODE_CONSTANT = "notificationCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	private String notificationCode;

	private String name;

	private int displayOrder;

	public CodeNotification()
	{}

	public CodeNotification( String notificationCode )
	{
		this.notificationCode = notificationCode;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "notification_code", unique = true, nullable = false, length = 20 )
	public String getNotificationCode()
	{
		return notificationCode;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationCode( String notificationCode )
	{
		this.notificationCode = notificationCode;
	}
}