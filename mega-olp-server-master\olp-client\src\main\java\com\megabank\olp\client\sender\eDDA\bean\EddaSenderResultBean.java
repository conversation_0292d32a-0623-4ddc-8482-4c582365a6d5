package com.megabank.olp.client.sender.eDDA.bean;

import com.megabank.olp.base.bean.BaseBean;

public class EddaSenderResultBean extends BaseBean
{
	/**
	 *
	 */
	private static final long serialVersionUID = 1L;
	
	private String aDate;

	private String rc;

	private String effDate;

	private String note;

	private String seq;

	private String cNo;

	private String reservedField;

	private String pBankNote;

	private String rcDesc;

	public String getaDate()
	{
		return aDate;
	}

	public String getcNo()
	{
		return cNo;
	}

	public String getEffDate()
	{
		return effDate;
	}

	public String getNote()
	{
		return note;
	}

	public String getpBankNote()
	{
		return pBankNote;
	}

	public String getRc()
	{
		return rc;
	}

	public String getRcDesc()
	{
		return rcDesc;
	}

	public String getReservedField()
	{
		return reservedField;
	}

	public String getSeq()
	{
		return seq;
	}

	public void setaDate( String aDate )
	{
		this.aDate = aDate;
	}

	public void setcNo( String cNo )
	{
		this.cNo = cNo;
	}

	public void setEffDate( String effDate )
	{
		this.effDate = effDate;
	}

	public void setNote( String note )
	{
		this.note = note;
	}

	public void setpBankNote( String pBankNote )
	{
		this.pBankNote = pBankNote;
	}

	public void setRc( String rc )
	{
		this.rc = rc;
	}

	public void setRcDesc( String rcDesc )
	{
		this.rcDesc = rcDesc;
	}

	public void setReservedField( String reservedField )
	{
		this.reservedField = reservedField;
	}

	public void setSeq( String seq )
	{
		this.seq = seq;
	}
}
