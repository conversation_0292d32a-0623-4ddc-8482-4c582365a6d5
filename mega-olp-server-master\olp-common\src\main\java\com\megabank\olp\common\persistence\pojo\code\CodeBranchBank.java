package com.megabank.olp.common.persistence.pojo.code;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeBranchBank is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_branch_bank" )
public class CodeBranchBank extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_branch_bank";

	public static final String BRANCH_BANK_ID_CONSTANT = "branchBankId";

	public static final String CODE_TOWN_CONSTANT = "codeTown";

	public static final String NAME_CONSTANT = "name";

	public static final String BANK_CODE_CONSTANT = "bankCode";

	public static final String ADDRESS_CONSTANT = "address";

	public static final String HEAD_OFFICE_CONSTANT = "headOffice";

	public static final String DISABLED_CONSTANT = "disabled";

	private Long branchBankId;

	private transient CodeTown codeTown;

	private String name;

	private String bankCode;

	private String address;

	private boolean headOffice;

	private boolean disabled;

	public CodeBranchBank()
	{}

	public CodeBranchBank( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	@Column( name = "address", nullable = false )
	public String getAddress()
	{
		return address;
	}

	@Column( name = "bank_code", nullable = false, length = 10 )
	public String getBankCode()
	{
		return bankCode;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "branch_bank_id", unique = true, nullable = false )
	public Long getBranchBankId()
	{
		return branchBankId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "town_code", nullable = false )
	public CodeTown getCodeTown()
	{
		return codeTown;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Column( name = "disabled", nullable = false, precision = 1, scale = 0 )
	public boolean isDisabled()
	{
		return disabled;
	}

	@Column( name = "head_office", nullable = false, precision = 1, scale = 0 )
	public boolean isHeadOffice()
	{
		return headOffice;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setCodeTown( CodeTown codeTown )
	{
		this.codeTown = codeTown;
	}

	public void setDisabled( boolean disabled )
	{
		this.disabled = disabled;
	}

	public void setHeadOffice( boolean headOffice )
	{
		this.headOffice = headOffice;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}