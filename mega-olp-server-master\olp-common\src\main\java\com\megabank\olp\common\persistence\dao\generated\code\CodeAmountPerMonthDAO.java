package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeAmountPerMonth;

import org.springframework.stereotype.Repository;

/**
 * The CodeAmountPerMonthDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeAmountPerMonthDAO extends BasePojoDAO<CodeAmountPerMonth, String>
{
	@Override
	protected Class<CodeAmountPerMonth> getPojoClass()
	{
		return CodeAmountPerMonth.class;
	}
}
