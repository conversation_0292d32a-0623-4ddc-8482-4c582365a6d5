package com.megabank.olp.common.persistence.pojo.code;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeList is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_list" )
public class CodeList extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_list";

	public static final String CODE_ID_CONSTANT = "codeId";

	public static final String CODE_TYPE_CONSTANT = "codeType";

	public static final String CODE_VALUE_CONSTANT = "codeValue";

	public static final String CODE_DESC_CONSTANT = "codeDesc";

	public static final String CODE_DISPLAY_ORDER_CONSTANT = "codeDisplayOrder";

	public static final String CODE_DISABLED_CONSTANT = "codeDisabled";

	public static final String CODE_MODEIFY_TIME_CONSTANT = "codeModeifyTime";

	private Long codeId;

	private String codeType;

	private String codeValue;

	private String codeDesc;

	private long codeDisplayOrder;

	private boolean codeDisabled;

	private Date codeModeifyTime;

	public CodeList()
	{}

	public CodeList( Long codeId )
	{
		this.codeId = codeId;
	}

	@Column( name = "code_desc", nullable = false )
	public String getCodeDesc()
	{
		return codeDesc;
	}

	@Column( name = "code_disabled", nullable = false, precision = 1, scale = 0 )
	public boolean getCodeDisabled()
	{
		return codeDisabled;
	}

	@Column( name = "code_display_order", nullable = false, precision = 5, scale = 0 )
	public long getCodeDisplayOrder()
	{
		return codeDisplayOrder;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "code_id", unique = true, nullable = false )
	public Long getCodeId()
	{
		return codeId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "code_modeify_time", nullable = false, length = 23 )
	public Date getCodeModeifyTime()
	{
		return codeModeifyTime;
	}

	@Column( name = "code_type", nullable = false, length = 50 )
	public String getCodeType()
	{
		return codeType;
	}

	@Column( name = "code_value", nullable = false, length = 20 )
	public String getCodeValue()
	{
		return codeValue;
	}

	public void setCodeDesc( String codeDesc )
	{
		this.codeDesc = codeDesc;
	}

	public void setCodeDisabled( boolean codeDisabled )
	{
		this.codeDisabled = codeDisabled;
	}

	public void setCodeDisplayOrder( long codeDisplayOrder )
	{
		this.codeDisplayOrder = codeDisplayOrder;
	}

	public void setCodeId( Long codeId )
	{
		this.codeId = codeId;
	}

	public void setCodeModeifyTime( Date codeModeifyTime )
	{
		this.codeModeifyTime = codeModeifyTime;
	}

	public void setCodeType( String codeType )
	{
		this.codeType = codeType;
	}

	public void setCodeValue( String codeValue )
	{
		this.codeValue = codeValue;
	}
}