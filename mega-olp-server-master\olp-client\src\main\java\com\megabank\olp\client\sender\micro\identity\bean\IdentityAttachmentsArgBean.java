package com.megabank.olp.client.sender.micro.identity.bean;

import com.megabank.olp.base.bean.BaseBean;

public class IdentityAttachmentsArgBean extends BaseBean
{
	private Long otherBankId;

	public IdentityAttachmentsArgBean()
	{
		// default constructor
	}

	public Long getOtherBankId()
	{
		return otherBankId;
	}

	public void setOtherBankId( Long otherBankId )
	{
		this.otherBankId = otherBankId;
	}
}