package com.megabank.olp.common.controller.bean.code;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class SubCodeListGetterArgBean extends BaseBean
{
	@NotBlank
	private String codeId;

	@NotBlank
	private String codeType;

	public SubCodeListGetterArgBean()
	{
		// default constructor
	}

	/**
	 * 
	 * @return codeId
	 */
	public String getCodeId()
	{
		return codeId;
	}

	/**
	 *
	 * @return codeType
	 */
	public String getCodeType()
	{
		return codeType;
	}

	/**
	 * 
	 * @param codeId
	 */
	public void setCodeId( String codeId )
	{
		this.codeId = codeId;
	}

	/**
	 *
	 * @param codeType
	 */
	public void setCodeType( String codeType )
	{
		this.codeType = codeType;
	}
}
