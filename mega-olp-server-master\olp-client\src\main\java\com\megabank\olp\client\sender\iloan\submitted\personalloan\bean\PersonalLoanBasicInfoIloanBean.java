package com.megabank.olp.client.sender.iloan.submitted.personalloan.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class PersonalLoanBasicInfoIloanBean extends BaseBean
{
	private String name;

	private String enName;

	private String birthDate;

	private String idNo;

	private String educationLevel;

	private String marriageStatus;

	private Integer childrenCount;

	private String nationality;

	private String residenceStatus;

	private Integer rentAmt;

	private String houseStatus;

	@JsonProperty( "cellNumber" )
	private String mobileNumber;

	private String email;

	@JsonProperty( "homeTel" )
	private String homePhone;

	@JsonProperty( "homeAddrCity" )
	private String homeAddressCity;

	@JsonProperty( "homeAddrTown" )
	private String homeAddressTown;

	@JsonProperty( "homeAddrVillage" )
	private String homeAddressVillage;

	@JsonProperty( "homeAddrNeighborhood" )
	private String homeAddressNeighborhood;

	@JsonProperty( "homeAddrStreet" )
	private String homeAddressStreet;

	@JsonProperty( "homeAddrSection" )
	private String homeAddressSection;

	@JsonProperty( "homeAddrLane" )
	private String homeAddressLane;

	@JsonProperty( "homeAddrAlley" )
	private String homeAddressAlley;

	@JsonProperty( "homeAddrNo" )
	private String homeAddressNo;

	@JsonProperty( "homeAddrFloor" )
	private String homeAddressFloor;

	@JsonProperty( "homeAddrRoom" )
	private String homeAddressRoom;

	@JsonProperty( "mailingAddrCity" )
	private String mailingAddressCity;

	@JsonProperty( "mailingAddrTown" )
	private String mailingAddressTown;

	@JsonProperty( "mailingAddrVillage" )
	private String mailingAddressVillage;

	@JsonProperty( "mailingAddrNeighborhood" )
	private String mailingAddressNeighborhood;

	@JsonProperty( "mailingAddrStreet" )
	private String mailingAddressStreet;

	@JsonProperty( "mailingAddrSection" )
	private String mailingAddressSection;

	@JsonProperty( "mailingAddrLane" )
	private String mailingAddressLane;
	@JsonProperty( "mailingAddrAlley" )
	private String mailingAddressAlley;

	@JsonProperty( "mailingAddrNo" )
	private String mailingAddressNo;

	@JsonProperty( "mailingAddrFloor" )
	private String mailingAddressFloor;

	@JsonProperty( "mailingAddrRoom" )
	private String mailingAddressRoom;

	private String jobType;

	private String jobSubType;

	private String titleType;

	private String companyName;

	private String companyTaxNo;

	@JsonProperty( "companyTel" )
	private String companyPhone;

	@JsonProperty( "companyAddrCity" )
	private String companyAddressCity;

	@JsonProperty( "companyAddrTown" )
	private String companyAddressTown;

	@JsonProperty( "companyAddrVillage" )
	private String companyAddressVillage;

	@JsonProperty( "companyAddrNeighborhood" )
	private String companyAddressNeighborhood;

	@JsonProperty( "companyAddrStreet" )
	private String companyAddressStreet;

	@JsonProperty( "companyAddrSection" )
	private String companyAddressSection;

	@JsonProperty( "companyAddrLane" )
	private String companyAddressLane;

	@JsonProperty( "companyAddrAlley" )
	private String companyAddressAlley;

	@JsonProperty( "companyAddrNo" )
	private String companyAddressNo;

	@JsonProperty( "companyAddrFloor" )
	private String companyAddressFloor;

	@JsonProperty( "companyAddrRoom" )
	private String companyAddressRoom;

	private Integer annualIncome;

	private Integer seniority;

	private String avgTransactionAmt;

	private String serviceAssociateDeptCode;

	private String serviceAssociateCode;

	private String assignedBranchCode;

	private String resultBranchCode;

	private String guarantyReason;

	private String otherGuarantyReason;

	private String relationWithBorrower;

	private String liveWithBorrower;

	private Integer seniorityMonth;

	private String needW8BEN;

	private String needCRS;

	private String empNo;

	private String rateAdjNotify;

	private String crossMarket;

	public PersonalLoanBasicInfoIloanBean()
	{
	}

	public Integer getAnnualIncome()
	{
		return annualIncome;
	}

	public void setAnnualIncome( Integer annualIncome )
	{
		this.annualIncome = annualIncome;
	}

	public String getAssignedBranchCode()
	{
		return assignedBranchCode;
	}

	public void setAssignedBranchCode( String assignedBranchCode )
	{
		this.assignedBranchCode = assignedBranchCode;
	}

	public String getAvgTransactionAmt()
	{
		return avgTransactionAmt;
	}

	public void setAvgTransactionAmt( String avgTransactionAmt )
	{
		this.avgTransactionAmt = avgTransactionAmt;
	}

	public String getBirthDate()
	{
		return birthDate;
	}

	public void setBirthDate( String birthDate )
	{
		this.birthDate = birthDate;
	}

	public Integer getChildrenCount()
	{
		return childrenCount;
	}

	public void setChildrenCount( Integer childrenCount )
	{
		this.childrenCount = childrenCount;
	}

	public String getCompanyAddressAlley()
	{
		return companyAddressAlley;
	}

	public void setCompanyAddressAlley( String companyAddressAlley )
	{
		this.companyAddressAlley = companyAddressAlley;
	}

	public String getCompanyAddressCity()
	{
		return companyAddressCity;
	}

	public void setCompanyAddressCity( String companyAddressCity )
	{
		this.companyAddressCity = companyAddressCity;
	}

	public String getCompanyAddressFloor()
	{
		return companyAddressFloor;
	}

	public void setCompanyAddressFloor( String companyAddressFloor )
	{
		this.companyAddressFloor = companyAddressFloor;
	}

	public String getCompanyAddressLane()
	{
		return companyAddressLane;
	}

	public void setCompanyAddressLane( String companyAddressLane )
	{
		this.companyAddressLane = companyAddressLane;
	}

	public String getCompanyAddressNeighborhood()
	{
		return companyAddressNeighborhood;
	}

	public void setCompanyAddressNeighborhood( String companyAddressNeighborhood )
	{
		this.companyAddressNeighborhood = companyAddressNeighborhood;
	}

	public String getCompanyAddressNo()
	{
		return companyAddressNo;
	}

	public void setCompanyAddressNo( String companyAddressNo )
	{
		this.companyAddressNo = companyAddressNo;
	}

	public String getCompanyAddressRoom()
	{
		return companyAddressRoom;
	}

	public void setCompanyAddressRoom( String companyAddressRoom )
	{
		this.companyAddressRoom = companyAddressRoom;
	}

	public String getCompanyAddressSection()
	{
		return companyAddressSection;
	}

	public void setCompanyAddressSection( String companyAddressSection )
	{
		this.companyAddressSection = companyAddressSection;
	}

	public String getCompanyAddressStreet()
	{
		return companyAddressStreet;
	}

	public void setCompanyAddressStreet( String companyAddressStreet )
	{
		this.companyAddressStreet = companyAddressStreet;
	}

	public String getCompanyAddressTown()
	{
		return companyAddressTown;
	}

	public void setCompanyAddressTown( String companyAddressTown )
	{
		this.companyAddressTown = companyAddressTown;
	}

	public String getCompanyAddressVillage()
	{
		return companyAddressVillage;
	}

	public void setCompanyAddressVillage( String companyAddressVillage )
	{
		this.companyAddressVillage = companyAddressVillage;
	}

	public String getCompanyName()
	{
		return companyName;
	}

	public void setCompanyName( String companyName )
	{
		this.companyName = companyName;
	}

	public String getCompanyPhone()
	{
		return companyPhone;
	}

	public void setCompanyPhone( String companyPhone )
	{
		this.companyPhone = companyPhone;
	}

	public String getCompanyTaxNo()
	{
		return companyTaxNo;
	}

	public void setCompanyTaxNo( String companyTaxNo )
	{
		this.companyTaxNo = companyTaxNo;
	}

	public String getCrossMarket()
	{
		return crossMarket;
	}

	public void setCrossMarket( String crossMarket )
	{
		this.crossMarket = crossMarket;
	}

	public String getEducationLevel()
	{
		return educationLevel;
	}

	public void setEducationLevel( String educationLevel )
	{
		this.educationLevel = educationLevel;
	}

	public String getEmail()
	{
		return email;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public String getEmpNo()
	{
		return empNo;
	}

	public void setEmpNo( String empNo )
	{
		this.empNo = empNo;
	}

	public String getEnName()
	{
		return enName;
	}

	public void setEnName( String enName )
	{
		this.enName = enName;
	}

	public String getGuarantyReason()
	{
		return guarantyReason;
	}

	public void setGuarantyReason( String guarantyReason )
	{
		this.guarantyReason = guarantyReason;
	}

	public String getHomeAddressAlley()
	{
		return homeAddressAlley;
	}

	public void setHomeAddressAlley( String homeAddressAlley )
	{
		this.homeAddressAlley = homeAddressAlley;
	}

	public String getHomeAddressCity()
	{
		return homeAddressCity;
	}

	public void setHomeAddressCity( String homeAddressCity )
	{
		this.homeAddressCity = homeAddressCity;
	}

	public String getHomeAddressFloor()
	{
		return homeAddressFloor;
	}

	public void setHomeAddressFloor( String homeAddressFloor )
	{
		this.homeAddressFloor = homeAddressFloor;
	}

	public String getHomeAddressLane()
	{
		return homeAddressLane;
	}

	public void setHomeAddressLane( String homeAddressLane )
	{
		this.homeAddressLane = homeAddressLane;
	}

	public String getHomeAddressNeighborhood()
	{
		return homeAddressNeighborhood;
	}

	public void setHomeAddressNeighborhood( String homeAddressNeighborhood )
	{
		this.homeAddressNeighborhood = homeAddressNeighborhood;
	}

	public String getHomeAddressNo()
	{
		return homeAddressNo;
	}

	public void setHomeAddressNo( String homeAddressNo )
	{
		this.homeAddressNo = homeAddressNo;
	}

	public String getHomeAddressRoom()
	{
		return homeAddressRoom;
	}

	public void setHomeAddressRoom( String homeAddressRoom )
	{
		this.homeAddressRoom = homeAddressRoom;
	}

	public String getHomeAddressSection()
	{
		return homeAddressSection;
	}

	public void setHomeAddressSection( String homeAddressSection )
	{
		this.homeAddressSection = homeAddressSection;
	}

	public String getHomeAddressStreet()
	{
		return homeAddressStreet;
	}

	public void setHomeAddressStreet( String homeAddressStreet )
	{
		this.homeAddressStreet = homeAddressStreet;
	}

	public String getHomeAddressTown()
	{
		return homeAddressTown;
	}

	public void setHomeAddressTown( String homeAddressTown )
	{
		this.homeAddressTown = homeAddressTown;
	}

	public String getHomeAddressVillage()
	{
		return homeAddressVillage;
	}

	public void setHomeAddressVillage( String homeAddressVillage )
	{
		this.homeAddressVillage = homeAddressVillage;
	}

	public String getHomePhone()
	{
		return homePhone;
	}

	public void setHomePhone( String homePhone )
	{
		this.homePhone = homePhone;
	}

	public String getHouseStatus()
	{
		return houseStatus;
	}

	public void setHouseStatus( String houseStatus )
	{
		this.houseStatus = houseStatus;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public String getJobSubType()
	{
		return jobSubType;
	}

	public void setJobSubType( String jobSubType )
	{
		this.jobSubType = jobSubType;
	}

	public String getJobType()
	{
		return jobType;
	}

	public void setJobType( String jobType )
	{
		this.jobType = jobType;
	}

	public String getLiveWithBorrower()
	{
		return liveWithBorrower;
	}

	public void setLiveWithBorrower( String liveWithBorrower )
	{
		this.liveWithBorrower = liveWithBorrower;
	}

	public String getMailingAddressAlley()
	{
		return mailingAddressAlley;
	}

	public void setMailingAddressAlley( String mailingAddressAlley )
	{
		this.mailingAddressAlley = mailingAddressAlley;
	}

	public String getMailingAddressCity()
	{
		return mailingAddressCity;
	}

	public void setMailingAddressCity( String mailingAddressCity )
	{
		this.mailingAddressCity = mailingAddressCity;
	}

	public String getMailingAddressFloor()
	{
		return mailingAddressFloor;
	}

	public void setMailingAddressFloor( String mailingAddressFloor )
	{
		this.mailingAddressFloor = mailingAddressFloor;
	}

	public String getMailingAddressLane()
	{
		return mailingAddressLane;
	}

	public void setMailingAddressLane( String mailingAddressLane )
	{
		this.mailingAddressLane = mailingAddressLane;
	}

	public String getMailingAddressNeighborhood()
	{
		return mailingAddressNeighborhood;
	}

	public void setMailingAddressNeighborhood( String mailingAddressNeighborhood )
	{
		this.mailingAddressNeighborhood = mailingAddressNeighborhood;
	}

	public String getMailingAddressNo()
	{
		return mailingAddressNo;
	}

	public void setMailingAddressNo( String mailingAddressNo )
	{
		this.mailingAddressNo = mailingAddressNo;
	}

	public String getMailingAddressRoom()
	{
		return mailingAddressRoom;
	}

	public void setMailingAddressRoom( String mailingAddressRoom )
	{
		this.mailingAddressRoom = mailingAddressRoom;
	}

	public String getMailingAddressSection()
	{
		return mailingAddressSection;
	}

	public void setMailingAddressSection( String mailingAddressSection )
	{
		this.mailingAddressSection = mailingAddressSection;
	}

	public String getMailingAddressStreet()
	{
		return mailingAddressStreet;
	}

	public void setMailingAddressStreet( String mailingAddressStreet )
	{
		this.mailingAddressStreet = mailingAddressStreet;
	}

	public String getMailingAddressTown()
	{
		return mailingAddressTown;
	}

	public void setMailingAddressTown( String mailingAddressTown )
	{
		this.mailingAddressTown = mailingAddressTown;
	}

	public String getMailingAddressVillage()
	{
		return mailingAddressVillage;
	}

	public void setMailingAddressVillage( String mailingAddressVillage )
	{
		this.mailingAddressVillage = mailingAddressVillage;
	}

	public String getMarriageStatus()
	{
		return marriageStatus;
	}

	public void setMarriageStatus( String marriageStatus )
	{
		this.marriageStatus = marriageStatus;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public String getNationality()
	{
		return nationality;
	}

	public void setNationality( String nationality )
	{
		this.nationality = nationality;
	}

	public String getNeedCRS()
	{
		return needCRS;
	}

	public void setNeedCRS( String needCRS )
	{
		this.needCRS = needCRS;
	}

	public String getNeedW8BEN()
	{
		return needW8BEN;
	}

	public void setNeedW8BEN( String needW8BEN )
	{
		this.needW8BEN = needW8BEN;
	}

	public String getOtherGuarantyReason()
	{
		return otherGuarantyReason;
	}

	public void setOtherGuarantyReason( String otherGuarantyReason )
	{
		this.otherGuarantyReason = otherGuarantyReason;
	}

	public String getRateAdjNotify()
	{
		return rateAdjNotify;
	}

	public void setRateAdjNotify( String rateAdjNotify )
	{
		this.rateAdjNotify = rateAdjNotify;
	}

	public String getRelationWithBorrower()
	{
		return relationWithBorrower;
	}

	public void setRelationWithBorrower( String relationWithBorrower )
	{
		this.relationWithBorrower = relationWithBorrower;
	}

	public Integer getRentAmt()
	{
		return rentAmt;
	}

	public void setRentAmt( Integer rentAmt )
	{
		this.rentAmt = rentAmt;
	}

	public String getResidenceStatus()
	{
		return residenceStatus;
	}

	public void setResidenceStatus( String residenceStatus )
	{
		this.residenceStatus = residenceStatus;
	}

	public String getResultBranchCode()
	{
		return resultBranchCode;
	}

	public void setResultBranchCode( String resultBranchCode )
	{
		this.resultBranchCode = resultBranchCode;
	}

	public Integer getSeniority()
	{
		return seniority;
	}

	public void setSeniority( Integer seniority )
	{
		this.seniority = seniority;
	}

	public Integer getSeniorityMonth()
	{
		return seniorityMonth;
	}

	public void setSeniorityMonth( Integer seniorityMonth )
	{
		this.seniorityMonth = seniorityMonth;
	}

	public String getServiceAssociateCode()
	{
		return serviceAssociateCode;
	}

	public void setServiceAssociateCode( String serviceAssociateCode )
	{
		this.serviceAssociateCode = serviceAssociateCode;
	}

	public String getServiceAssociateDeptCode()
	{
		return serviceAssociateDeptCode;
	}

	public void setServiceAssociateDeptCode( String serviceAssociateDeptCode )
	{
		this.serviceAssociateDeptCode = serviceAssociateDeptCode;
	}

	public String getTitleType()
	{
		return titleType;
	}

	public void setTitleType( String titleType )
	{
		this.titleType = titleType;
	}

}
