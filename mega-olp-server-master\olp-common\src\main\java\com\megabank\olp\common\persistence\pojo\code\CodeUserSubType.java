package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeUserSubType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_user_sub_type" )
public class CodeUserSubType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_user_sub_type";

	public static final String USER_SUB_TYPE_CONSTANT = "userSubType";

	public static final String CODE_USER_TYPE_CONSTANT = "codeUserType";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	private String userSubType;

	private transient CodeUserType codeUserType;

	private String name;

	private int displayOrder;

	public CodeUserSubType()
	{}

	public CodeUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "user_type", nullable = false )
	public CodeUserType getCodeUserType()
	{
		return codeUserType;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "user_sub_type", unique = true, nullable = false, length = 20 )
	public String getUserSubType()
	{
		return userSubType;
	}

	public void setCodeUserType( CodeUserType codeUserType )
	{
		this.codeUserType = codeUserType;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}
}