package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalPbAccountArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalPbAccountResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */
@Component
public class InternalPbAccountClient extends BaseOtherDatabaseClient<InternalPbAccountArgBean, InternalPbAccountResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/ods/account/getPbAccount";
	}

}
