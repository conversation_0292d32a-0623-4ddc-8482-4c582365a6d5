package com.megabank.olp.common.persistence.dao.generated.landing;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.landing.LandingSolutionValue;

/**
 * The LandingSolutionValueDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class LandingSolutionValueDAO extends BasePojoDAO<LandingSolutionValue, Long>
{
	@Autowired
	private LandingTemplateColumnDAO landingTemplateColumnDAO;

	@Autowired
	private LandingSolutionDAO landingSolutionDAO;

	public List<LandingSolutionValue> getPojosBySolutionId( Long solutionId, Long templateColumnId )
	{
		Validate.notNull( solutionId );
		Validate.notNull( templateColumnId );

		NameValueBean landingSolution = new NameValueBean( LandingSolutionValue.LANDING_SOLUTION_CONSTANT, landingSolutionDAO.read( solutionId ) );
		NameValueBean landingTemplateColumn = new NameValueBean( LandingSolutionValue.LANDING_TEMPLATE_COLUMN_CONSTANT,
																 landingTemplateColumnDAO.read( templateColumnId ) );

		NameValueBean[] conditions = new NameValueBean[]{ landingSolution, landingTemplateColumn };

		return this.getPojosByProperties( conditions );
	}

	public LandingSolutionValue read( Long solutionValueId )
	{
		Validate.notNull( solutionValueId );

		return getPojoByPK( solutionValueId, LandingSolutionValue.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<LandingSolutionValue> getPojoClass()
	{
		return LandingSolutionValue.class;
	}
}
