package com.megabank.olp.common.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.common.service.bean.message.MessageResBean;

@SpringBootTest
@ContextConfiguration( classes = CommonConfig.class )
public class MessageServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private MessageService service;

	@Test
	public void getErrorMessage()
	{
		String url = "open/loan/apply/checkLoanApplyExisted";

		MessageResBean resBean = service.getErrorMessage( url );

		logger.info( "resBean:{}", resBean );
	}

}
