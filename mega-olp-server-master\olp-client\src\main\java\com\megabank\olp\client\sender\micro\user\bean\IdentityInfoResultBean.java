package com.megabank.olp.client.sender.micro.user.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class IdentityInfoResultBean extends BaseBean
{
	private Long validatedIdentityId;

	private String userType;

	private String userSubType;

	private String identityType;

	private Long refIdentityId;

	private String idNo;

	private Date birthDate;

	private String mobileNumber;

	private String clientAddress;

	public IdentityInfoResultBean()
	{
		// default constructor
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getClientAddress()
	{
		return clientAddress;
	}

	public String getIdentityType()
	{
		return identityType;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public Long getRefIdentityId()
	{
		return refIdentityId;
	}

	public String getUserSubType()
	{
		return userSubType;
	}

	public String getUserType()
	{
		return userType;
	}

	public Long getValidatedIdentityId()
	{
		return validatedIdentityId;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setClientAddress( String clientAddress )
	{
		this.clientAddress = clientAddress;
	}

	public void setIdentityType( String identityType )
	{
		this.identityType = identityType;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setRefIdentityId( Long refIdentityId )
	{
		this.refIdentityId = refIdentityId;
	}

	public void setUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}

	public void setUserType( String userType )
	{
		this.userType = userType;
	}

	public void setValidatedIdentityId( Long validatedIdentityId )
	{
		this.validatedIdentityId = validatedIdentityId;
	}

}
