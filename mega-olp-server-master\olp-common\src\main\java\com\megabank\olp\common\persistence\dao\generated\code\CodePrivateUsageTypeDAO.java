package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodePrivateUsageType;

import org.springframework.stereotype.Repository;

/**
 * The CodePrivateUsageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodePrivateUsageTypeDAO extends BasePojoDAO<CodePrivateUsageType, String>
{
	@Override
	protected Class<CodePrivateUsageType> getPojoClass()
	{
		return CodePrivateUsageType.class;
	}
}
