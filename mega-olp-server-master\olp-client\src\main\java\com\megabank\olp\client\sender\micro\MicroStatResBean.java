package com.megabank.olp.client.sender.micro;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class MicroStatResBean<T> extends BaseBean
{
	@JsonProperty( "errorMsg" )
	private MicroErrorMsgBean errorMsgBean;

	private String errorCode;

	private Long errorLogId;

	private T result;

	private String stat;

	public MicroStatResBean()
	{}

	public String getErrorCode()
	{
		return errorCode;
	}

	public Long getErrorLogId()
	{
		return errorLogId;
	}

	public MicroErrorMsgBean getErrorMsgBean()
	{
		return errorMsgBean;
	}

	public T getResult()
	{
		return result;
	}

	public String getStat()
	{
		return stat;
	}

	public void setErrorCode( String errorCode )
	{
		this.errorCode = errorCode;
	}

	public void setErrorLogId( Long errorLogId )
	{
		this.errorLogId = errorLogId;
	}

	public void setErrorMsgBean( MicroErrorMsgBean errorMsgBean )
	{
		this.errorMsgBean = errorMsgBean;
	}

	public void setResult( T result )
	{
		this.result = result;
	}

	public void setStat( String stat )
	{
		this.stat = stat;
	}
}
