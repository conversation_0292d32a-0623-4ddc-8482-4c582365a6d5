package com.megabank.olp.client.sender.micro.user;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.user.bean.UserTypesArgBean;

@Component
public class UserTypesClient extends BaseUserClient<UserTypesArgBean, List<String>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return String.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/user/getUserTypes";
	}
}
