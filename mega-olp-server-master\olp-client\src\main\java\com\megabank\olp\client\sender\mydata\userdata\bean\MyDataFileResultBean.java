package com.megabank.olp.client.sender.mydata.userdata.bean;

import java.util.Collections;
import java.util.List;

import com.megabank.olp.client.sender.mydata.BaseMyDataResultBean;

public class MyDataFileResultBean extends BaseMyDataResultBean
{
	private String errorCode;

	private int waitSec;

	private List<MyDataFileBean> myDataFileBeans = Collections.emptyList();

	public MyDataFileResultBean()
	{}

	public String getErrorCode()
	{
		return errorCode;
	}

	public List<MyDataFileBean> getMyDataFileBeans()
	{
		return myDataFileBeans;
	}

	public int getWaitSec()
	{
		return waitSec;
	}

	public void setErrorCode( String errorCode )
	{
		this.errorCode = errorCode;
	}

	public void setMyDataFileBeans( List<MyDataFileBean> myDataFileBeans )
	{
		this.myDataFileBeans = myDataFileBeans;
	}

	public void setWaitSec( int waitSec )
	{
		this.waitSec = waitSec;
	}

}
