package com.megabank.olp.client.sender.sms.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "msgNo", "branchNo", "businessType", "customerId", "dupId", "phoneNo", "regTime", "regCancel", "priority", "msgContent" } )
public class ItemBean extends BaseBean
{
	/**
	 * 編號
	 */
	@XmlElement( name = "MsgNo" )
	private String msgNo;

	/**
	 * 分行別
	 */
	@XmlElement( name = "BranchNo" )
	private String branchNo;

	/**
	 * 業務別(固定使用MMSG)
	 */
	@XmlElement( name = "BusinessType" )
	private String businessType = "PLON";

	/**
	 * 客戶ID
	 */
	@XmlElement( name = "CustomerID" )
	private String customerId;

	/**
	 * 重覆碼
	 */
	@XmlElement( name = "DupID" )
	private String dupId = "";

	/**
	 * 中心傳送的手機號碼
	 */
	@XmlElement( name = "PhoneNo" )
	private String phoneNo;

	/**
	 * 預約時間,若為預約簡訊則填入預計發送日期時間
	 */
	@XmlElement( name = "RegTime" )
	private String regTime = "";

	/**
	 * 取消預約:Y/N(固定N)
	 */
	@XmlElement( name = "RegCancel" )
	private String regCancel = "N";

	/**
	 * 優先順序:
	 * 一般通知簡訊請帶10
	 * 具時效性之簡訊請帶1
	 */
	@XmlElement( name = "Priority" )
	private String priority = "10";

	/**
	 * 簡訊內容
	 */
	@XmlElement( name = "MsgContent" )
	private String msgContent;

	public ItemBean()
	{}

	public String getBranchNo()
	{
		return branchNo;
	}

	public String getBusinessType()
	{
		return businessType;
	}

	public String getCustomerId()
	{
		return customerId;
	}

	public String getDupId()
	{
		return dupId;
	}

	public String getMsgContent()
	{
		return msgContent;
	}

	public String getMsgNo()
	{
		return msgNo;
	}

	public String getPhoneNo()
	{
		return phoneNo;
	}

	public String getPriority()
	{
		return priority;
	}

	public String getRegCancel()
	{
		return regCancel;
	}

	public String getRegTime()
	{
		return regTime;
	}

	public void setBranchNo( String branchNo )
	{
		this.branchNo = branchNo;
	}

	public void setBusinessType( String businessType )
	{
		this.businessType = businessType;
	}

	public void setCustomerId( String customerId )
	{
		this.customerId = customerId;
	}

	public void setDupId( String dupId )
	{
		this.dupId = dupId;
	}

	public void setMsgContent( String msgContent )
	{
		this.msgContent = msgContent;
	}

	public void setMsgNo( String msgNo )
	{
		this.msgNo = msgNo;
	}

	public void setPhoneNo( String phoneNo )
	{
		this.phoneNo = phoneNo;
	}

	public void setPriority( String priority )
	{
		this.priority = priority;
	}

	public void setRegCancel( String regCancel )
	{
		this.regCancel = regCancel;
	}

	public void setRegTime( String regTime )
	{
		this.regTime = regTime;
	}
}
