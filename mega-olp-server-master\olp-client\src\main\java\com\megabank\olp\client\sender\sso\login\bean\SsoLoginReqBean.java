/**
 *
 */
package com.megabank.olp.client.sender.sso.login.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.client.sender.sso.BaseSsoReqBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoLoginReqBean extends BaseSsoReqBean
{

	private String clientIxd;

	private String clientIp;

	@JsonProperty( "rqData" )
	private SsoLoginReqDataBean reqDataBean;

	public SsoLoginReqBean()
	{}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getClientIxd()
	{
		return clientIxd;
	}

	public SsoLoginReqDataBean getReqDataBean()
	{
		return reqDataBean;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setClientIxd( String clientIxd )
	{
		this.clientIxd = clientIxd;
	}

	public void setReqDataBean( SsoLoginReqDataBean reqDataBean )
	{
		this.reqDataBean = reqDataBean;
	}

}
