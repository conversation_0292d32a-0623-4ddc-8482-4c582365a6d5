package com.megabank.olp.client.sender.monitor;

import java.io.IOException;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.Iterator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import com.megabank.olp.base.exception.MyViolationException;
import com.megabank.olp.client.config.ClientPropertyBean;
import com.megabank.olp.client.utility.bean.EmptyArgBean;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public abstract class BaseMonitorClient<TArg, TReq>
{

	public static final String SYSTEM_NAME = "monitor";

	private final Logger logger = LogManager.getLogger( getClass() );

	@Value( "${openshift.location}" )
	protected String openshiftLocation;

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private SystemService systemService;

	@Autowired
	protected ClientPropertyBean propertyBean;

	private final Class reqClass;

	public BaseMonitorClient()
	{
		reqClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseMonitorClient.class )[ 1 ];
	}

	/**
	 *
	 * @param argBean can be null if there is no any arguments binding to the HTTP request
	 * @return
	 */
	@SuppressWarnings( { "unchecked" } )
	public void send( TArg argBean )
	{
		if( argBean != null )
			validate( argBean );

		TReq reqBean = transArg2ReqToNull( argBean );

		HttpHeaders httpHeaders = getReqHeaders( argBean );
		httpHeaders.set( "system-name", getSystemName() );

		try
		{
			String url = getApiURI( argBean ).toString();

			String requestBody = transReq2ReqBody( reqBean );

			logger.debug( "url:{}", url );
			logger.debug( "headerValues:{}", httpHeaders.toString() );
			logger.debug( "requstBody:{}", requestBody );

			HttpEntity<String> reqHttpEntity = new HttpEntity<>( requestBody, httpHeaders );

			ResponseEntity<String> resEntity = restTemplate.exchange( url, getHttpMethod(), reqHttpEntity, getResType() );

			String resBody = resEntity.getBody();

			logger.debug( "resBody:{}", resBody );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-client", "連結 監控系統錯誤" );

		}

	}

	/**
	 *
	 * @param argBean
	 * @return
	 */
	private HttpHeaders getReqHeaders( TArg argBean )
	{
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add( HttpHeaders.CONTENT_TYPE, "text/xml; charset=utf-8" );
		httpHeaders.add( "x-mock-match-request-headers", "simulator" );
		httpHeaders.add( "simulator", getSimulatorCode( argBean ) );

		return httpHeaders;
	}

	private TReq transArg2ReqToNull( TArg argBean )
	{
		if( argBean instanceof EmptyArgBean )
			return null;

		return transArg2Req( argBean );
	}

	/**
	 * @param reqBean
	 */
	private void validate( TArg argBean )
	{
		StringBuilder builder = new StringBuilder();

		int invalidCount = 0;

		Iterator<ConstraintViolation<TArg>> iterator = Validation.buildDefaultValidatorFactory().getValidator().validate( argBean ).iterator();

		while( iterator.hasNext() )
		{
			ConstraintViolation<TArg> violation = iterator.next();

			if( invalidCount != 0 )
				builder.append( "," );

			builder.append( violation.getMessage() );
			invalidCount++;
		}

		if( invalidCount > 0 )
			throw new MyViolationException( builder.toString() );
	}

	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlThirdpartyMonitor() + getSuffixUrl() );
	}

	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.POST;
	}

	/**
	 *
	 * @param resEntity
	 * @return
	 */
	protected HttpHeaders getResHeaders( ResponseEntity resEntity )
	{
		return resEntity.getHeaders();
	}

	protected Class getResType()
	{
		return String.class;
	}

	protected abstract String getSimulatorCode( TArg argBean );

	protected abstract String getSuffixUrl();

	protected String getSystemName()
	{
		return SYSTEM_NAME;
	}

	/**
	 *
	 * @param argBean
	 * @return
	 */
	protected abstract TReq transArg2Req( TArg argBean );

	/**
	 *
	 * @param reqBean
	 * @return
	 * @throws IOException
	 * @throws JAXBException
	 */
	protected String transReq2ReqBody( TReq reqBean ) throws JAXBException
	{
		StringWriter writer = new StringWriter();

		JAXBContext context = JAXBContext.newInstance( reqClass );

		Marshaller marshaller = context.createMarshaller();

		marshaller.setProperty( Marshaller.JAXB_FORMATTED_OUTPUT, true );

		marshaller.marshal( reqBean, writer );

		return writer.toString();
	}

}
