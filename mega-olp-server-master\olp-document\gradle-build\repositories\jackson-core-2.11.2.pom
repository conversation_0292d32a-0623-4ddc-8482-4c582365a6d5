<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.11.2</version>
  </parent>

  <groupId>com.fasterxml.jackson.core</groupId>
  <artifactId>jackson-core</artifactId>
  <name>Jackson-core</name>
  <version>2.11.2</version>
  <packaging>bundle</packaging>
  <description>Core Jackson processing abstractions (aka Streaming API), implementation for JSON</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <inceptionYear>2008</inceptionYear>

  <url>https://github.com/FasterXML/jackson-core</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-core.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-core.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-core</url>    
    <tag>jackson-core-2.11.2</tag>
  </scm>

  <properties>
    <!-- 04-Mar-2019, tatu: Retain Java6/JDK1.6 compatibility for annotations for Jackson 2.x,
             but use Moditect to get JDK9+ module info support; need newer bundle plugin as well
      -->
    <javac.src.version>1.6</javac.src.version>
    <javac.target.version>1.6</javac.target.version>

    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>

    <osgi.export>com.fasterxml.jackson.core;version=${project.version},
com.fasterxml.jackson.core.*;version=${project.version}
    </osgi.export>

    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/core/json</packageVersion.dir>
    <packageVersion.package>${project.groupId}.json</packageVersion.package>
  </properties>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

  <build>
    <plugins>

      <!-- 26-Aug-2019, tatu: JaCoCo for code coverage -->
      <plugin>
	<groupId>org.jacoco</groupId>
	<artifactId>jacoco-maven-plugin</artifactId>
	<version>0.8.4</version>
	<executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- Important: enable enforcer plug-in: -->
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
	<executions> <!-- or?  combine.children="merge"> -->
          <execution>
            <id>enforce-properties</id>
	        <phase>validate</phase>
            <goals><goal>enforce</goal></goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${version.plugin.surefire}</version>
        <configuration>
          <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
          <excludes>
            <exclude>**/failing/**/*.java</exclude>
          </excludes>
<!-- 13-Apr-2018, tatu: for debugging [core#400]
          <systemPropertyVariables>
<com.fasterxml.jackson.core.util.BufferRecyclers.trackReusableBuffers>true</com.fasterxml.jackson.core.util.BufferRecyclers.trackReusableBuffers>
          </systemPropertyVariables>
-->
        </configuration>
      </plugin>
      <!-- settings are fine, but needed to trigger execution! -->
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
      </plugin>

      <!--  04-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
