/**
 *
 */
package com.megabank.olp.client.sender.sso.custinfo.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.client.sender.sso.BaseSsoResBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoCustInfoResBean extends BaseSsoResBean
{
	private String token;

	private String warnDisplay;

	private String warnMessage;

	private String errorField;

	@JsonProperty( "rsData" )
	private SsoCustInfoRespDataBean respDataBean;

	private Boolean contactAgent;

	private String navigatorType;

	public SsoCustInfoResBean()
	{}

	public Boolean getContactAgent()
	{
		return contactAgent;
	}

	public String getErrorField()
	{
		return errorField;
	}

	public String getNavigatorType()
	{
		return navigatorType;
	}

	public SsoCustInfoRespDataBean getRespDataBean()
	{
		return respDataBean;
	}

	public String getToken()
	{
		return token;
	}

	public String getWarnDisplay()
	{
		return warnDisplay;
	}

	public String getWarnMessage()
	{
		return warnMessage;
	}

	public void setContactAgent( Boolean contactAgent )
	{
		this.contactAgent = contactAgent;
	}

	public void setErrorField( String errorField )
	{
		this.errorField = errorField;
	}

	public void setNavigatorType( String navigatorType )
	{
		this.navigatorType = navigatorType;
	}

	public void setRespDataBean( SsoCustInfoRespDataBean respDataBean )
	{
		this.respDataBean = respDataBean;
	}

	public void setToken( String token )
	{
		this.token = token;
	}

	public void setWarnDisplay( String warnDisplay )
	{
		this.warnDisplay = warnDisplay;
	}

	public void setWarnMessage( String warnMessage )
	{
		this.warnMessage = warnMessage;
	}

}
