package com.megabank.olp.client.service.otp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.otp.getted.OtpGettedClient;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedArgBean;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedResultBean;
import com.megabank.olp.client.sender.otp.verified.OtpVerifiedClient;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedArgBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "uat", "prod" } )
public class OtpSenderServiceImp implements OtpSenderService
{

	@Autowired
	private OtpVerifiedClient otpVerifiedClient;

	@Autowired
	private OtpGettedClient otpGettedClient;

	@Override
	public OtpGettedResultBean getOtp( String mobileNumber )
	{
		OtpGettedArgBean argBean = new OtpGettedArgBean();
		argBean.setMobileNumber( mobileNumber );

		return otpGettedClient.send( argBean );
	}

	@Override
	public OtpVerifiedResultBean verifyOtp( String mobileNumber, String captcha )
	{
		OtpVerifiedArgBean argBean = new OtpVerifiedArgBean();
		argBean.setCaptcha( captcha );
		argBean.setMobileNumber( mobileNumber );

		return otpVerifiedClient.send( argBean );
	}

}
