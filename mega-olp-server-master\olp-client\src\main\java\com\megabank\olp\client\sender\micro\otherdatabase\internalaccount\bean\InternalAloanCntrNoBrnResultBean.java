/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import java.util.Collections;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */

public class InternalAloanCntrNoBrnResultBean extends BaseBean
{

	private List<String> cntrNoBrnList = Collections.emptyList();

	public InternalAloanCntrNoBrnResultBean()
	{}

	public List<String> getCntrNoBrnList()
	{
		return cntrNoBrnList;
	}

	public void setCntrNoBrnList( List<String> cntrNoBrnList )
	{
		this.cntrNoBrnList = cntrNoBrnList;
	}

}
