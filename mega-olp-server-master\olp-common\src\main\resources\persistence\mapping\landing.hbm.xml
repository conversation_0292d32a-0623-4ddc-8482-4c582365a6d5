<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<sql-query name="landing.getInUseSolution">
		<return-scalar column="solutionId" type="long"/>
		<![CDATA[
		SELECT solution.solution_id solutionId
		FROM landing_solution solution
		JOIN landing_template template ON solution.template_id=template.template_id
		LEFT JOIN (SELECT solution_id 
						,ROW_NUMBER() OVER(PARTITION BY template_id ORDER BY apply_date DESC) as row_number
					FROM landing_solution
					WHERE deleted = 0 and apply_date <= :currentTime ) AS T1 ON solution.solution_id=T1.solution_id
		WHERE solution.deleted = 0
		AND template.template_type = coalesce( :templateType, template.template_type )
		AND template.name LIKE coalesce( :templateName, template.name )
		AND T1.row_number = 1
		]]>
	</sql-query>
	
</hibernate-mapping>