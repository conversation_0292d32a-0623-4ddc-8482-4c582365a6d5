package com.megabank.olp.client.sender.micro.schedule.mydata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.schedule.BaseScheduleClient;
import com.megabank.olp.client.sender.micro.schedule.mydata.bean.MyDataTaskScheduledArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class MyDataTaskScheduledClient extends BaseScheduleClient<MyDataTaskScheduledArgBean, String>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return String.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/mydata/getUserData";
	}

}
