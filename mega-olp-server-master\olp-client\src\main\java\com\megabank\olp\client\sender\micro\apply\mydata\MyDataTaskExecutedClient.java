package com.megabank.olp.client.sender.micro.apply.mydata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseScheduleApplyClient;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MyDataTaskExecutedArgBean;

@Component
public class MyDataTaskExecutedClient extends BaseScheduleApplyClient<MyDataTaskExecutedArgBean, String>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/mydata/getUserData";
	}
}
