package com.megabank.olp.client.sender.monitor.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class SoapBodyBean extends BaseBean
{

	@XmlElement( name = "Send" )
	private SendBean sendBean;

	public SoapBodyBean()
	{}

	public SendBean getSendBean()
	{
		return sendBean;
	}

	public void setSendBean( SendBean sendBean )
	{
		this.sendBean = sendBean;
	}

}
