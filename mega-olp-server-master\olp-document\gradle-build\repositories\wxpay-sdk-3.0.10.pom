<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>com.github.tedzhdz</groupId>
	<artifactId>wxpay-sdk</artifactId>
	<version>3.0.10</version>
	<name>wxpay-sdk</name>
	<description>wxpay sdk</description>
	<url>https://github.com/TedZhDZ/wxpay-sdk.git</url>

	<properties>
		<project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>
		<argLine>-Dfile.encoding=UTF-8</argLine>
	</properties>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.3</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.21</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>1.7.21</version>
		</dependency>

	</dependencies>

	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<issueManagement>
		<url>https://github.com/TedZhDZ/wxpay-sdk/issues</url>
		<system>GitHub Issues</system>
	</issueManagement>

	<scm>
		<url>https://github.com/TedZhDZ/wxpay-sdk.git</url>
		<connection>scm:git:https://github.com/TedZhDZ/wxpay-sdk.git</connection>
		<developerConnection>scm:git:https://github.com/TedZhDZ/wxpay-sdk.git</developerConnection>
	</scm>

	<developers>
		<developer>
			<name>Ted Zhang</name>
			<email><EMAIL></email>
			<url>https://github.com/TedZhDZ/wxpay-sdk.git</url>
		</developer>
	</developers>

	<profiles>
		<profile>
			<id>release</id>
			<distributionManagement>
				<snapshotRepository>
					<id>oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots</url>
				</snapshotRepository>
				<repository>
					<id>oss</id>
					<url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
				</repository>
			</distributionManagement>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<version>2.16</version>
						<configuration>
							<forkMode>once</forkMode>
							<argLine>-Dfile.encoding=UTF-8</argLine>
						</configuration>
					</plugin>
					<!-- Source -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>3.0.1</version>
						<executions>
							<execution>
								<phase>package</phase>
								<goals>
									<goal>jar-no-fork</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- Javadoc -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>2.10.4</version>
						<configuration>
							<charset>UTF-8</charset>
							<docencoding>UTF-8</docencoding>
						</configuration>
						<executions>
							<execution>
								<phase>package</phase>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- Gpg Signature -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.6</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

</project>