package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class ResInfoBean extends BaseBean
{
	@XmlElement( name = "StatusCode" )
	private String statusCode;

	@XmlElement( name = "StatusDesc" )
	private String statusDesc;

	@XmlElement( name = "TxnTime" )
	private String txnTime;

	@XmlElement( name = "SrcSystemID" )
	private String srcSystemID;

	@XmlElement( name = "NotificationId" )
	private String notificationId;

	@XmlElement( name = "Category" )
	private String category;

	public ResInfoBean()
	{}

	public String getCategory()
	{
		return category;
	}

	public String getNotificationId()
	{
		return notificationId;
	}

	public String getSrcSystemID()
	{
		return srcSystemID;
	}

	public String getStatusCode()
	{
		return statusCode;
	}

	public String getStatusDesc()
	{
		return statusDesc;
	}

	public String getTxnTime()
	{
		return txnTime;
	}

	public void setCategory( String category )
	{
		this.category = category;
	}

	public void setNotificationId( String notificationId )
	{
		this.notificationId = notificationId;
	}

	public void setSrcSystemID( String srcSystemID )
	{
		this.srcSystemID = srcSystemID;
	}

	public void setStatusCode( String statusCode )
	{
		this.statusCode = statusCode;
	}

	public void setStatusDesc( String statusDesc )
	{
		this.statusDesc = statusDesc;
	}

	public void setTxnTime( String txnTime )
	{
		this.txnTime = txnTime;
	}

}
