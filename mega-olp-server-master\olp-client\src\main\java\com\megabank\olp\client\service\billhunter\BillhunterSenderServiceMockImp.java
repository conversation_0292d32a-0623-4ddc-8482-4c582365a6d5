package com.megabank.olp.client.service.billhunter;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderArgBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev" } )
public class BillhunterSenderServiceMockImp implements BillhunterSenderService
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public BillhunterSenderResultBean send( BillhunterSenderArgBean argBean )
	{
		BillhunterSenderResultBean resultBean = new BillhunterSenderResultBean();
		resultBean.setSuccess( true );

		return resultBean;
	}

}
