/**
 *
 */
package com.megabank.olp.client.sender.sso.login.bean;

import com.megabank.olp.client.sender.sso.BaseSsoResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoLoginResultBean extends BaseSsoResultBean
{
	private String sys;

	private String code;

	private String desc;

	private String resource;

	private String clientTime;

	private String sessionIxd;

	private String accessToken;

	public SsoLoginResultBean()
	{}

	public String getAccessToken()
	{
		return accessToken;
	}

	public String getClientTime()
	{
		return clientTime;
	}

	public String getCode()
	{
		return code;
	}

	public String getDesc()
	{
		return desc;
	}

	public String getResource()
	{
		return resource;
	}

	public String getSessionIxd()
	{
		return sessionIxd;
	}

	public String getSys()
	{
		return sys;
	}

	public void setAccessToken( String accessToken )
	{
		this.accessToken = accessToken;
	}

	public void setClientTime( String clientTime )
	{
		this.clientTime = clientTime;
	}

	public void setCode( String code )
	{
		this.code = code;
	}

	public void setDesc( String desc )
	{
		this.desc = desc;
	}

	public void setResource( String resource )
	{
		this.resource = resource;
	}

	public void setSessionIxd( String sessionIxd )
	{
		this.sessionIxd = sessionIxd;
	}

	public void setSys( String sys )
	{
		this.sys = sys;
	}

}
