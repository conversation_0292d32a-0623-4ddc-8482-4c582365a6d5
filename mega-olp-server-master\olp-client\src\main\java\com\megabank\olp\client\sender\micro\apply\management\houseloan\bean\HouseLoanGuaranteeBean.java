package com.megabank.olp.client.sender.micro.apply.management.houseloan.bean;

import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanGuaranteeBean extends BaseBean
{
	private String guarantyReason;

	private String otherGuarantyReason;

	private Boolean isCohabiting;

	private String relationBorrowerType;

	public HouseLoanGuaranteeBean()
	{
		// default constructor
	}

	public String getGuarantyReason()
	{
		return guarantyReason;
	}

	public Boolean getIsCohabiting()
	{
		return isCohabiting;
	}

	public String getOtherGuarantyReason()
	{
		return otherGuarantyReason;
	}

	public String getRelationBorrowerType()
	{
		return relationBorrowerType;
	}

	public void setGuarantyReason( String guarantyReason )
	{
		this.guarantyReason = guarantyReason;
	}

	public void setIsCohabiting( Boolean isCohabiting )
	{
		this.isCohabiting = isCohabiting;
	}

	public void setOtherGuarantyReason( String otherGuarantyReason )
	{
		this.otherGuarantyReason = otherGuarantyReason;
	}

	public void setRelationBorrowerType( String relationBorrowerType )
	{
		this.relationBorrowerType = relationBorrowerType;
	}

}
