<?xml version='1.0' encoding='UTF-8'?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>org.jboss</groupId>
  <version>12</version>
  <artifactId>jboss-parent</artifactId>

  <packaging>pom</packaging>

  <name>JBoss Parent POM</name>
  <description>Parent POM for JBoss projects. Provides default project build configuration.</description>
  <url>http://www.jboss.org</url>

  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.jboss.org/</url>
  </issueManagement>

  <scm>
    <connection>scm:git:**************:jboss/jboss-parent-pom.git</connection>
    <developerConnection>scm:git:**************:jboss/jboss-parent-pom.git</developerConnection>
    <url>http://github.com/jboss/jboss-parent-pom</url>
  </scm>

  <developers>
    <developer>
      <id>jboss.org</id>
      <name>JBoss.org Community</name>
      <organization>JBoss.org</organization>
      <organizationUrl>http://www.jboss.org</organizationUrl>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>JBoss User List</name>
      <subscribe>https://lists.jboss.org/mailman/listinfo/jboss-user</subscribe>
      <unsubscribe>https://lists.jboss.org/mailman/listinfo/jboss-user</unsubscribe>
      <archive>http://lists.jboss.org/pipermail/jboss-user/</archive>
    </mailingList>
    <mailingList>
      <name>JBoss Developer List</name>
      <subscribe>https://lists.jboss.org/mailman/listinfo/jboss-development</subscribe>
      <unsubscribe>https://lists.jboss.org/mailman/listinfo/jboss-development</unsubscribe>
      <archive>http://lists.jboss.org/pipermail/jboss-development/</archive>
    </mailingList>
  </mailingLists>

  <licenses>
    <license>
      <name>Public Domain</name>
      <url>http://repository.jboss.org/licenses/cc0-1.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>JBoss by Red Hat</name>
    <url>http://www.jboss.org</url>
  </organization>

  <properties>
    <!-- **************** -->
    <!-- Plugins versions -->
    <!-- **************** -->
    <version.antrun.plugin>1.7</version.antrun.plugin>
    <version.apt.plugin>1.0-alpha-5</version.apt.plugin>
    <version.assembly.plugin>2.4</version.assembly.plugin>
    <version.buildhelper.plugin>1.7</version.buildhelper.plugin>
    <version.buildnumber.plugin>1.2</version.buildnumber.plugin>
    <version.bundle.plugin>2.3.7</version.bundle.plugin>
    <version.checkstyle.plugin>2.10</version.checkstyle.plugin>
    <version.clean.plugin>2.5</version.clean.plugin>
    <version.clover2.plugin>3.1.11</version.clover2.plugin>
    <version.cobertura.plugin>2.5.2</version.cobertura.plugin>
    <version.compiler.plugin>3.1</version.compiler.plugin>
    <version.dependency.plugin>2.8</version.dependency.plugin>
    <version.deploy.plugin>2.7</version.deploy.plugin>
    <version.ear.plugin>2.8</version.ear.plugin>
    <version.eclipse.plugin>2.9</version.eclipse.plugin>
    <version.org.eclipse.m2e.lifecycle-mapping>1.0.0</version.org.eclipse.m2e.lifecycle-mapping>
    <version.ejb.plugin>2.3</version.ejb.plugin>
    <version.enforcer.plugin>1.3.1</version.enforcer.plugin>
    <version.findbugs.plugin>2.5.2</version.findbugs.plugin>
    <version.gpg.plugin>1.4</version.gpg.plugin>
    <version.help.plugin>2.2</version.help.plugin>
    <version.idea.plugin>2.2</version.idea.plugin>
    <version.idlj.plugin>1.2.1</version.idlj.plugin>
    <version.injection.plugin>1.0.2</version.injection.plugin>
    <version.install.plugin>2.4</version.install.plugin>
    <version.jar.plugin>2.4</version.jar.plugin>
    <version.javacc.plugin>2.6</version.javacc.plugin>
    <version.javadoc.plugin>2.9.1</version.javadoc.plugin>
    <version.javancss.plugin>2.0</version.javancss.plugin>
    <version.jdepend.plugin>2.0-beta-2</version.jdepend.plugin>
    <version.jxr.plugin>2.3</version.jxr.plugin>
    <version.license.plugin>1.3</version.license.plugin>
    <version.pir.plugin>2.7</version.pir.plugin><!-- maven-project-info-reports-plugins -->
    <version.plugin.plugin>3.1</version.plugin.plugin>
    <version.pmd.plugin>3.0.1</version.pmd.plugin>
    <version.rar.plugin>2.2</version.rar.plugin>
    <version.release.plugin>2.4.1</version.release.plugin>
    <version.resources.plugin>2.6</version.resources.plugin>
    <version.shade.plugin>2.1</version.shade.plugin>
    <version.site.plugin>3.3</version.site.plugin>
    <version.sonar.plugin>2.0</version.sonar.plugin>
    <version.source.plugin>2.2.1</version.source.plugin>
    <version.surefire.plugin>2.16</version.surefire.plugin>
    <version.failsafe.plugin>${version.surefire.plugin}</version.failsafe.plugin>
    <version.taglist.plugin>2.4</version.taglist.plugin>
    <version.war.plugin>2.3</version.war.plugin>


    <!-- ***************** -->
    <!-- Repository Deployment URLs -->
    <!-- ***************** -->
    <jboss.releases.repo.url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/</jboss.releases.repo.url>
    <jboss.snapshots.repo.url>https://repository.jboss.org/nexus/content/repositories/snapshots/</jboss.snapshots.repo.url>

    <!-- ************** -->
    <!-- Build settings -->
    <!-- ************** -->

    <!-- Cross plugins settings -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- maven-compiler-plugin -->
    <maven.compiler.target>1.7</maven.compiler.target>
    <maven.compiler.source>1.7</maven.compiler.source>

    <!--
        Options to override the compiler arguments directly on the compiler arument line to separate between what
        the IDE understands as the source level and what the Maven compiler actually use.
    -->
    <maven.compiler.argument.target>${maven.compiler.target}</maven.compiler.argument.target>
    <maven.compiler.argument.source>${maven.compiler.source}</maven.compiler.argument.source>

    <!-- maven-enforcer-plugin -->
    <maven.min.version>3.0.3</maven.min.version>
    <jdk.min.version>${maven.compiler.source}</jdk.min.version>

    <!-- maven-idea-plugin & maven-eclipse-plugin -->
    <downloadSources>true</downloadSources>

    <!-- maven-pmd-plugin -->
    <targetJdk>${maven.compiler.target}</targetJdk>

    <!-- maven-release-plugin -->
    <useReleaseProfile>false</useReleaseProfile>
    <arguments>-Pjboss-release</arguments>

    <!-- maven-assembly-plugin -->
    <sourceReleaseAssemblyDescriptor>source-release</sourceReleaseAssemblyDescriptor>

    <!-- tools.jar location, this needs to be overriden on OSX -->
  </properties>

  <prerequisites>
    <maven>${maven.min.version}</maven>
  </prerequisites>


  <build>

    <pluginManagement>
      <!-- All plugins ordered by shortname (antrun, assembly ...) -->
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${version.antrun.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>apt-maven-plugin</artifactId>
          <version>${version.apt.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${version.assembly.plugin}</version>
          <configuration>
            <archive>
              <index>true</index>
              <manifest>
                <addDefaultSpecificationEntries>
                  true
                </addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>
                  true
                </addDefaultImplementationEntries>
              </manifest>
              <manifestEntries>
                <Implementation-URL>${project.url}</Implementation-URL>
                <Java-Version>${java.version}</Java-Version>
                <Java-Vendor>${java.vendor}</Java-Vendor>
                <Os-Name>${os.name}</Os-Name>
                <Os-Arch>${os.arch}</Os-Arch>
                <Os-Version>${os.version}</Os-Version>
                <Scm-Url>${project.scm.url}</Scm-Url>
                <Scm-Connection>${project.scm.connection}</Scm-Connection>
                <Scm-Revision>${buildNumber}</Scm-Revision>
                <Build-Timestamp>${build.datetime}</Build-Timestamp>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${version.buildhelper.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>${version.buildnumber.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${version.bundle.plugin}</version>
          <configuration>
            <archive>
              <index>true</index>
              <manifest>
                <addDefaultSpecificationEntries>
                  true
                </addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>
                  true
                </addDefaultImplementationEntries>
              </manifest>
              <manifestEntries>
                <Implementation-URL>${project.url}</Implementation-URL>
                <Java-Version>${java.version}</Java-Version>
                <Java-Vendor>${java.vendor}</Java-Vendor>
                <Os-Name>${os.name}</Os-Name>
                <Os-Arch>${os.arch}</Os-Arch>
                <Os-Version>${os.version}</Os-Version>
                <Scm-Url>${project.scm.url}</Scm-Url>
                <Scm-Connection>${project.scm.connection}</Scm-Connection>
              </manifestEntries>
            </archive>
            <instructions>
              <Scm-Revision>${buildNumber}</Scm-Revision>
              <Build-Timestamp>${build.datetime}</Build-Timestamp>
            </instructions>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${version.checkstyle.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${version.clean.plugin}</version>
        </plugin>
        <plugin>
          <groupId>com.atlassian.maven.plugins</groupId>
          <artifactId>maven-clover2-plugin</artifactId>
          <version>${version.clover2.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>${version.cobertura.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${version.compiler.plugin}</version>
          <configuration>
            <showDeprecation>true</showDeprecation>
            <showWarnings>true</showWarnings>
            <compilerArguments>
              <source>${maven.compiler.argument.source}</source>
              <target>${maven.compiler.argument.target}</target>
            </compilerArguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${version.dependency.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${version.deploy.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-ear-plugin</artifactId>
          <version>${version.ear.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-eclipse-plugin</artifactId>
          <version>${version.eclipse.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-ejb-plugin</artifactId>
          <version>${version.ejb.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${version.enforcer.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${version.failsafe.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>${version.findbugs.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${version.gpg.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-help-plugin</artifactId>
          <version>${version.help.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-idea-plugin</artifactId>
          <version>${version.idea.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>idlj-maven-plugin</artifactId>
          <version>${version.idlj.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.jboss.maven.plugins</groupId>
          <artifactId>maven-injection-plugin</artifactId>
          <version>${version.injection.plugin}</version>
          <executions>
            <execution>
              <phase>compile</phase>
              <goals>
                <goal>bytecode</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${version.install.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${version.jar.plugin}</version>
          <configuration>
            <archive>
              <index>true</index>
              <manifest>
                <addDefaultSpecificationEntries>
                  true
                </addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>
                  true
                </addDefaultImplementationEntries>
              </manifest>
              <manifestEntries>
                <Implementation-URL>${project.url}</Implementation-URL>
                <Java-Version>${java.version}</Java-Version>
                <Java-Vendor>${java.vendor}</Java-Vendor>
                <Os-Name>${os.name}</Os-Name>
                <Os-Arch>${os.arch}</Os-Arch>
                <Os-Version>${os.version}</Os-Version>
                <Scm-Url>${project.scm.url}</Scm-Url>
                <Scm-Connection>${project.scm.connection}</Scm-Connection>
                <Scm-Revision>${buildNumber}</Scm-Revision>
                <Build-Timestamp>${build.datetime}</Build-Timestamp>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>javacc-maven-plugin</artifactId>
          <version>${version.javacc.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${version.javadoc.plugin}</version>
          <configuration>
            <header><![CDATA[<b>${project.name} ${project.version}</b>]]></header>
            <footer><![CDATA[<b>${project.name} ${project.version}</b>]]></footer>
            <archive>
              <index>true</index>
              <manifest>
                <addDefaultSpecificationEntries>
                  true
                </addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>
                  true
                </addDefaultImplementationEntries>
              </manifest>
              <manifestEntries>
                <Implementation-URL>${project.url}</Implementation-URL>
                <Java-Version>${java.version}</Java-Version>
                <Java-Vendor>${java.vendor}</Java-Vendor>
                <Os-Name>${os.name}</Os-Name>
                <Os-Arch>${os.arch}</Os-Arch>
                <Os-Version>${os.version}</Os-Version>
                <Scm-Url>${project.scm.url}</Scm-Url>
                <Scm-Connection>${project.scm.connection}</Scm-Connection>
                <Scm-Revision>${buildNumber}</Scm-Revision>
                <Build-Timestamp>${build.datetime}</Build-Timestamp>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>javancss-maven-plugin</artifactId>
          <version>${version.javancss.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>jdepend-maven-plugin</artifactId>
          <version>${version.jdepend.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${version.jxr.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${version.license.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${version.plugin.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${version.pmd.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-rar-plugin</artifactId>
          <version>${version.rar.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${version.release.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${version.resources.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${version.shade.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${version.site.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>sonar-maven-plugin</artifactId>
          <version>${version.sonar.plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${version.source.plugin}</version>
          <configuration>
            <archive>
              <index>true</index>
              <manifest>
                <addDefaultSpecificationEntries>
                  true
                </addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>
                  true
                </addDefaultImplementationEntries>
              </manifest>
              <manifestEntries>
                <Implementation-URL>${project.url}</Implementation-URL>
                <Java-Version>${java.version}</Java-Version>
                <Java-Vendor>${java.vendor}</Java-Vendor>
                <Os-Name>${os.name}</Os-Name>
                <Os-Arch>${os.arch}</Os-Arch>
                <Os-Version>${os.version}</Os-Version>
                <Scm-Url>${project.scm.url}</Scm-Url>
                <Scm-Connection>${project.scm.connection}</Scm-Connection>
                <Scm-Revision>${buildNumber}</Scm-Revision>
                <Build-Timestamp>${build.datetime}</Build-Timestamp>
              </manifestEntries>
            </archive>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.plexus</groupId>
              <artifactId>plexus-io</artifactId>
              <version>2.0.10</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${version.surefire.plugin}</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
                <version>${version.surefire.plugin}</version>
            </dependency>
          </dependencies>
          <configuration>
            <systemProperties>
              <java.io.tmpdir>${project.build.directory}</java.io.tmpdir>
            </systemProperties>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${version.war.plugin}</version>
        </plugin>

        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>${version.org.eclipse.m2e.lifecycle-mapping}</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <!-- Configure m2e to execute the manifest goal of the bundle plugin, if present. -->
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <versionRange>[2.3.7,)</versionRange>
                    <goals>
                      <goal>manifest</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute></execute>
                  </action>
                </pluginExecution>
                <!-- Configure m2e to ignore the Maven enforcer plugin -->
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>enforce</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <!-- Configure m2e to ignore the buildnumber-maven-plugin. -->
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <versionRange>[1.0.0,)</versionRange>
                    <goals>
                      <goal>create</goal>
                      <goal>create-timestamp</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>

      </plugins>

    </pluginManagement>

    <plugins>

      <!-- Check for the minimum version of Java and Maven.  Runs during the validate phase. -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java-version</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <message>To build this project JDK ${jdk.min.version} (or greater) is required. Please install it.</message>
                  <version>${jdk.min.version}</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>enforce-maven-version</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <message>To build this project Maven ${maven.min.version} (or greater) is required. Please install it.</message>
                  <version>${maven.min.version}</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Set properties containing the scm revision and build timestamp -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>buildnumber-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>get-build-timestamp</id>
            <phase>initialize</phase>
            <goals>
              <goal>create-timestamp</goal>
            </goals>
            <configuration>
              <!-- Example date: Wed, 4 Jul 2001 12:08:56 -0700 -->
              <timestampFormat>EEE, d MMM yyyy HH:mm:ss Z</timestampFormat>
              <timestampPropertyName>build.datetime</timestampPropertyName>
            </configuration>
          </execution>
          <execution>
            <id>get-scm-revision</id>
            <phase>initialize</phase>
            <goals>
              <goal>create</goal>
            </goals>
            <configuration>
              <doCheck>false</doCheck>
              <doUpdate>false</doUpdate>
              <revisionOnScmFailure>UNKNOWN</revisionOnScmFailure>
              <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Attach source jar to all builds.  Runs during the package phase.-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>

  </build>

  <profiles>

    <!-- 
        The profile jboss-release must be active when a project is released.  The configuration
        in this POM will automatically call this profile if using the maven-release-plugin.
        If the maven-release-plugin is not used during the release, this profile must
        be manually activated.
     -->
    <profile>
      <id>jboss-release</id>
      <build>
        <plugins>
          <!-- Create a source-release artifact that contains the fully buildable 
               project directory source structure.  This should be released to 
               the Maven repository for each JBoss project release. -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.3</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>${sourceReleaseAssemblyDescriptor}</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileFormat>gnu</tarLongFileFormat>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>          
        </plugins>
      </build>
    </profile>

    <!-- 
        This profile can be activated to generate gpg signatures for all build 
        artifacts.  This profile requires that the properties "gpg.keyname" 
        and "gpg.passphrase" are available to the current build.
    -->
    <profile>
      <id>gpg-sign</id>
      <build>
        <plugins>
          <!-- This will sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <useAgent>true</useAgent>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>

  <distributionManagement>
    <repository>
      <id>jboss-releases-repository</id>
      <name>JBoss Releases Repository</name>
      <url>${jboss.releases.repo.url}</url>
    </repository>
    <snapshotRepository>
      <id>jboss-snapshots-repository</id>
      <name>JBoss Snapshots Repository</name>
      <url>${jboss.snapshots.repo.url}</url>
    </snapshotRepository>
  </distributionManagement>

</project>
