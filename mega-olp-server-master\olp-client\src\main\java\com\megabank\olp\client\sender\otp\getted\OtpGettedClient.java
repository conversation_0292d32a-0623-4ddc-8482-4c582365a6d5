package com.megabank.olp.client.sender.otp.getted;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.client.sender.otp.BaseOtpClient;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedArgBean;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedReqBean;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedResBean;
import com.megabank.olp.client.sender.otp.getted.bean.OtpGettedResultBean;
import com.megabank.olp.client.utility.enums.ClientOtpErrorEnum;

@Component
public class OtpGettedClient extends BaseOtpClient<OtpGettedArgBean, OtpGettedReq<PERSON><PERSON>, OtpGetted<PERSON>es<PERSON>ean, OtpGettedResultBean>
{

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000" };
	}

	@Override
	protected ErrorEnum getOtpCommonErrorEnum()
	{
		return ClientOtpErrorEnum.GETTED_SMS_COMMON;
	}

	@Override
	protected OtpGettedResultBean getResultBean( String returnCode )
	{
		return new OtpGettedResultBean();
	}

	@Override
	protected String getSimulatorCode( OtpGettedArgBean argBean )
	{
		String mobileNumber = argBean.getMobileNumber();

		if( StringUtils.startsWith( mobileNumber, "0918000" ) )
			return "S" + StringUtils.right( mobileNumber, 3 );

		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/GetSmsOTP";
	}

	@Override
	protected OtpGettedReqBean transArg2Req( OtpGettedArgBean argBean )
	{
		OtpGettedReqBean reqBean = new OtpGettedReqBean();
		reqBean.setMobileNumber( argBean.getMobileNumber() );
		reqBean.setPrifixMsg( "您好，歡迎使用兆豐銀行貸款服務" );
		reqBean.setValidateTime( "120" );

		return reqBean;
	}

}
