package com.megabank.olp.client.sender.micro.common.landing;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.common.BaseCommonClient;
import com.megabank.olp.client.sender.micro.common.landing.bean.RiskInfoArgBean;
import com.megabank.olp.client.sender.micro.common.landing.bean.RiskInfoResultBean;

@Component
public class RiskInfoClient extends BaseCommonClient<RiskInfoArgBean, RiskInfoResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/open/landing/getRiskInfo";
	}
}
