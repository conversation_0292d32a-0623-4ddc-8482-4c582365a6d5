/**
 *
 */
package com.megabank.olp.common.persistence.dao.generated;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.common.persistence.dao.generated.code.CodeOtherBankDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeOtherBank;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@SpringBootTest
@ContextConfiguration( classes = CommonConfig.class )
public class CodeOtherBankDAOIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private CodeOtherBankDAO codeOtherBankDAO;

	@Test
	public void read()
	{
		CodeOtherBank result = codeOtherBankDAO.read( "008" );
		logger.info( "result:{}", result );
	}

}
