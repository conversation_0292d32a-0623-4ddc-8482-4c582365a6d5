package com.megabank.olp.client.sender.micro.apply.management.signing.iloan;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyBranchBankUpdatedArgBean;

@Component
public class ApplyBranchBankUpdatedILoanClient extends BaseApplyClient<ApplyBranchBankUpdatedArgBean, String>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/apply/iloan/updateBranchBank";
	}
}
