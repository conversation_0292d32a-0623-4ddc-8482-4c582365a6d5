package com.megabank.olp.common.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.common.config.CommonConfig;

@SpringBootTest
@ContextConfiguration( classes = CommonConfig.class )
public class UrlServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private UrlService service;

	@Test
	public void isUrlDisplayed()
	{
		String key = "service.url.signing";

		boolean result = service.isUrlDisplayed( key );

		logger.info( "result:{}", result );
	}

}
