/**
 *
 */
package com.megabank.olp.client.sender.micro.identity;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.identity.bean.AuthBankInfoArgBean;
import com.megabank.olp.client.sender.micro.identity.bean.AuthBankInfoResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

@Component
public class AuthBankInfoClient extends BaseIdentityClient<AuthBankInfoArgBean, AuthBankInfoResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/identity/otherbank/getAuthBankInfo";
	}
}
