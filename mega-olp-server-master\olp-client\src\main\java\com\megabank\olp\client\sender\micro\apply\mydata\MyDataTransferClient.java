package com.megabank.olp.client.sender.micro.apply.mydata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MyDataTransferArgBean;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MydataResultBean;

@Component
public class MyDataTransferClient extends BaseApplyClient<MyDataTransferArgBean, MydataResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/mydata/dataTransfer";
	}
}
