package com.megabank.olp.client.sender.micro.user;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.utility.bean.EmptyArgBean;

@Component
public class CurrentIdentityInfoClient extends BaseUserClient<EmptyArgBean, IdentityInfoResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/user/getCurrentIdentityInfo";
	}
}
