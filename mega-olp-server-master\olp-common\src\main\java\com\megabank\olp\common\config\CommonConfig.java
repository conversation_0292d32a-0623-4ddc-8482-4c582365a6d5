package com.megabank.olp.common.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;

/**
 * @version 1.0
 * <AUTHOR>
 * @company Mega Bank
 * @copyright Copyright (c) 2019
 */

@Configuration
@ComponentScan( { "com.megabank.olp.common.controller", "com.megabank.olp.common.service", "com.megabank.olp.common.persistence.dao" } )
@Import( { BaseServiceConfig.class, ClientServiceConfig.class } )
public class CommonConfig
{

}
