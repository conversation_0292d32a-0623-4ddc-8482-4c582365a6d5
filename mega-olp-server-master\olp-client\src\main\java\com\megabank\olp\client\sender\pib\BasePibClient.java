/**
 *
 */
package com.megabank.olp.client.sender.pib;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;

import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.core5.http.impl.DefaultConnectionReuseStrategy;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.ssl.SSLContexts;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.BaseJsonClient;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public abstract class BasePibClient<TArg extends BasePibArgBean, TReq extends BasePibReqBean, TRes extends BasePibResBean, TResult extends BasePibResultBean>
			extends BaseJsonClient<TArg, TReq, TRes, TResult>
{
	public static final String SYSTEM_NAME = "pib";

	private ClientHttpRequestFactory getSSLRequestFactory()
	{
		try
		{
			TrustStrategy acceptingTrustStrategy = ( X509Certificate[] chain, String authType ) -> true;

			SSLContext sslContext = SSLContexts.custom().loadTrustMaterial( null, acceptingTrustStrategy ).build();

			SSLConnectionSocketFactory csf = SSLConnectionSocketFactoryBuilder.create().setSslContext( sslContext )
						.setHostnameVerifier( myHostnameVerifier ).build();

			HttpClientConnectionManager cm = PoolingHttpClientConnectionManagerBuilder.create().setSSLSocketFactory( csf ).build();

			CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager( cm )
						.setConnectionReuseStrategy( DefaultConnectionReuseStrategy.INSTANCE ).build();

			HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();

			requestFactory.setHttpClient( httpClient );

			return requestFactory;
		}
		catch( Exception exception )
		{
			throw new MyRuntimeException( SystemErrorEnum.INTERNAL, null, exception );
		}
	}

	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlThirdpartyPib() + getSuffixUrl() );
	}

	@Override
	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.POST;
	}

	protected abstract String getSuffixUrl();

	@Override
	protected String getSystemName()
	{
		return SYSTEM_NAME;
	}

	@Override
	protected void setRequestFactory()
	{
		logger.info( "use SSL request" );
		restTemplate.setRequestFactory( getSSLRequestFactory() );
	}
}