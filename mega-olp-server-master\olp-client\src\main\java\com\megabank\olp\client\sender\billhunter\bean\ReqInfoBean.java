package com.megabank.olp.client.sender.billhunter.bean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementRef;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "txnTime", "subject", "severity", "srcSystemID", "notificationId", "category", "contactInfoBean", "notifyMethod",
						"messageBean", "savefile", "savemonth", "attachmentBeans" } )
public class ReqInfoBean extends BaseBean
{
	/**
	 * Mail主旨
	 */
	@XmlElement( name = "Subject" )
	private String subject;

	/**
	 * 傳送時間
	 */
	@XmlElement( name = "TxnTime" )
	private String txnTime = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss:SSSZ" ).format( new Date() );

	/**
	 * 傳送速度 INFO/WARN/ERROR/HIGH
	 */
	@XmlElement( name = "Severity" )
	private String severity = "HIGH";

	/**
	 * 系統代碼
	 */
	@XmlElement( name = "SrcSystemID" )
	private String srcSystemID = "PLOAN";

	/**
	 * 週邊系統唯一值
	 */
	@XmlElement( name = "NotificationId" )
	private String notificationId;

	/**
	 * 帳單類別MailType
	 */
	@XmlElement( name = "Category" )
	private String category;

	@XmlElement( name = "NotifyMethod" )
	private String notifyMethod = "EMAIL";

	/**
	 * 郵件本文
	 */
	@XmlElementRef( name = "Message" )
	private MessageBean messageBean;

	@XmlElement( name = "savefile" )
	private String savefile = "";

	@XmlElement( name = "savemonth" )
	private String savemonth = "";

	@XmlElement( name = "ContactInfo" )
	private ContactInfoBean contactInfoBean;

	@XmlElementWrapper( name = "Attachments" )
	@XmlElement( name = "Attachment" )
	private List<AttachmentBean> attachmentBeans;

	public ReqInfoBean()
	{}

	public List<AttachmentBean> getAttachmentBeans()
	{
		return attachmentBeans;
	}

	public String getCategory()
	{
		return category;
	}

	public ContactInfoBean getContactInfoBean()
	{
		return contactInfoBean;
	}

	public MessageBean getMessageBean()
	{
		return messageBean;
	}

	public String getNotificationId()
	{
		return notificationId;
	}

	public String getNotifyMethod()
	{
		return notifyMethod;
	}

	public String getSavefile()
	{
		return savefile;
	}

	public String getSavemonth()
	{
		return savemonth;
	}

	public String getSeverity()
	{
		return severity;
	}

	public String getSrcSystemID()
	{
		return srcSystemID;
	}

	public String getSubject()
	{
		return subject;
	}

	public String getTxnTime()
	{
		return txnTime;
	}

	public void setAttachmentBeans( List<AttachmentBean> attachmentBeans )
	{
		this.attachmentBeans = attachmentBeans;
	}

	public void setCategory( String category )
	{
		this.category = category;
	}

	public void setContactInfoBean( ContactInfoBean contactInfoBean )
	{
		this.contactInfoBean = contactInfoBean;
	}

	public void setMessageBean( MessageBean messageBean )
	{
		this.messageBean = messageBean;
	}

	public void setNotificationId( String notificationId )
	{
		this.notificationId = notificationId;
	}

	public void setNotifyMethod( String notifyMethod )
	{
		this.notifyMethod = notifyMethod;
	}

	public void setSavefile( String savefile )
	{
		this.savefile = savefile;
	}

	public void setSavemonth( String savemonth )
	{
		this.savemonth = savemonth;
	}

	public void setSeverity( String severity )
	{
		this.severity = severity;
	}

	public void setSrcSystemID( String srcSystemID )
	{
		this.srcSystemID = srcSystemID;
	}

	public void setSubject( String subject )
	{
		this.subject = subject;
	}

	public void setTxnTime( String txnTime )
	{
		this.txnTime = txnTime;
	}
}
