package com.megabank.olp.client.sender.sms.bean;

import com.megabank.olp.base.bean.BaseBean;

public class SmsSenderArgBean extends BaseBean
{
	private String mobileNumber;

	private String message;

	private String contractNo;

	private String idNo;

	private String branchBankCode;

	public SmsSenderArgBean()
	{}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMessage()
	{
		return message;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMessage( String message )
	{
		this.message = message;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
