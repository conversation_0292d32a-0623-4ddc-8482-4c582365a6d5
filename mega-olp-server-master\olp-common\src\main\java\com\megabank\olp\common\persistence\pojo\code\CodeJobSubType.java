package com.megabank.olp.common.persistence.pojo.code;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeJobSubType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_job_sub_type" )
public class CodeJobSubType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_job_sub_type";

	public static final String JOB_SUB_TYPE_ID_CONSTANT = "jobSubTypeId";

	public static final String CODE_JOB_TYPE_CONSTANT = "codeJobType";

	public static final String NAME_CONSTANT = "name";

	public static final String SUB_TYPE_CODE_CONSTANT = "subTypeCode";

	private Long jobSubTypeId;

	private transient CodeJobType codeJobType;

	private String name;

	private String subTypeCode;

	public CodeJobSubType()
	{}

	public CodeJobSubType( Long jobSubTypeId )
	{
		this.jobSubTypeId = jobSubTypeId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "job_type", nullable = false )
	public CodeJobType getCodeJobType()
	{
		return codeJobType;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "job_sub_type_id", unique = true, nullable = false )
	public Long getJobSubTypeId()
	{
		return jobSubTypeId;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Column( name = "sub_type_code", nullable = false, length = 1 )
	public String getSubTypeCode()
	{
		return subTypeCode;
	}

	public void setCodeJobType( CodeJobType codeJobType )
	{
		this.codeJobType = codeJobType;
	}

	public void setJobSubTypeId( Long jobSubTypeId )
	{
		this.jobSubTypeId = jobSubTypeId;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSubTypeCode( String subTypeCode )
	{
		this.subTypeCode = subTypeCode;
	}
}