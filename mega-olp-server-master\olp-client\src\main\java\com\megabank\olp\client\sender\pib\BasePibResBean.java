package com.megabank.olp.client.sender.pib;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class BasePibResBean extends BaseBean
{
	private String sys;

	private String resource;

	@JsonProperty( "clientTime" )
	private String responseDate;

	@JsonProperty( "code" )
	private String returnCode;

	@JsonProperty( "desc" )
	private String returnDesc;

	public BasePibResBean()
	{}

	public String getResource()
	{
		return resource;
	}

	public String getResponseDate()
	{
		return responseDate;
	}

	public String getReturnCode()
	{
		return returnCode;
	}

	public String getReturnDesc()
	{
		return returnDesc;
	}

	public String getSys()
	{
		return sys;
	}

	public void setResource( String resource )
	{
		this.resource = resource;
	}

	public void setResponseDate( String responseDate )
	{
		this.responseDate = responseDate;
	}

	public void setReturnCode( String returnCode )
	{
		this.returnCode = returnCode;
	}

	public void setReturnDesc( String returnDesc )
	{
		this.returnDesc = returnDesc;
	}

	public void setSys( String sys )
	{
		this.sys = sys;
	}
}
