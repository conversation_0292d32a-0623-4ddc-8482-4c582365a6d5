package com.megabank.olp.common.controller.bean.code;

import javax.validation.constraints.NotBlank;

public class IntroduceBranchBankListGetterArgBean
{	
	@NotBlank
	private String loanType;
	
	private Long branchBankId;
	
	private Long subBranchBankId;
	
	private String originalBranchBankCode;
	
	public IntroduceBranchBankListGetterArgBean()
	{
	}

	public String getLoanType()
	{
		return loanType;
	}
	
	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public Long getSubBranchBankId()
	{
		return subBranchBankId;
	}

	public String getOriginalBranchBankCode()
	{
		return originalBranchBankCode;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setSubBranchBankId( Long subBranchBankId )
	{
		this.subBranchBankId = subBranchBankId;
	}

	public void setOriginalBranchBankCode( String originalBranchBankCode )
	{
		this.originalBranchBankCode = originalBranchBankCode;
	}
}
