/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.finauth;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.finauth.bean.FinAuthVerifiedArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class FinAuthVerifiedClient extends BaseOtherDatabaseClient<FinAuthVerifiedArgBean, Boolean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/webatml/finauth/verifyAuth";
	}

}
