/**
 *
 */
package com.megabank.olp.client.sender.sso.custinfo.bean;

import com.megabank.olp.client.sender.sso.BaseSsoReqBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoCustInfoReqBean extends BaseSsoReqBean
{
	private String pageId;

	private String pageNo;

	private String deviceIxd;

	private String txnIxd;

	private String model;

	private String network = "Unknown";

	private String appVer;

	private String token;

	private String deviceToken;

	private String clientSysId = "NIB";

	public SsoCustInfoReqBean()
	{}

	public String getAppVer()
	{
		return appVer;
	}

	public String getClientSysId()
	{
		return clientSysId;
	}

	public String getDeviceIxd()
	{
		return deviceIxd;
	}

	public String getDeviceToken()
	{
		return deviceToken;
	}

	public String getModel()
	{
		return model;
	}

	public String getNetwork()
	{
		return network;
	}

	public String getPageId()
	{
		return pageId;
	}

	public String getPageNo()
	{
		return pageNo;
	}

	public String getToken()
	{
		return token;
	}

	public String getTxnIxd()
	{
		return txnIxd;
	}

	public void setAppVer( String appVer )
	{
		this.appVer = appVer;
	}

	public void setClientSysId( String clientSysId )
	{
		this.clientSysId = clientSysId;
	}

	public void setDeviceIxd( String deviceIxd )
	{
		this.deviceIxd = deviceIxd;
	}

	public void setDeviceToken( String deviceToken )
	{
		this.deviceToken = deviceToken;
	}

	public void setModel( String model )
	{
		this.model = model;
	}

	public void setNetwork( String network )
	{
		this.network = network;
	}

	public void setPageId( String pageId )
	{
		this.pageId = pageId;
	}

	public void setPageNo( String pageNo )
	{
		this.pageNo = pageNo;
	}

	public void setToken( String token )
	{
		this.token = token;
	}

	public void setTxnIxd( String txnIxd )
	{
		this.txnIxd = txnIxd;
	}

}
