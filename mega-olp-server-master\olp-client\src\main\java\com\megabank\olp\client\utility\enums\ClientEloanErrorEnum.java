package com.megabank.olp.client.utility.enums;

import com.megabank.olp.base.enums.ErrorEnum;

public enum ClientEloanErrorEnum implements ErrorEnum
{
	/**
	 * 查無擔保品提供人資料
	 */
	NO_COLLATERAL_PROVIDER_INFO( "02001" );

	private String errorCode;

	private ClientEloanErrorEnum( String errorCode )
	{
		this.errorCode = errorCode;
	}

	@Override
	public String getCode()
	{
		return "CLIENT-ELOAN" + errorCode;
	}
}
