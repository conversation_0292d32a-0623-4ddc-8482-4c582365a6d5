/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.finverify.bean;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class DoFinAuthArgBean extends BaseBean
{
	private String idNo;

	private String BirthDate;

	private String mobileNumber;

	private String bankCode;

	private String bankAccount;

	public String getBankAccount()
	{
		return bankAccount;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public String getBirthDate()
	{
		return BirthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBirthDate( String birthDate )
	{
		BirthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}
}
