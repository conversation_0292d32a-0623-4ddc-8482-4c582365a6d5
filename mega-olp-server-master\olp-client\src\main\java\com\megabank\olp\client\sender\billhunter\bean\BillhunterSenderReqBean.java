package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlRootElement( name = "NotificationXML" )
@XmlAccessorType( XmlAccessType.FIELD )
public class BillhunterSenderReqBean extends BaseBean
{
	@XmlAttribute( name = "VersionNo" )
	private String versionNo = "20120815_BH_AP2APMAIL";

	@XmlElement( name = "NotificationInfo" )
	private ReqInfoBean reqInfoBean;

	public BillhunterSenderReqBean()
	{}

	public ReqInfoBean getReqInfoBean()
	{
		return reqInfoBean;
	}

	public String getVersionNo()
	{
		return versionNo;
	}

	public void setReqInfoBean( ReqInfoBean reqInfoBean )
	{
		this.reqInfoBean = reqInfoBean;
	}

	public void setVersionNo( String versionNo )
	{
		this.versionNo = versionNo;
	}

}
