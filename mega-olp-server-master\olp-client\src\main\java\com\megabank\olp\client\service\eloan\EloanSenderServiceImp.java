package com.megabank.olp.client.service.eloan;

import java.util.Date;

import com.megabank.olp.client.sender.eloan.getted.agreedDate.EloanAgreedDateClient;
import com.megabank.olp.client.sender.eloan.getted.agreedDate.bean.EloanAgreedDateArgBean;
import com.megabank.olp.client.sender.eloan.getted.agreedDate.bean.EloanAgreedDateResultBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.eloan.getted.casestatus.EloanCaseStatusClient;
import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusArgBean;
import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusResultBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.EloanCustInfoClient;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoArgBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.attachment.AttachmentSubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.collateral.ProviderAgreementSubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.collateral.bean.ProviderAgreementSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.HouseLoanApplySubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.mydata.MyDataSubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.mydata.bean.MyDataSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.PersonalLoanApplySubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.ContractCompletedClient;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "sit", "stress", "uat", "prod" } )
public class EloanSenderServiceImp implements EloanSenderService
{

	@Autowired
	private AttachmentSubmittedClient attachmentSubmittedClient;

	@Autowired
	private HouseLoanApplySubmittedClient houseLoanApplySubmittedClient;

	@Autowired
	private PersonalLoanApplySubmittedClient personalLoanApplySubmittedClient;

	@Autowired
	private ProviderAgreementSubmittedClient providerAgreementSubmittedClient;

	@Autowired
	private ContractCompletedClient contractCompletedClient;

	@Autowired
	private EloanCustInfoClient eloanCustInfoClient;

	@Autowired
	private EloanCaseStatusClient caseStatusClient;

	@Autowired
	private MyDataSubmittedClient myDataSubmittedClient;

	@Autowired
	private EloanAgreedDateClient agreedDateClient;

	@Override
	public EloanCaseStatusResultBean getCaseStatusList( String idNo, Date birthDate )
	{
		EloanCaseStatusArgBean argBean = new EloanCaseStatusArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( CommonDateStringUtils.transDate2String( birthDate ) );
		argBean.setQueryWindow( 0 );

		return caseStatusClient.send( argBean );
	}

	@Override
	public EloanCustInfoResultBean getEloanCustInfo( String idNo, Date birthDate )
	{
		EloanCustInfoArgBean argBean = new EloanCustInfoArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( CommonDateStringUtils.transDate2String( birthDate ) );

		return eloanCustInfoClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitAttachment( AttachmentSubmittedArgBean argBean )
	{
		return attachmentSubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitHouseLoanApply( HouseLoanApplySubmittedArgBean argBean )
	{
		return houseLoanApplySubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitMyData( MyDataSubmittedArgBean argBean )
	{
		return myDataSubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitPersonalLoanApply( PersonalLoanApplySubmittedArgBean argBean )
	{
		return personalLoanApplySubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitProviderAgreement( ProviderAgreementSubmittedArgBean argBean )
	{
		return providerAgreementSubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitSigningContract( SigningContractCompletedArgBean argBean )
	{
		return contractCompletedClient.send( argBean );
	}

	@Override
	public EloanAgreedDateResultBean getAgreedDate( String idNo, Date birthDate, String loanType )
	{
		EloanAgreedDateArgBean argBean = new EloanAgreedDateArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( CommonDateStringUtils.transDate2String( birthDate ) );
		argBean.setLoanType( loanType );
		return agreedDateClient.send( argBean );
	}
}
