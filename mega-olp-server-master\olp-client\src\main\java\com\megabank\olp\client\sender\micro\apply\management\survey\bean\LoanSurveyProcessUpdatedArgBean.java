package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyProcessUpdatedArgBean extends BaseBean
{
	private String employeeId;

	private String employeeName;

	private Long id;

	private String status;

	public LoanSurveyProcessUpdatedArgBean()
	{
		// default constructor
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public Long getId()
	{
		return id;
	}

	public String getStatus()
	{
		return status;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setId( Long id )
	{
		this.id = id;
	}

	public void setStatus( String status )
	{
		this.status = status;
	}

}
