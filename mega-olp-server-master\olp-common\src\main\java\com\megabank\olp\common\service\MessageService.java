package com.megabank.olp.common.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.layer.BaseService;
import com.megabank.olp.common.service.bean.message.MessageResBean;
import com.megabank.olp.common.utility.enums.ErrorMessageEnum;

@Service
@Transactional
public class MessageService extends BaseService
{
	/**
	 * 取得錯誤頁訊息
	 *
	 * @param url
	 * @return
	 */
	public MessageResBean getErrorMessage( String url )
	{
		MessageResBean resBean = new MessageResBean();

		for( ErrorMessageEnum errorMessageEnum : ErrorMessageEnum.values() )
			if( errorMessageEnum.getContext().equals( url ) )
			{
				resBean.setTitle( errorMessageEnum.getTitle() );
				resBean.setContent( errorMessageEnum.getContent() );

				return resBean;
			}

		return resBean;
	}

}
