package com.megabank.olp.client.sender.micro;

import java.io.IOException;
import java.util.Collection;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.megabank.olp.base.bean.threadlocal.SessionInfoThreadLocalBean;
import com.megabank.olp.base.enums.IdentityTypeEnum;
import com.megabank.olp.base.enums.ResponseEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.service.jwt.encrypt.StrongIdentityJwtService;
import com.megabank.olp.base.service.jwt.encrypt.bean.StrongIdentityJwtArgBean;
import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.client.sender.BaseJsonClient;
import com.megabank.olp.client.utility.exception.MyClientException;

/**
 * s
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public abstract class BaseMicroServicesClient<TArg, TResult> extends BaseJsonClient<TArg, TArg, MicroStatResBean<TResult>, TResult>
{
	private final Class resultClass;

	@Autowired
	private StrongIdentityJwtService strongIdentityJwtService;
	
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	public BaseMicroServicesClient()
	{
		resultClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseMicroServicesClient.class )[ 1 ];
	}

	public TResult send( TArg arg, JwtArgBean jwtArgBean )
	{
		Validate.notNull( jwtArgBean );

		StrongIdentityJwtArgBean strongIdentityJwtArgBean = new StrongIdentityJwtArgBean( IdentityTypeEnum.BACKEND.getContext() );
		strongIdentityJwtArgBean.setBirthDate( jwtArgBean.getBirthDate() );
		strongIdentityJwtArgBean.setIdNo( jwtArgBean.getIdNo() );
		strongIdentityJwtArgBean.setSessionToken( jwtArgBean.getSessionToken() );

		SessionInfoThreadLocalBean localBean = new SessionInfoThreadLocalBean();
		localBean.setJwt( strongIdentityJwtService.generateJwtToken( strongIdentityJwtArgBean ) );

		sessionInfoThreadLocal.set( localBean );

		return super.send( arg );
	}

	private String getHeaderValue()
	{
		SessionInfoThreadLocalBean localBean = sessionInfoThreadLocal.get();

		if( localBean == null )
			throw new MyClientException( "SessionInfoThreadLocalBean is null, should be occured on UnitTest or need add /open suffix url" );

		return "Bearer " + localBean.getJwt();
	}

	@Override
	protected void doReqHeaders( HttpHeaders httpHeaders, TArg argBean )
	{
		if( StringUtils.startsWith( getSuffixUrl(), "/open" ) )
			return;

		httpHeaders.set( HttpHeaders.AUTHORIZATION, getHeaderValue() );
	}

	@Override
	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.POST;
	}

	@Override
	protected String getSimulatorCode( TArg argBean )
	{
		return "not-need-simulator";
	}

	protected abstract String getSuffixUrl();

	@Override
	protected boolean isMonitor()
	{
		return false;
	}

	@Override
	protected TArg transArg2Req( TArg argBean )
	{
		return argBean;
	}

	@Override
	protected TResult transRes2Result( MicroStatResBean<TResult> statResBean, HttpHeaders httpHeaders )
	{
		String stat = statResBean.getStat();

		if( StringUtils.isBlank( stat ) )
			throw new MyClientException( "the value in the stat field is blank" );

		if( !ResponseEnum.STAT_OK.getContext().equals( stat ) )
		{
			String errorMsg = getSystemName() + "系統錯誤:" + statResBean.getErrorMsgBean().getContent();

			throw new MyRuntimeException( statResBean.getErrorCode(), errorMsg, statResBean.getErrorLogId() );
		}

		return statResBean.getResult();
	}

	@Override
	protected MicroStatResBean<TResult> transResBody2Res( String resBody ) throws IOException
	{
		TypeFactory factory = mapper.getTypeFactory();

		if( Collection.class.isAssignableFrom( resultClass ) )
		{
			CollectionType collectionType = factory.constructCollectionType( resultClass, getBeanClassByCollection() );

			return mapper.readValue( resBody, factory.constructParametricType( MicroStatResBean.class, collectionType ) );

		}

		return mapper.readValue( resBody, factory.constructParametricType( MicroStatResBean.class, resultClass ) );
	}
}
