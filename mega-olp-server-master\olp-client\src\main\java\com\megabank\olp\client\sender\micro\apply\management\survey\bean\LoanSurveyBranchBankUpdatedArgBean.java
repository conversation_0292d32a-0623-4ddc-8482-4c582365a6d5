package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyBranchBankUpdatedArgBean extends BaseBean
{
	private String employeeId;

	private String employeeName;

	private Long id;

	private Long branchBankId;

	public LoanSurveyBranchBankUpdatedArgBean()
	{
		// default constructor
	}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getEmployeeId()
	{
		return employeeId;
	}

	public String getEmployeeName()
	{
		return employeeName;
	}

	public Long getId()
	{
		return id;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setEmployeeId( String employeeId )
	{
		this.employeeId = employeeId;
	}

	public void setEmployeeName( String employeeName )
	{
		this.employeeName = employeeName;
	}

	public void setId( Long id )
	{
		this.id = id;
	}

}
