/**
 *
 */
package com.megabank.olp.client.sender.credit.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "seqNo", "type", "func", "cno", "exp", "ccd", "idNo" } )
public class AuthDataBean extends BaseBean
{
	/**
	 * 序號識別碼，格式YYYYMMDD_HHmmss_xxxxxx(6碼數字)
	 */
	@XmlElement( name = "SEQNO" )
	private String seqNo;

	/**
	 * 來源代號(用來分辨來源， M化信用卡申請為：MWEB)
	 */
	@XmlElement( name = "TYPE" )
	private String type;

	/**
	 * 功能代號(數位開戶為：DA；信用卡申請為：7375)
	 * 檢查是否持有本行信用卡超過一年以上: LN
	 */
	@XmlElement( name = "FUNC" )
	private String func;

	/**
	 * 卡片卡號
	 * 卡號16位前8 位帶入出生年月日後補8個0
	 */
	@XmlElement( name = "CNO" )
	private String cno;

	/**
	 * 卡片到期月年MMYY
	 * 0000 -> 有效年月帶4個0 (加密前)
	 */
	@XmlElement( name = "EXP" )
	private String exp;

	/**
	 * 卡檢核碼
	 * 000 -> 驗證碼帶3個0 (加密前)
	 */
	@XmlElement( name = "CCD" )
	private String ccd;

	/**
	 * 身分證字號
	 */
	@XmlElement( name = "ID" )
	private String idNo;

	public String getCcd()
	{
		return ccd;
	}

	public String getCno()
	{
		return cno;
	}

	public String getExp()
	{
		return exp;
	}

	public String getFunc()
	{
		return func;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getSeqNo()
	{
		return seqNo;
	}

	public String getType()
	{
		return type;
	}

	public void setCcd( String ccd )
	{
		this.ccd = ccd;
	}

	public void setCno( String cno )
	{
		this.cno = cno;
	}

	public void setExp( String exp )
	{
		this.exp = exp;
	}

	public void setFunc( String func )
	{
		this.func = func;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setSeqNo( String seqNo )
	{
		this.seqNo = seqNo;
	}

	public void setType( String type )
	{
		this.type = type;
	}
}
