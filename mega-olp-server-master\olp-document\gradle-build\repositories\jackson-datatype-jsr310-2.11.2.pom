<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-modules-java8</artifactId>
    <version>2.11.2</version>
  </parent>
  <groupId>com.fasterxml.jackson.datatype</groupId>
  <artifactId>jackson-datatype-jsr310</artifactId>
  <name>Jackson datatype: JSR310</name>
  <packaging>bundle</packaging>
  <description>Add-on module to support JSR-310 (Java 8 Date &amp; Time API) data types.</description>
  <developers>
    <developer>
      <id>beamerblvd</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <timezone>-6</timezone>
    </developer>
  </developers>

  <properties>
    <!-- Java8 takes Javadoc-Nazi attitude, insert some sanity here --> 
    <additionalparam>-Xdoclint:none</additionalparam>

    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/datatype/jsr310</packageVersion.dir>
    <packageVersion.package>${project.groupId}.jsr310</packageVersion.package>
    <javac.src.version>1.8</javac.src.version>
    <javac.target.version>1.8</javac.target.version>
    <!-- Configuration properties for the OSGi maven-bundle-plugin -->
    <!-- import should be generated automatically from needed deps; export from simple package (include all) -->
<!--
    <osgi.export>${project.groupId}.jsr310.*;version=${project.version}</osgi.export>
-->
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>[1.8,)</version>
                  <message>[ERROR] The currently supported version of Java is 1.8 or higher</message>
                </requireJavaVersion>
                <requireMavenVersion>
                  <version>[3.0,)</version>
                  <message>[ERROR] The currently supported version of Maven is 3.0 or higher</message>
                </requireMavenVersion>
                <requirePluginVersions>
                  <banLatest>true</banLatest>
                  <banRelease>true</banRelease>
                  <banSnapshots>true</banSnapshots>
                  <phases>clean,deploy,site</phases>
                  <message>[ERROR] Best Practice is to always define plugin versions!</message>
                </requirePluginVersions>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.0</version>
        <inherited>true</inherited>
        <configuration>
          <source>${javac.src.version}</source>
          <target>${javac.target.version}</target>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
          <optimize>true</optimize>
          <compilerArguments>
            <Xmaxerrs>10000</Xmaxerrs>
            <Xmaxwarns>10000</Xmaxwarns>
            <Xlint />
          </compilerArguments>
        </configuration>
      </plugin>
      <!--  28-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
