package com.megabank.olp.common.service.bean.landing;

import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TextListColumnResBean extends BaseColumnResBean
{
	@JsonProperty( "columnValue" )
	private List<String> value = Collections.emptyList();

	public TextListColumnResBean()
	{
		// default constructor
	}

	public List<String> getValue()
	{
		return value;
	}

	public void setValue( List<String> value )
	{
		this.value = value;
	}

}
