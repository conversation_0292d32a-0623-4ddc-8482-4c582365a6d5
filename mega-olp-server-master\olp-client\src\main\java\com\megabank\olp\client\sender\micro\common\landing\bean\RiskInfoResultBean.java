package com.megabank.olp.client.sender.micro.common.landing.bean;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class RiskInfoResultBean extends BaseBean
{
	private Integer riskDouble;

	private BigDecimal riskRate;

	private Boolean hasResult;

	public RiskInfoResultBean()
	{
		// default constructor
	}

	public Boolean getHasResult()
	{
		return hasResult;
	}

	public Integer getRiskDouble()
	{
		return riskDouble;
	}

	public BigDecimal getRiskRate()
	{
		return riskRate;
	}

	public void setHasResult( Boolean hasResult )
	{
		this.hasResult = hasResult;
	}

	public void setRiskDouble( Integer riskDouble )
	{
		this.riskDouble = riskDouble;
	}

	public void setRiskRate( BigDecimal riskRate )
	{
		this.riskRate = riskRate;
	}
}
