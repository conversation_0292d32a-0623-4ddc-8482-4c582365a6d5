package com.megabank.olp.client.sender.sms.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class RequestBodyBean extends BaseBean
{
	@XmlElement( name = "aMsgToHiB2B" )
	private RequestBean requestBean;

	public RequestBodyBean()
	{}

	public RequestBean getRequestBean()
	{
		return requestBean;
	}

	public void setRequestBean( RequestBean requestBean )
	{
		this.requestBean = requestBean;
	}
}
