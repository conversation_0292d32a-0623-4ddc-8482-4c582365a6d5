<!--
  ~ <PERSON>B<PERSON>, Home of Professional Open Source.
  ~ Copyright 2018 Red Hat, Inc., and individual contributors
  ~ as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>org.jboss</groupId>
    <version>36</version>
    <artifactId>jboss-parent</artifactId>

    <packaging>pom</packaging>

    <name>JBoss Parent Parent POM</name>
    <description>Provides, via submodules, a base configuration for JBoss project builds, as well as a derived configuration supporting multi-release JARs</description>
    <url>http://www.jboss.org</url>

    <issueManagement>
        <system>JIRA</system>
        <url>https://issues.jboss.org/</url>
    </issueManagement>

    <scm>
        <connection>scm:git:**************:jboss/jboss-parent-pom.git</connection>
        <developerConnection>scm:git:**************:jboss/jboss-parent-pom.git</developerConnection>
        <url>http://github.com/jboss/jboss-parent-pom</url>
        <tag>HEAD</tag>
    </scm>

    <developers>
        <developer>
            <id>jboss.org</id>
            <name>JBoss.org Community</name>
            <organization>JBoss.org</organization>
            <organizationUrl>http://www.jboss.org</organizationUrl>
        </developer>
    </developers>

    <mailingLists>
        <mailingList>
            <name>JBoss User List</name>
            <subscribe>https://lists.jboss.org/mailman/listinfo/jboss-user</subscribe>
            <unsubscribe>https://lists.jboss.org/mailman/listinfo/jboss-user</unsubscribe>
            <archive>http://lists.jboss.org/pipermail/jboss-user/</archive>
        </mailingList>
        <mailingList>
            <name>JBoss Developer List</name>
            <subscribe>https://lists.jboss.org/mailman/listinfo/jboss-development</subscribe>
            <unsubscribe>https://lists.jboss.org/mailman/listinfo/jboss-development</unsubscribe>
            <archive>http://lists.jboss.org/pipermail/jboss-development/</archive>
        </mailingList>
    </mailingLists>

    <licenses>
        <license>
            <name>Public Domain</name>
            <url>http://repository.jboss.org/licenses/cc0-1.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <organization>
        <name>JBoss by Red Hat</name>
        <url>http://www.jboss.org</url>
    </organization>

    <properties>
      <!-- **************** -->
      <!-- Plugins versions -->
      <!-- **************** -->
      <version.antrun.plugin>1.8</version.antrun.plugin>
      <version.archetype.plugin>3.0.1</version.archetype.plugin>
      <version.assembly.plugin>3.1.0</version.assembly.plugin>
      <version.buildhelper.plugin>3.0.0</version.buildhelper.plugin>
      <version.buildnumber.plugin>1.4</version.buildnumber.plugin>
      <version.bundle.plugin>3.5.0</version.bundle.plugin>
      <version.checkstyle.plugin>3.0.0</version.checkstyle.plugin>
      <version.clean.plugin>3.1.0</version.clean.plugin>
      <version.clover.plugin>4.1.2</version.clover.plugin>
      <version.cobertura.plugin>2.7</version.cobertura.plugin>
      <version.compiler.plugin>3.8.0-jboss-2</version.compiler.plugin>
      <version.dependency.plugin>3.1.1</version.dependency.plugin>
      <version.deploy.plugin>2.8.2</version.deploy.plugin>
      <version.download.plugin>1.4.1</version.download.plugin>
      <version.ear.plugin>3.0.1</version.ear.plugin>
      <version.org.eclipse.m2e.lifecycle-mapping>1.0.0</version.org.eclipse.m2e.lifecycle-mapping>
      <version.ejb.plugin>3.0.1</version.ejb.plugin>
      <version.exec.plugin>1.6.0</version.exec.plugin>
      <version.enforcer.plugin>3.0.0-M2</version.enforcer.plugin>
      <version.findbugs.plugin>3.0.5</version.findbugs.plugin>
      <version.gpg.plugin>1.6</version.gpg.plugin>
      <version.help.plugin>3.1.0</version.help.plugin>
      <version.injection.plugin>1.0.2</version.injection.plugin>
      <version.install.plugin>2.5.2</version.install.plugin>
      <version.jar.plugin>3.1.0</version.jar.plugin>
      <version.javadoc.plugin>3.0.1</version.javadoc.plugin>
      <version.javancss.plugin>2.1</version.javancss.plugin>
      <version.jxr.plugin>2.5</version.jxr.plugin>
      <version.license.plugin>1.16</version.license.plugin>
      <version.pir.plugin>2.9</version.pir.plugin><!-- maven-project-info-reports-plugins -->
      <version.plugin.plugin>3.5.2</version.plugin.plugin>
      <version.pmd.plugin>3.10.0</version.pmd.plugin>
      <version.rar.plugin>2.4</version.rar.plugin>
      <version.release.plugin>2.5.3</version.release.plugin>
      <version.resources.plugin>3.1.0</version.resources.plugin>
      <version.shade.plugin>3.1.1</version.shade.plugin>
      <version.site.plugin>3.7.1</version.site.plugin>
      <version.sonar.plugin>3.6.0.1398</version.sonar.plugin>
      <version.source.plugin>3.0.1</version.source.plugin>
      <version.surefire.plugin>2.22.0</version.surefire.plugin>
      <version.failsafe.plugin>${version.surefire.plugin}</version.failsafe.plugin>
      <version.war.plugin>3.2.2</version.war.plugin>
      <version.zanata.plugin>4.4.3</version.zanata.plugin>

      <!-- we override plexus Archiver with version newer than 3.0.3 as older don't work on JDK9 -->
      <version.plexus.archiver>3.6.0</version.plexus.archiver>


      <!-- ***************** -->
      <!-- Repository Deployment URLs -->
      <!-- ***************** -->
      <jboss.releases.repo.id>jboss-releases-repository</jboss.releases.repo.id>
      <jboss.releases.repo.url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/</jboss.releases.repo.url>
      <jboss.snapshots.repo.id>jboss-snapshots-repository</jboss.snapshots.repo.id>
      <jboss.snapshots.repo.url>https://repository.jboss.org/nexus/content/repositories/snapshots/</jboss.snapshots.repo.url>

      <!-- ************** -->
      <!-- Build settings -->
      <!-- ************** -->

      <!-- Cross plugins settings -->
      <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
      <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

      <!-- maven-compiler-plugin -->
      <maven.compiler.target>1.8</maven.compiler.target>
      <maven.compiler.source>1.8</maven.compiler.source>
      <maven.compiler.testTarget>${maven.compiler.target}</maven.compiler.testTarget>
      <maven.compiler.testSource>${maven.compiler.source}</maven.compiler.testSource>

      <!--
          Options to override the compiler arguments directly on the compiler argument line to separate between what
          the IDE understands as the source level and what the Maven compiler actually use.
      -->
      <maven.compiler.argument.target>${maven.compiler.target}</maven.compiler.argument.target>
      <maven.compiler.argument.source>${maven.compiler.source}</maven.compiler.argument.source>
      <maven.compiler.argument.testTarget>${maven.compiler.testTarget}</maven.compiler.argument.testTarget>
      <maven.compiler.argument.testSource>${maven.compiler.testSource}</maven.compiler.argument.testSource>

      <!-- maven-enforcer-plugin -->
      <maven.min.version>3.2.5</maven.min.version>
      <jdk.min.version>${maven.compiler.argument.source}</jdk.min.version>
      <insecure.repositories>ERROR</insecure.repositories>

      <!-- maven-idea-plugin & maven-eclipse-plugin -->
      <downloadSources>true</downloadSources>

      <!-- maven-pmd-plugin -->
      <targetJdk>${maven.compiler.argument.target}</targetJdk>

      <!-- maven-release-plugin -->
      <useReleaseProfile>false</useReleaseProfile>
      <arguments>-Pjboss-release</arguments>

      <!-- maven-assembly-plugin -->
      <sourceReleaseAssemblyDescriptor>source-release</sourceReleaseAssemblyDescriptor>
      <version.checkstyle>8.19</version.checkstyle>
      <!-- exposed additional params for javadoc, such as Xlint -->
      <javadoc.additional.params />

      <!-- Special version for MR JAR jdk-misc artifact -->
      <version.jdk-misc>3.Final</version.jdk-misc>
    </properties>

    <build>

      <pluginManagement>
        <!-- All plugins ordered by shortname (antrun, assembly ...) -->
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>${version.antrun.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-archetype-plugin</artifactId>
            <version>${version.archetype.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <version>${version.assembly.plugin}</version>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <version>${version.buildhelper.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>buildnumber-maven-plugin</artifactId>
            <version>${version.buildnumber.plugin}</version>
          </plugin>
          <plugin>
            <groupId>com.googlecode.maven-download-plugin</groupId>
            <artifactId>download-maven-plugin</artifactId>
            <version>${version.download.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.felix</groupId>
            <artifactId>maven-bundle-plugin</artifactId>
            <version>${version.bundle.plugin}</version>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                </manifestEntries>
              </archive>
              <instructions>
                <Scm-Revision>${buildNumber}</Scm-Revision>
              </instructions>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${version.checkstyle.plugin}</version>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>${version.checkstyle}</version>
                <exclusions>
                  <exclusion>
                    <groupId>com.sun</groupId>
                    <artifactId>tools</artifactId>
                  </exclusion>
                </exclusions>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-clean-plugin</artifactId>
            <version>${version.clean.plugin}</version>
          </plugin>
          <plugin>
            <groupId>com.atlassian.maven.plugins</groupId>
            <artifactId>clover-maven-plugin</artifactId>
            <version>${version.clover.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
            <version>${version.cobertura.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>${version.compiler.plugin}</version>
            <configuration>
              <showDeprecation>true</showDeprecation>
              <showWarnings>true</showWarnings>
              <source>${maven.compiler.argument.source}</source>
              <target>${maven.compiler.argument.target}</target>
              <testSource>${maven.compiler.argument.testSource}</testSource>
              <testTarget>${maven.compiler.argument.testTarget}</testTarget>
              <compilerArgs>
                <arg>-Xlint:unchecked</arg>
              </compilerArgs>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <version>${version.dependency.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <version>${version.deploy.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-ear-plugin</artifactId>
            <version>${version.ear.plugin}</version>
            <dependencies>
              <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-archiver</artifactId>
                <version>${version.plexus.archiver}</version>
              </dependency>
            </dependencies>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-ejb-plugin</artifactId>
            <version>${version.ejb.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <version>${version.enforcer.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>${version.exec.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <version>${version.failsafe.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>findbugs-maven-plugin</artifactId>
            <version>${version.findbugs.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${version.gpg.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-help-plugin</artifactId>
            <version>${version.help.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.jboss.maven.plugins</groupId>
            <artifactId>maven-injection-plugin</artifactId>
            <version>${version.injection.plugin}</version>
            <executions>
              <execution>
                <phase>compile</phase>
                <goals>
                  <goal>bytecode</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-install-plugin</artifactId>
            <version>${version.install.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <version>${version.jar.plugin}</version>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${version.javadoc.plugin}</version>
            <configuration>
              <header><![CDATA[<b>${project.name} ${project.version}</b>]]></header>
              <footer><![CDATA[<b>${project.name} ${project.version}</b>]]></footer>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
              <!--
                 Since maven-javadoc-plugin 3.0.0 additionalparam is not not working anymore.
                 Therefore additionalOptions has been introduced, but old configuration is kept for backward compatibility
                 in case you are using this parent pom with overwritten version of maven-javadoc-plugin
              -->
              <additionalparam>${javadoc.additional.params}</additionalparam>
              <additionalOptions>${javadoc.additional.params}</additionalOptions>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>javancss-maven-plugin</artifactId>
            <version>${version.javancss.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <version>${version.jxr.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>license-maven-plugin</artifactId>
            <version>${version.license.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-plugin-plugin</artifactId>
            <version>${version.plugin.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>${version.pmd.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-rar-plugin</artifactId>
            <version>${version.rar.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-release-plugin</artifactId>
            <version>${version.release.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>${version.resources.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <version>${version.shade.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-site-plugin</artifactId>
            <version>${version.site.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>sonar-maven-plugin</artifactId>
            <version>${version.sonar.plugin}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>${version.source.plugin}</version>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${version.surefire.plugin}</version>
            <configuration>
              <trimStackTrace>false</trimStackTrace>
              <systemProperties>
                <java.io.tmpdir>${project.build.directory}</java.io.tmpdir>
              </systemProperties>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <version>${version.war.plugin}</version>
            <configuration>
              <archive>
                <index>true</index>
                <manifest>
                  <addDefaultSpecificationEntries>
                    true
                  </addDefaultSpecificationEntries>
                  <addDefaultImplementationEntries>
                    true
                  </addDefaultImplementationEntries>
                </manifest>
                <manifestEntries>
                  <Implementation-URL>${project.url}</Implementation-URL>
                  <Java-Version>${java.version}</Java-Version>
                  <Java-Vendor>${java.vendor}</Java-Vendor>
                  <Os-Name>${os.name}</Os-Name>
                  <Os-Arch>${os.arch}</Os-Arch>
                  <Os-Version>${os.version}</Os-Version>
                  <Scm-Url>${project.scm.url}</Scm-Url>
                  <Scm-Connection>${project.scm.connection}</Scm-Connection>
                  <Scm-Revision>${buildNumber}</Scm-Revision>
                </manifestEntries>
              </archive>
              <failOnMissingWebXml>false</failOnMissingWebXml>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.zanata</groupId>
            <artifactId>zanata-maven-plugin</artifactId>
            <version>${version.zanata.plugin}</version>
          </plugin>

          <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
          <plugin>
            <groupId>org.eclipse.m2e</groupId>
            <artifactId>lifecycle-mapping</artifactId>
            <version>${version.org.eclipse.m2e.lifecycle-mapping}</version>
            <configuration>
              <lifecycleMappingMetadata>
                <pluginExecutions>
                  <!-- Configure m2e to execute the manifest goal of the bundle plugin, if present. -->
                  <pluginExecution>
                    <pluginExecutionFilter>
                      <groupId>org.apache.felix</groupId>
                      <artifactId>maven-bundle-plugin</artifactId>
                      <versionRange>[2.3.7,)</versionRange>
                      <goals>
                        <goal>manifest</goal>
                      </goals>
                    </pluginExecutionFilter>
                    <action>
                      <execute />
                    </action>
                  </pluginExecution>
                  <!-- Configure m2e to ignore the Maven enforcer plugin -->
                  <pluginExecution>
                    <pluginExecutionFilter>
                      <groupId>org.apache.maven.plugins</groupId>
                      <artifactId>maven-enforcer-plugin</artifactId>
                      <versionRange>[1.3.1,)</versionRange>
                      <goals>
                        <goal>enforce</goal>
                      </goals>
                    </pluginExecutionFilter>
                    <action>
                      <ignore />
                    </action>
                  </pluginExecution>
                  <!-- Configure m2e to ignore the buildnumber-maven-plugin. -->
                  <pluginExecution>
                    <pluginExecutionFilter>
                      <groupId>org.codehaus.mojo</groupId>
                      <artifactId>buildnumber-maven-plugin</artifactId>
                      <versionRange>[1.0.0,)</versionRange>
                      <goals>
                        <goal>create</goal>
                      </goals>
                    </pluginExecutionFilter>
                    <action>
                      <ignore />
                    </action>
                  </pluginExecution>
                </pluginExecutions>
              </lifecycleMappingMetadata>
            </configuration>
          </plugin>

        </plugins>

      </pluginManagement>

      <plugins>

        <!-- Check for the minimum version of Java and Maven.  Runs during the validate phase. -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <executions>
            <execution>
              <id>enforce-java-version</id>
              <goals>
                <goal>enforce</goal>
              </goals>
              <configuration>
                <rules>
                  <bannedRepositories>
                      <level>${insecure.repositories}</level>
                      <bannedRepositories>
                          <bannedRepository>http://*</bannedRepository>
                      </bannedRepositories>
                      <bannedPluginRepositories>
                          <bannedPluginRepository>http://*</bannedPluginRepository>
                      </bannedPluginRepositories>
                  </bannedRepositories>
                  <requireJavaVersion>
                    <message>To build this project JDK ${jdk.min.version} (or greater) is required. Please install it.</message>
                    <version>${jdk.min.version}</version>
                  </requireJavaVersion>
                </rules>
              </configuration>
            </execution>
            <execution>
              <id>enforce-maven-version</id>
              <goals>
                <goal>enforce</goal>
              </goals>
              <configuration>
                <rules>
                  <requireMavenVersion>
                    <message>To build this project Maven ${maven.min.version} (or greater) is required. Please install it.</message>
                    <version>${maven.min.version}</version>
                  </requireMavenVersion>
                </rules>
              </configuration>
            </execution>
          </executions>
        </plugin>

        <!-- Set properties containing the scm revision -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <executions>
            <execution>
              <id>get-scm-revision</id>
              <phase>initialize</phase>
              <goals>
                <goal>create</goal>
              </goals>
              <configuration>
                <doCheck>false</doCheck>
                <doUpdate>false</doUpdate>
                <revisionOnScmFailure>UNKNOWN</revisionOnScmFailure>
                <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
              </configuration>
            </execution>
          </executions>
        </plugin>

        <!-- Attach source jar to all builds.  Runs during the package phase.-->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

      </plugins>

    </build>

    <profiles>

      <!--
          The profile jboss-release must be active when a project is released.  The configuration
          in this POM will automatically call this profile if using the maven-release-plugin.
          If the maven-release-plugin is not used during the release, this profile must
          be manually activated.
       -->
      <profile>
        <id>jboss-release</id>
        <build>
          <plugins>
            <!-- Create a source-release artifact that contains the fully buildable
                 project directory source structure.  This should be released to
                 the Maven repository for each JBoss project release. -->
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-assembly-plugin</artifactId>
              <version>${version.assembly.plugin}</version>
              <dependencies>
                <dependency>
                  <groupId>org.apache.apache.resources</groupId>
                  <artifactId>apache-source-release-assembly-descriptor</artifactId>
                  <version>1.0.6</version>
                </dependency>
              </dependencies>
              <executions>
                <execution>
                  <id>source-release-assembly</id>
                  <phase>package</phase>
                  <goals>
                    <goal>single</goal>
                  </goals>
                  <configuration>
                    <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                    <descriptorRefs>
                      <descriptorRef>${sourceReleaseAssemblyDescriptor}</descriptorRef>
                    </descriptorRefs>
                    <tarLongFileMode>gnu</tarLongFileMode>
                  </configuration>
                </execution>
              </executions>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-deploy-plugin</artifactId>
              <configuration>
                <updateReleaseInfo>true</updateReleaseInfo>
              </configuration>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
              <executions>
                <execution>
                  <id>attach-javadocs</id>
                  <goals>
                    <goal>jar</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </build>
      </profile>
      <profile>
         <id>doclint-java8-disable</id>
         <activation>
           <jdk>[1.8,)</jdk>
         </activation>
         <properties>
           <javadoc.additional.params>-Xdoclint:none</javadoc.additional.params>
         </properties>
       </profile>
      <!--
          This profile can be activated to generate gpg signatures for all build
          artifacts.  This profile requires that the properties "gpg.keyname"
          and "gpg.passphrase" are available to the current build.
      -->
      <profile>
        <id>gpg-sign</id>
        <build>
          <plugins>
            <!-- This will sign the artifact, the POM, and all attached artifacts -->
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-gpg-plugin</artifactId>
              <configuration>
                <useAgent>true</useAgent>
              </configuration>
              <executions>
                <execution>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </build>
      </profile>

        <profile>
          <id>compile-java8-release-flag</id>
          <activation>
            <file>
              <exists>${basedir}/build-release-8</exists>
            </file>
            <jdk>[9,)</jdk>
          </activation>
          <build>
            <plugins>
              <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                  <execution>
                    <id>default-compile</id>
                    <phase>compile</phase>
                    <goals>
                      <goal>compile</goal>
                    </goals>
                    <configuration>
                      <release>8</release>
                    </configuration>
                  </execution>
                </executions>
              </plugin>
            </plugins>
          </build>
        </profile>

        <!-- This profile is manually activated by a property to include jdk-misc.jar in the base layer build. -->
        <profile>
            <id>include-jdk-misc</id>
            <activation>
                <file>
                    <exists>${basedir}/build-include-jdk-misc</exists>
                </file>
                <jdk>[9,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>fetch-jdk-misc</id>
                                <phase>generate-sources</phase>
                                <goals>
                                    <goal>get</goal>
                                    <goal>copy</goal>
                                </goals>
                                <configuration>
                                    <artifact>org.jboss:jdk-misc:${version.jdk-misc}</artifact>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                    <stripVersion>true</stripVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-compile</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>8</release>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>${project.build.directory}/jdk-misc.jar
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 9 or later is used to test a project that supports Java 8 -->
        <profile>
            <id>java8-test</id>
            <activation>
                <jdk>[9,)</jdk>
                <property>
                    <name>java8.home</name>
                </property>
                <file>
                    <exists>${basedir}/build-test-java8</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>java8-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <jvm>${java8.home}/bin/java</jvm>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>${java8.home}/lib/tools.jar</additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 9 or later is used to build -->
        <profile>
            <id>java9-mr-build</id>
            <activation>
                <jdk>[9,)</jdk>
                <file>
                    <exists>${basedir}/src/main/java9</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>compile-java9</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>9</release>
                                    <buildDirectory>${project.build.directory}</buildDirectory>
                                    <compileSourceRoots>${project.basedir}/src/main/java9</compileSourceRoots>
                                    <outputDirectory>${project.build.directory}/classes/META-INF/versions/9
                                    </outputDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifestEntries>
                                    <Multi-Release>true</Multi-Release>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 10 or later is used to test a project that supports Java 9 -->
        <profile>
            <id>java9-test</id>
            <activation>
                <jdk>[10,)</jdk>
                <property>
                    <name>java9.home</name>
                </property>
                <file>
                    <exists>${basedir}/build-test-java9</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>java9-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <jvm>${java9.home}/bin/java</jvm>
                                    <classesDirectory>${project.build.directory}/classes/META-INF/versions/9
                                    </classesDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 10 or later is used to build -->
        <profile>
            <id>java10-mr-build</id>
            <activation>
                <jdk>[10,)</jdk>
                <file>
                    <exists>${basedir}/src/main/java10</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>compile-java10</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>10</release>
                                    <buildDirectory>${project.build.directory}</buildDirectory>
                                    <compileSourceRoots>${project.basedir}/src/main/java10</compileSourceRoots>
                                    <outputDirectory>${project.build.directory}/classes/META-INF/versions/10
                                    </outputDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifestEntries>
                                    <Multi-Release>true</Multi-Release>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 11 or later is used to test a project that supports Java 10 -->
        <profile>
            <id>java10-test</id>
            <activation>
                <jdk>[11,)</jdk>
                <property>
                    <name>java10.home</name>
                </property>
                <file>
                    <exists>${basedir}/build-test-java10</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>java10-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <jvm>${java10.home}/bin/java</jvm>
                                    <classesDirectory>${project.build.directory}/classes/META-INF/versions/10
                                    </classesDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 11 or later is used to build -->
        <profile>
            <id>java11-mr-build</id>
            <activation>
                <jdk>[11,)</jdk>
                <file>
                    <exists>${basedir}/src/main/java11</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>compile-java11</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>11</release>
                                    <buildDirectory>${project.build.directory}</buildDirectory>
                                    <compileSourceRoots>${project.basedir}/src/main/java11</compileSourceRoots>
                                    <outputDirectory>${project.build.directory}/classes/META-INF/versions/11
                                    </outputDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/10
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifestEntries>
                                    <Multi-Release>true</Multi-Release>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 12 or later is used to test a project that supports Java 11 -->
        <profile>
            <id>java11-test</id>
            <activation>
                <jdk>[12,)</jdk>
                <property>
                    <name>java11.home</name>
                </property>
                <file>
                    <exists>${basedir}/build-test-java11</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>java11-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <jvm>${java11.home}/bin/java</jvm>
                                    <classesDirectory>${project.build.directory}/classes/META-INF/versions/11
                                    </classesDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/10
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 12 or later is used to build -->
        <profile>
            <id>java12-mr-build</id>
            <activation>
                <jdk>[12,)</jdk>
                <file>
                    <exists>${basedir}/src/main/java12</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>compile-java12</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>12</release>
                                    <buildDirectory>${project.build.directory}</buildDirectory>
                                    <compileSourceRoots>${project.basedir}/src/main/java12</compileSourceRoots>
                                    <outputDirectory>${project.build.directory}/classes/META-INF/versions/12
                                    </outputDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/11
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/10
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifestEntries>
                                    <Multi-Release>true</Multi-Release>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 13 or later is used to test a project that supports Java 12 -->
        <profile>
            <id>java12-test</id>
            <activation>
                <jdk>[13,)</jdk>
                <property>
                    <name>java12.home</name>
                </property>
                <file>
                    <exists>${basedir}/build-test-java12</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>java12-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <jvm>${java12.home}/bin/java</jvm>
                                    <classesDirectory>${project.build.directory}/classes/META-INF/versions/12
                                    </classesDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/11
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/10
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- This profile is activated when Java 13 or later is used to build -->
        <profile>
            <id>java13-mr-build</id>
            <activation>
                <jdk>[13,)</jdk>
                <file>
                    <exists>${basedir}/src/main/java13</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>compile-java13</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <release>12</release>
                                    <buildDirectory>${project.build.directory}</buildDirectory>
                                    <compileSourceRoots>${project.basedir}/src/main/java13</compileSourceRoots>
                                    <outputDirectory>${project.build.directory}/classes/META-INF/versions/13
                                    </outputDirectory>
                                    <additionalClasspathElements>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/12
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/11
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/10
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>
                                            ${project.build.directory}/classes/META-INF/versions/9
                                        </additionalClasspathElement>
                                        <additionalClasspathElement>${project.build.outputDirectory}
                                        </additionalClasspathElement>
                                    </additionalClasspathElements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-jar-plugin</artifactId>
                        <configuration>
                            <archive>
                                <manifestEntries>
                                    <Multi-Release>true</Multi-Release>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

    </profiles>

    <!-- Some plugins are available in the jboss repository only -->
    <pluginRepositories>
      <pluginRepository>
        <id>jboss-public-repository</id>
        <name>JBoss Public Maven Repository</name>
        <url>https://repository.jboss.org/nexus/content/groups/public/</url>
        <layout>default</layout>
        <releases>
          <enabled>true</enabled>
        </releases>
        <snapshots>
          <enabled>false</enabled>
        </snapshots>
      </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
      <repository>
        <id>${jboss.releases.repo.id}</id>
        <name>JBoss Releases Repository</name>
        <url>${jboss.releases.repo.url}</url>
      </repository>
      <snapshotRepository>
        <id>${jboss.snapshots.repo.id}</id>
        <name>JBoss Snapshots Repository</name>
        <url>${jboss.snapshots.repo.url}</url>
      </snapshotRepository>
    </distributionManagement>

</project>
