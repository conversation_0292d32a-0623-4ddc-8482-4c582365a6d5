<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.projectreactor</groupId>
  <artifactId>reactor-bom</artifactId>
  <version>Bismuth-SR9</version>
  <packaging>pom</packaging>
  <name>Project Reactor 3 Release Train - BOM</name>
  <description>Bill of materials to make sure a consistent set of versions is used for Reactor 3.</description>
  <url>http://projectreactor.io</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://pivotal.io/</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>smaldini</id>
      <name>Stephane Maldini</name>
      <email>smaldini at pivotal.io</email>
    </developer>
    <developer>
      <id>simonbasle</id>
      <name>Simon Baslé</name>
      <email>sbasle at pivotal.io</email>
    </developer>
    <developer>
      <id>akarnokd</id>
      <name>David Karnok</name>
      <email>akarnokd at gmail.com</email>
    </developer>
    <developer>
      <id>violetagg</id>
      <name>Violeta Georgieva</name>
      <email>vgeorgieva at pivotal.io</email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/reactor/reactor</connection>
    <developerConnection>scm:git:git://github.com/reactor/reactor</developerConnection>
    <url>https://github.com/reactor/reactor</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/reactor</url>
  </issueManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>1.0.2</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core</artifactId>
        <version>3.1.7.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-test</artifactId>
        <version>3.1.7.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-extra</artifactId>
        <version>3.1.6.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-adapter</artifactId>
        <version>3.1.6.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-logback</artifactId>
        <version>3.1.6.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.ipc</groupId>
        <artifactId>reactor-netty</artifactId>
        <version>0.7.7.RELEASE</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kafka</groupId>
        <artifactId>reactor-kafka</artifactId>
        <version>1.0.0.RELEASE</version>
        <scope>compile</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
