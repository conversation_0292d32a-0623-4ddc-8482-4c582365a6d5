package com.megabank.olp.common.utility.enums;

/**
 * 分行服務可使用者
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public enum AllowedUserEnum
{
	/**
	 * 僅 一般使用者
	 */
	NORMAL_USER( 1 ),

	/**
	 * 僅行員
	 */
	EMPLOYEE( 2 ),

	/**
	 * 一般使用者與行員
	 */
	USER_AND_EMPLOYEE( 3 );

	private Integer context;

	private AllowedUserEnum( Integer context )
	{
		this.context = context;
	}

	public Integer getContext()
	{
		return context;
	}
}
