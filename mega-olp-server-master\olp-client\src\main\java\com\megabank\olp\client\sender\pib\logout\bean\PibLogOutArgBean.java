package com.megabank.olp.client.sender.pib.logout.bean;

import com.megabank.olp.client.sender.pib.BasePibArgBean;

public class PibLogOutArgBean extends BasePibArgBean
{
	private String trackingIxd;

	private String clientIp;

	private String sessionId;

	private String accessToken;

	public PibLogOutArgBean()
	{}

	public String getAccessToken()
	{
		return accessToken;
	}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getSessionId()
	{
		return sessionId;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public void setAccessToken( String accessToken )
	{
		this.accessToken = accessToken;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setSessionId( String sessionId )
	{
		this.sessionId = sessionId;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}
}
