package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalCustInfoArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalCustInfoResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class InternalCustInfoClient extends BaseOtherDatabaseClient<InternalCustInfoArgBean, InternalCustInfoResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/nbots/internalaccount/getCustInfo";
	}

}
