package com.megabank.olp.common.controller.bean.code;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

public class BranchBankListGetterArgBean extends BaseBean
{
	@NotBlank
	private String bankType;

	@NotBlank
	private String townCode;

	public BranchBankListGetterArgBean()
	{
		// default constructor
	}

	public String getBankType()
	{
		return bankType;
	}

	public String getTownCode()
	{
		return townCode;
	}

	public void setBankType( String bankType )
	{
		this.bankType = bankType;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}

}
