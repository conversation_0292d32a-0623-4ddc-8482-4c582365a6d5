package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.base.bean.ImmutableByteArray;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 * The LandingSolutionImage is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_solution_image" )
public class LandingSolutionImage extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_solution_image";

	public static final String SOLUTION_IMAGE_ID_CONSTANT = "solutionImageId";

	public static final String LANDING_SOLUTION_CONSTANT = "landingSolution";

	public static final String LANDING_TEMPLATE_COLUMN_CONSTANT = "landingTemplateColumn";

	public static final String IMAGE_NAME_CONSTANT = "imageName";

	public static final String IMAGE_SIZE_CONSTANT = "imageSize";

	public static final String IMAGE_DPI_WIDTH_CONSTANT = "imageDpiWidth";

	public static final String IMAGE_DPI_HEIGHT_CONSTANT = "imageDpiHeight";

	public static final String IMAGE_CONTENT_CONSTANT = "imageContent";

	public static final String SOLUTION_TEMP_ID_CONSTANT = "solutionTempId";

	public static final String CURRENT_USE_CONSTANT = "currentUse";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long solutionImageId;

	private transient LandingSolution landingSolution;

	private transient LandingTemplateColumn landingTemplateColumn;

	private String imageName;

	private long imageSize;

	private int imageDpiWidth;

	private int imageDpiHeight;

	private transient ImmutableByteArray imageContent;

	private Long solutionTempId;

	private boolean currentUse;

	private Date createdDate;

	public LandingSolutionImage()
	{}

	public LandingSolutionImage( LandingTemplateColumn landingTemplateColumn, String imageName, long imageSize, int imageDpiWidth, int imageDpiHeight,
								 ImmutableByteArray imageContent, boolean currentUse, Date createdDate )
	{
		this.landingTemplateColumn = landingTemplateColumn;
		this.imageName = imageName;
		this.imageSize = imageSize;
		this.imageDpiWidth = imageDpiWidth;
		this.imageDpiHeight = imageDpiHeight;
		this.imageContent = imageContent;
		this.currentUse = currentUse;
		this.createdDate = createdDate;
	}

	public LandingSolutionImage( Long solutionImageId )
	{
		this.solutionImageId = solutionImageId;
	}

	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "created_date", nullable = false, length = 23 )
	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Embedded
	@AttributeOverrides( { @AttributeOverride( name = "data", column = @Column( name = "image_content", nullable = false ) ) } )
	public ImmutableByteArray getImageContent()
	{
		return imageContent;
	}

	@Column( name = "image_dpi_height", nullable = false, precision = 9, scale = 0 )
	public int getImageDpiHeight()
	{
		return imageDpiHeight;
	}

	@Column( name = "image_dpi_width", nullable = false, precision = 9, scale = 0 )
	public int getImageDpiWidth()
	{
		return imageDpiWidth;
	}

	@Column( name = "image_name", nullable = false, length = 50 )
	public String getImageName()
	{
		return imageName;
	}

	@Column( name = "image_size", nullable = false )
	public long getImageSize()
	{
		return imageSize;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "solution_id" )
	public LandingSolution getLandingSolution()
	{
		return landingSolution;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "template_column_id", nullable = false )
	public LandingTemplateColumn getLandingTemplateColumn()
	{
		return landingTemplateColumn;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "solution_image_id", unique = true, nullable = false )
	public Long getSolutionImageId()
	{
		return solutionImageId;
	}

	@Column( name = "solution_temp_id" )
	public Long getSolutionTempId()
	{
		return solutionTempId;
	}

	@Column( name = "current_use", nullable = false, precision = 1, scale = 0 )
	public boolean isCurrentUse()
	{
		return currentUse;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setCurrentUse( boolean currentUse )
	{
		this.currentUse = currentUse;
	}

	public void setImageContent( ImmutableByteArray imageContent )
	{
		this.imageContent = imageContent;
	}

	public void setImageDpiHeight( int imageDpiHeight )
	{
		this.imageDpiHeight = imageDpiHeight;
	}

	public void setImageDpiWidth( int imageDpiWidth )
	{
		this.imageDpiWidth = imageDpiWidth;
	}

	public void setImageName( String imageName )
	{
		this.imageName = imageName;
	}

	public void setImageSize( long imageSize )
	{
		this.imageSize = imageSize;
	}

	public void setLandingSolution( LandingSolution landingSolution )
	{
		this.landingSolution = landingSolution;
	}

	public void setLandingTemplateColumn( LandingTemplateColumn landingTemplateColumn )
	{
		this.landingTemplateColumn = landingTemplateColumn;
	}

	public void setSolutionImageId( Long solutionImageId )
	{
		this.solutionImageId = solutionImageId;
	}

	public void setSolutionTempId( Long solutionTempId )
	{
		this.solutionTempId = solutionTempId;
	}
}