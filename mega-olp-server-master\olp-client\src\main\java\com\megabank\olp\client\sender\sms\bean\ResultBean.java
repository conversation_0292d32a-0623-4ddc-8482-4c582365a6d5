package com.megabank.olp.client.sender.sms.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class ResultBean extends BaseBean
{
	@XmlElement( name = "ErrCode" )
	private String errCode;

	@XmlElement( name = "ErrDesc" )
	private String errDesc;

	public ResultBean()
	{}

	public String getErrCode()
	{
		return errCode;
	}

	public String getErrDesc()
	{
		return errDesc;
	}

	public void setErrCode( String errCode )
	{
		this.errCode = errCode;
	}

	public void setErrDesc( String errDesc )
	{
		this.errDesc = errDesc;
	}

}
