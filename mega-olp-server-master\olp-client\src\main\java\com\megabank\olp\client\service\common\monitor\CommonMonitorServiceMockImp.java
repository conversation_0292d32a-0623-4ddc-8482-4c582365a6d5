package com.megabank.olp.client.service.common.monitor;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.layer.BaseService;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Transactional
@Profile( { "dev" } )
public class CommonMonitorServiceMockImp extends BaseService implements CommonMonitorService
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public void sendByException( String errorCode, String errorMsg, Exception ex, Long tranLogId, Long errorLogId )
	{
		logger.info( "send exception success, exception:{}", ex.getClass().getName() );
	}

}
