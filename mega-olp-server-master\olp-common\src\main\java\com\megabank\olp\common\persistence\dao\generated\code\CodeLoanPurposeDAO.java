package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeLoanPurpose;

/**
 * The CodeLoanPurposeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeLoanPurposeDAO extends BasePojoDAO<CodeLoanPurpose, Long>
{
	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	public List<CodeLoanPurpose> getPojosByProperties( String loanType )
	{
		Validate.notBlank( loanType );

		NameValueBean codeLoanType = new NameValueBean( CodeLoanPurpose.CODE_LOAN_TYPE_CONSTANT, codeLoanTypeDAO.read( loanType ) );
		NameValueBean disabled = new NameValueBean( CodeLoanPurpose.DISABLED_CONSTANT, false );

		NameValueBean[] conditions = new NameValueBean[]{ codeLoanType, disabled };

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeLoanPurpose.DISPLAY_ORDER_CONSTANT ) };

		return this.getPojosByPropertiesOrderBy( conditions, orderBeans );
	}

	@Override
	protected Class<CodeLoanPurpose> getPojoClass()
	{
		return CodeLoanPurpose.class;
	}
}
