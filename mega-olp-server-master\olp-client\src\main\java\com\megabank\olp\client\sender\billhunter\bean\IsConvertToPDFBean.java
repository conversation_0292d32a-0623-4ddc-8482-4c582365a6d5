package com.megabank.olp.client.sender.billhunter.bean;

import java.io.Serializable;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlValue;

@XmlRootElement( name = "IsConvertToPDF" )
@XmlAccessorType( XmlAccessType.FIELD )
public class IsConvertToPDFBean implements Serializable
{
	@XmlAttribute( name = "PDFVertical" )
	private String pdfVertical = "Y";

	@XmlValue
	private String value = "N";

	public IsConvertToPDFBean()
	{}

	public String getPdfVertical()
	{
		return pdfVertical;
	}

	public String getValue()
	{
		return value;
	}

	public void setPdfVertical( String pdfVertical )
	{
		this.pdfVertical = pdfVertical;
	}

	public void setValue( String value )
	{
		this.value = value;
	}

}
