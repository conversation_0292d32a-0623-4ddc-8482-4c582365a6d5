package com.megabank.olp.client.sender.micro.apply.management.house;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactDetailResultBean;

@Component
public class HouseContactDetailClient extends BaseApplyClient<HouseContactArgBean, HouseContactDetailResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/housecontact/getDetail";
	}
}
