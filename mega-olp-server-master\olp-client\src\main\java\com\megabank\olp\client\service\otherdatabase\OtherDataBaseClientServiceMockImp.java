package com.megabank.olp.client.service.otherdatabase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalCustInfoResultBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalPbAccountResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev" } )
public class OtherDataBaseClientServiceMockImp implements OtherDataBaseClientService
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public Boolean checkIsEmployee( String idNo )
	{
		return StringUtils.startsWith( idNo, "E" );
	}

	@Override
	public InternalCustInfoResultBean getInternalCustInfo( String idNo )
	{
		InternalCustInfoResultBean resultBean = new InternalCustInfoResultBean();
		resultBean.setName( "測試者名稱" );
		resultBean.setTitleType( "01" );

		return resultBean;

	}

	@Override
	public List<String> getLoaneeBankCode( String idNo )
	{
		List<String> bankCodes = new ArrayList<>();
		bankCodes.add( "229" );
		bankCodes.add( "048" );
		bankCodes.add( "013" );

		return StringUtils.startsWith( idNo, "L" ) ? bankCodes : new ArrayList<>();
	}

	@Override
	public InternalPbAccountResultBean getPbAccount( String idNo, Date birthDate, String include_currTWD, String include_currNotTWD,
													 String include_digitalAccount_promoted, String include_digitalAccount_unpromoted )
	{
		InternalPbAccountResultBean resultBean = new InternalPbAccountResultBean();

		List<String> result_empty = new ArrayList<>();

		List<String> result_multiple = new ArrayList<>();
		result_multiple.add( "***********" );
		result_multiple.add( "***********" );
		result_multiple.add( "***********" );

		List<String> result_single = new ArrayList<>();
		result_single.add( "***********" );

		if( StringUtils.startsWith( idNo, "A" ) || StringUtils.startsWith( idNo, "E" ) || StringUtils.startsWith( idNo, "F" ) )
			resultBean.setAccountList( result_multiple );
		else if( StringUtils.startsWith( idNo, "Y" ) )
			resultBean.setAccountList( result_empty );
		else
			resultBean.setAccountList( result_single );
		return resultBean;

	}
}
