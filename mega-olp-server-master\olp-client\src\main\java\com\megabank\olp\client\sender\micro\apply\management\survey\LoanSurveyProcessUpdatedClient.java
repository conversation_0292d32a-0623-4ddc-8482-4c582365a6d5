package com.megabank.olp.client.sender.micro.apply.management.survey;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyProcessUpdatedArgBean;

@Component
public class LoanSurveyProcessUpdatedClient extends BaseApplyClient<LoanSurveyProcessUpdatedArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/survey/updateProcessStatus";
	}
}
