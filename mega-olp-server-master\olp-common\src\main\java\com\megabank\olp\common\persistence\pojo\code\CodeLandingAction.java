package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeLandingAction is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_landing_action" )
public class CodeLandingAction extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_landing_action";

	public static final String LANDING_ACTION_CODE_CONSTANT = "landingActionCode";

	public static final String NAME_CONSTANT = "name";

	private String landingActionCode;

	private String name;

	public CodeLandingAction()
	{}

	public CodeLandingAction( String landingActionCode )
	{
		this.landingActionCode = landingActionCode;
	}

	@Id
	@Column( name = "landing_action_code", unique = true, nullable = false, length = 20 )
	public String getLandingActionCode()
	{
		return landingActionCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setLandingActionCode( String landingActionCode )
	{
		this.landingActionCode = landingActionCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}