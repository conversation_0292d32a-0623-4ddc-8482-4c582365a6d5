/**
 *
 */
package com.megabank.olp.client.sender.micro.apply.management.signing;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.ContractCtrTypeCApplyClientArgBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */

@Component
public class ContractCtrTypeCClient extends BaseApplyClient<ContractCtrTypeCApplyClientArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		// 對應到 ContractController.java
		return "/management/signingcontract/sendContractCtrTypeC";
	}
}
