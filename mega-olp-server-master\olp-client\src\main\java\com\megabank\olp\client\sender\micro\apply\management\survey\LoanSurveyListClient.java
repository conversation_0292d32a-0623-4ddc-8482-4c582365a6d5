package com.megabank.olp.client.sender.micro.apply.management.survey;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyListArgBean;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyListResultBean;

@Component
public class LoanSurveyListClient extends BaseApplyClient<LoanSurveyListArgBean, LoanSurveyListResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/survey/list";
	}
}
