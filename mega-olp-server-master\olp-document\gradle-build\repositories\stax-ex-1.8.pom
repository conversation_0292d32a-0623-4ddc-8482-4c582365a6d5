<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2018 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>5</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.jvnet.staxex</groupId>
    <artifactId>stax-ex</artifactId>
    <version>1.8</version>
    <packaging>jar</packaging>

    <name>Extended StAX API</name>
    <inceptionYear>2006</inceptionYear>
    <description>Extensions to JSR-173 StAX API.</description>
    <url>http://stax-ex.java.net/</url>

    <scm>
        <connection>scm:git:ssh://**************/javaee/metro-stax-ex.git</connection>
        <developerConnection>scm:git:ssh://**************/javaee/metro-stax-ex.git</developerConnection>
        <url>https://github.com/javaee/metro-stax-ex</url>
        <tag>1.8</tag>
    </scm>

    <licenses>
        <license>
            <name>
                Dual license consisting of the CDDL v1.1 and GPL v2
            </name>
            <url>https://glassfish.dev.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>bravehorsie</id>
            <name>Roman Grigoriadi</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>zhengjl</id>
            <name>Zheng Jun Li</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/java</sourceDirectory>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.5</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.49</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.7,)</version>
                        </requireJavaVersion>
                        <requireMavenVersion>
                            <version>[3.0.3,)</version>
                        </requireMavenVersion>
                        <!--<DependencyConvergence />-->
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <!--compile all as JPMS module-->
                        <id>default-compile</id>
                        <configuration>
                            <release>9</release>
                        </configuration>
                    </execution>
                    <execution>
                        <!--replace classes with source/target 7-->
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>7</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <release>7</release>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <additionalparam>-Xdoclint:none</additionalparam>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <configuration>
                    <skip>${findbugs.skip}</skip>
                    <threshold>${findbugs.threshold}</threshold>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                    <excludeFilterFile>
                        exclude-common.xml,${findbugs.exclude}
                    </excludeFilterFile>
                    <fork>true</fork>
                    <jvmArgs>-Xms64m -Xmx256m</jvmArgs>    
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.glassfish.findbugs</groupId>
                        <artifactId>findbugs</artifactId>
                        <version>1.7</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <templateFile>${project.basedir}/copyright.txt</templateFile>
                    <excludeFile>${project.basedir}/copyright-exclude</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>true</scmOnly>
                    <!-- turn off warnings -->
                    <warn>true</warn>
                    <!-- for use with repair -->
                    <update>false</update>
                    <!-- check that year is correct -->
                    <ignoreYear>false</ignoreYear>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <configuration>
                    <formats>
                        <format>xml</format>
                    </formats>
                    <check>
                      <totalLineRate>0</totalLineRate>
                      <packageLineRate>0</packageLineRate>
                      <haltOnFailure>true</haltOnFailure>
                    </check>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                    <providerImplementations>
                        <svn>javasvn</svn>
                    </providerImplementations>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <revisionOnScmFailure>false</revisionOnScmFailure>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.tmatesoft.svnkit</groupId>
                        <artifactId>svnkit</artifactId>
                        <version>1.7.4-v1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>  
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>                        
                        <manifestEntries>
                            <Implementation-Build-Id>${scmBranch}-${buildNumber}, ${timestamp}</Implementation-Build-Id>
                        </manifestEntries>
                    </archive> 
                </configuration>
            </plugin>  
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                </configuration>
            </plugin>
        </plugins>

    </build>
	
    <properties>
        <findbugs.exclude />
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>Low</findbugs.threshold>
    </properties>

    <profiles>

        <profile>
            <id>jvnet-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

    </profiles>

</project>
