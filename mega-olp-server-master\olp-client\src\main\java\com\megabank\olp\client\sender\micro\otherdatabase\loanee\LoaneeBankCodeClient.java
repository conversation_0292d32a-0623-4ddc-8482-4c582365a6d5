package com.megabank.olp.client.sender.micro.otherdatabase.loanee;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.loanee.bean.LoaneeBankCodeArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class LoaneeBankCodeClient extends BaseOtherDatabaseClient<LoaneeBankCodeArgBean, List<String>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return String.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/nbots/loanee/getBankCode";
	}

}
