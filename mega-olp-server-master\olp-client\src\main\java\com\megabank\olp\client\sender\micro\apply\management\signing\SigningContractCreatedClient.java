/**
 *
 */
package com.megabank.olp.client.sender.micro.apply.management.signing;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.signing.bean.SigningContractCreatedArgBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

@Component
public class SigningContractCreatedClient extends BaseApplyClient<SigningContractCreatedArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/signingcontract/sendSigningContract";
	}
}
