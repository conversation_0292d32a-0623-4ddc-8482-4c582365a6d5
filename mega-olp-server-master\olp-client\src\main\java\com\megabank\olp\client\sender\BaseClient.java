package com.megabank.olp.client.sender;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.Iterator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.xml.stream.XMLStreamException;

import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.megabank.olp.base.bean.threadlocal.RequestInfoThreadLocalBean;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.exception.MyViolationException;
import com.megabank.olp.base.threadlocal.RequestInfoThreadLocal;
import com.megabank.olp.client.config.ClientPropertyBean;
import com.megabank.olp.client.sender.monitor.exception.MonitorExceptionClient;
import com.megabank.olp.client.sender.monitor.exception.bean.MonitorExceptionArgBean;
import com.megabank.olp.client.utility.bean.EmptyArgBean;
import com.megabank.olp.client.utility.verifier.MyHostnameVerifier;
import com.megabank.olp.system.persistence.dao.generated.SystemTranLogDAO;
import com.megabank.olp.system.service.TranLogService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

import jakarta.xml.bind.JAXBException;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public abstract class BaseClient<TArg, TReq, TRes, TResult>
{
	protected final Logger logger = LogManager.getLogger( getClass() );

	@Value( "${openshift.location}" )
	protected String openshiftLocation;

	@Qualifier( "clientHttpRequestFactory" )
	@Autowired
	private ClientHttpRequestFactory requestFactory;

	@Autowired
	protected RestTemplate restTemplate;

	@Autowired
	protected ClientPropertyBean propertyBean;

	@Autowired
	private MonitorExceptionClient exceptionClient;

	@Autowired
	private SystemTranLogDAO systemTranLogDAO;

	@Autowired
	protected TranLogService tranLogService;

	@Autowired
	protected RequestInfoThreadLocal requestInfoThreadLocal;

	@Autowired
	protected MyHostnameVerifier myHostnameVerifier;

	/**
	 *
	 * @param argBean can be null if there is no any arguments binding to the HTTP request
	 * @return
	 */
	@SuppressWarnings( { "rawtypes", "unchecked" } )
	public TResult send( TArg argBean )
	{
		if( argBean != null )
			validate( argBean );

		TReq reqBean = transArg2ReqToNull( argBean );

		HttpHeaders httpHeaders = getReqHeaders( argBean );
		httpHeaders.set( "system-name", getSystemName() );

		try
		{
			String url = getApiURI( argBean ).toString();

			String requestBody = transReq2ReqBody( reqBean );

			logger.debug( "url:{}", url );
			logger.debug( "headerValues:{}", httpHeaders.toString() );
			logger.debug( "requstBody:{}", getLogForRequestBody( requestBody ) );

			boolean save_log = save_to_system_tran_log();
			Long tranLogId = -1L;
			if( save_log )
			{
				RequestInfoThreadLocalBean bean = requestInfoThreadLocal.get();
				String remoteAddress = bean.getClientAddress();
				boolean ajaxRequest = false;
				String requestContentType = "text";
				String serverAddress = bean.getServerAddress();
				String serverHostName = bean.getServerHostName();
				String requestMethod = getHttpMethod().toString();
				String keep_url = url;
				String sessionId = null;
				tranLogId = systemTranLogDAO.create( remoteAddress, ajaxRequest, requestContentType, requestMethod, keep_url, serverAddress,
													 serverHostName, sessionId );
			}
			HttpEntity<String> reqHttpEntity = new HttpEntity<>( requestBody, httpHeaders );

			setRequestFactory();

			ResponseEntity<String> resEntity = restTemplate.exchange( url, getHttpMethod(), reqHttpEntity, getResType() );

			String resBody = resEntity.getBody();

			logger.debug( "resBody:{}", resBody );

			if( save_log )
			{
				String responseContentType = "text";
				Long elapsed = 0L;
				Long elapsedOtherSystem = 0L;
				systemTranLogDAO.updateToEnding( tranLogId, requestBody, resBody, responseContentType, elapsed, elapsedOtherSystem );
			}
			if( resBody.toLowerCase().contains( "xmlsoap" ) )
			{
				resBody = RegExUtils.removeAll( StringUtils.trimToEmpty( resBody ), "[^a-zA-z0-9+-=?<>:/ \"]" );

				return transRes2Result( transResBody2Res( StringEscapeUtils.escapeHtml4( resBody ) ), getResHeaders( resEntity ) );
			}
			else
				return transRes2Result( transResBody2Res( StringEscapeUtils.unescapeHtml4( StringEscapeUtils.escapeHtml4( resBody ) ) ),
										getResHeaders( resEntity ) );

		}
		catch( IOException | JAXBException | XMLStreamException exception )
		{
			throw new MyRuntimeException( SystemErrorEnum.INTERNAL, null, exception );
		}
		catch( ResourceAccessException | HttpClientErrorException | HttpServerErrorException exception )
		{
			if( isMonitor() )
				exceptionClient.send( new MonitorExceptionArgBean( getSystemName(), String.format( "connect %s is error", getSystemName() ) ) );

			throw new MyRuntimeException( SystemErrorEnum.CONNECTED_OTHER_SYSTEM, getSystemName(), exception );
		}

	}

	protected void doReqHeaders( HttpHeaders httpHeaders, TArg argBean )
	{}

	/**
	 *
	 * @param argBean
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	protected abstract URI getApiURI( TArg argBean ) throws UnsupportedEncodingException;

	protected abstract String getContentType();

	/**
	 *
	 * @return
	 */
	protected abstract HttpMethod getHttpMethod();

	protected String getLogForRequestBody( String requestBody )
	{
		return requestBody;
	}

	/**
	 *
	 * @param argBean
	 * @return
	 */
	protected HttpHeaders getReqHeaders( TArg argBean )
	{
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.add( HttpHeaders.CONTENT_TYPE, getContentType() );
		httpHeaders.add( "x-mock-match-request-headers", "simulator" );
		httpHeaders.add( "simulator", getSimulatorCode( argBean ) );

		doReqHeaders( httpHeaders, argBean );

		return httpHeaders;
	}

	/**
	 *
	 * @param resEntity
	 * @return
	 */
	protected HttpHeaders getResHeaders( ResponseEntity resEntity )
	{
		return resEntity.getHeaders();
	}

	protected Class getResType()
	{
		return String.class;
	}

	protected abstract String getSimulatorCode( TArg argBean );

	/**
	 *
	 * @return
	 */
	protected abstract String getSystemName();

	protected boolean isMonitor()
	{
		return true;
	}

	protected boolean save_to_system_tran_log()
	{
		return false;
	}

	protected void setRequestFactory()
	{
		logger.info( "use clientHttpRequestFactory" );

		restTemplate.setRequestFactory( requestFactory );
	}

	/**
	 *
	 * @param argBean
	 * @return
	 */
	protected abstract TReq transArg2Req( TArg argBean );

	protected TReq transArg2ReqToNull( TArg argBean )
	{
		if( argBean instanceof EmptyArgBean )
			return null;

		return transArg2Req( argBean );
	}

	/**
	 *
	 * @param reqBean
	 * @return
	 * @throws IOException
	 * @throws JAXBException
	 */
	protected abstract String transReq2ReqBody( TReq reqBean ) throws IOException, JAXBException;

	/**
	 * @param responseBean
	 * @param httpHeaders
	 * @return
	 */
	protected abstract TResult transRes2Result( TRes resBean, HttpHeaders httpHeaders );

	/**
	 *
	 * @param resBody
	 * @return
	 * @throws IOException
	 * @throws XMLStreamException
	 */
	protected abstract TRes transResBody2Res( String resBody ) throws IOException, JAXBException, XMLStreamException;

	/**
	 * @param reqBean
	 */
	protected void validate( TArg argBean )
	{
		StringBuilder builder = new StringBuilder();

		int invalidCount = 0;

		Iterator<ConstraintViolation<TArg>> iterator = Validation.buildDefaultValidatorFactory().getValidator().validate( argBean ).iterator();

		if( iterator != null )
			while( iterator.hasNext() )
		{
			ConstraintViolation<TArg> violation = iterator.next();

			if( invalidCount != 0 )
				builder.append( "," );

			builder.append( violation.getMessage() );
			invalidCount++;

			if( invalidCount > Integer.MAX_VALUE )
				break;
		}

		if( invalidCount > 0 )
			throw new MyViolationException( builder.toString() );
	}

}