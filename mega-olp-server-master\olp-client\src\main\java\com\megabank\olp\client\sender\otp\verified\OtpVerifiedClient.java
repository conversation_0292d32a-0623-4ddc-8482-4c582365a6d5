package com.megabank.olp.client.sender.otp.verified;

import java.io.UnsupportedEncodingException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.bean.threadlocal.RequestInfoThreadLocalBean;
import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.threadlocal.RequestInfoThreadLocal;
import com.megabank.olp.client.sender.otp.BaseOtpClient;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedArgBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedReqBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedResBean;
import com.megabank.olp.client.sender.otp.verified.bean.OtpVerifiedResultBean;
import com.megabank.olp.client.utility.enums.ClientOtpErrorEnum;
import com.megabank.olp.system.persistence.dao.generated.SystemTranLogDAO;
import com.megabank.olp.system.service.TranLogService;

@Component
public class OtpVerifiedClient extends BaseOtpClient<OtpVerifiedArgBean, OtpVerifiedReqBean, OtpVerifiedResBean, OtpVerifiedResultBean>
{

	@Autowired
	private SystemTranLogDAO systemTranLogDAO;

	@Autowired
	protected TranLogService tranLogService;

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000", "S008", "S009", "S010" };
	}

	@Override
	protected ErrorEnum getOtpCommonErrorEnum()
	{
		return ClientOtpErrorEnum.VARIFIED_SMS_COMMON;
	}

	@Override
	protected OtpVerifiedResultBean getResultBean( String returnCode )
	{
		if( "S008".equals( returnCode ) )
			throw new MyRuntimeException( ClientOtpErrorEnum.VARIFIED_SMS_EXPIRED );

		if( "S010".equals( returnCode ) )
			throw new MyRuntimeException( ClientOtpErrorEnum.VARIFIED_SMS_HAS_BEEN );

		if( !"0000".equals( returnCode ) )
		{
			String log_url = getSystemName();
			String requestData = "";

			try
			{
				String apiURI = getApiURI( new OtpVerifiedArgBean() ).toString();
				log_url = apiURI;
				requestData = "{" + "apiURI: " + apiURI + ", debug: select top 5 * from dbo.identity_otp order by otp_id desc" + "}";
				// ~~~~~~~~~
				int idx = log_url.indexOf( "/megaOTPSMS/sms/api" );
				if( idx > 1 )
					log_url = StringUtils.substring( log_url, idx );
			}
			catch( UnsupportedEncodingException e )
			{

			}

			RequestInfoThreadLocalBean bean = requestInfoThreadLocal.get();
			String requestMethod = "POST";
			String remoteAddress = bean.getClientAddress();
			boolean ajaxRequest = false;
			String serverAddress = bean.getServerAddress();
			String serverHostName = bean.getServerHostName();
			String sessionId = null;
			if( true )
			{
				String requestContentType = "text";

				Long tranLogId = systemTranLogDAO.create( remoteAddress, ajaxRequest, requestContentType, requestMethod, "[to]" + log_url,
														  serverAddress, serverHostName, sessionId );

				String responseData = "{returnCode:" + returnCode + "}";
				String responseContentType = "text";
				Long elapsed = 0L;
				Long elapsedOtherSystem = 0L;
				systemTranLogDAO.updateToEnding( tranLogId, requestData, responseData, responseContentType, elapsed, elapsedOtherSystem );
			}
		}

		OtpVerifiedResultBean resultBean = new OtpVerifiedResultBean();
		resultBean.setVerifiedResult( "0000".equals( returnCode ) );

		return resultBean;
	}

	@Override
	protected String getSimulatorCode( OtpVerifiedArgBean argBean )
	{
		String captcha = argBean.getCaptcha();

		if( StringUtils.startsWith( captcha, "00" ) )
			return "S" + StringUtils.right( captcha, 3 );

		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/VerifySmsOTP";
	}

	@Override
	protected boolean save_to_system_tran_log()
	{
		return true;
	}

	@Override
	protected OtpVerifiedReqBean transArg2Req( OtpVerifiedArgBean argBean )
	{
		OtpVerifiedReqBean reqBean = new OtpVerifiedReqBean();
		reqBean.setCaptcha( argBean.getCaptcha() );
		reqBean.setMobileNumber( argBean.getMobileNumber() );

		return reqBean;
	}
}
