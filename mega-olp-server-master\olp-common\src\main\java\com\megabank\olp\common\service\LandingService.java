package com.megabank.olp.common.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.layer.BaseService;
import com.megabank.olp.base.utility.text.CommonBase64Utils;
import com.megabank.olp.common.persistence.dao.generated.landing.LandingSolutionDAO;
import com.megabank.olp.common.persistence.dao.generated.landing.LandingSolutionImageDAO;
import com.megabank.olp.common.persistence.dao.generated.landing.LandingSolutionSubValueDAO;
import com.megabank.olp.common.persistence.dao.generated.landing.LandingSolutionValueDAO;
import com.megabank.olp.common.persistence.dao.generated.landing.LandingTemplateColumnDAO;
import com.megabank.olp.common.persistence.dao.mixed.LandingDAO;
import com.megabank.olp.common.persistence.pojo.landing.LandingSolution;
import com.megabank.olp.common.persistence.pojo.landing.LandingSolutionImage;
import com.megabank.olp.common.persistence.pojo.landing.LandingSolutionSubValue;
import com.megabank.olp.common.persistence.pojo.landing.LandingSolutionValue;
import com.megabank.olp.common.persistence.pojo.landing.LandingTemplateColumn;
import com.megabank.olp.common.service.bean.landing.BaseColumnResBean;
import com.megabank.olp.common.service.bean.landing.BooleanColumnResBean;
import com.megabank.olp.common.service.bean.landing.NumberColumnResBean;
import com.megabank.olp.common.service.bean.landing.RiskInfoResBean;
import com.megabank.olp.common.service.bean.landing.TextColumnResBean;
import com.megabank.olp.common.service.bean.landing.TextGroupBean;
import com.megabank.olp.common.service.bean.landing.TextGroupColumnResBean;
import com.megabank.olp.common.service.bean.landing.TextListColumnResBean;

@Service
@Transactional
public class LandingService extends BaseService
{
	@Autowired
	private LandingDAO landingDAO;

	@Autowired
	private LandingTemplateColumnDAO landingTemplateColumnDAO;

	@Autowired
	private LandingSolutionDAO landingSolutionDAO;

	@Autowired
	private LandingSolutionImageDAO landingSolutionImageDAO;

	@Autowired
	private LandingSolutionValueDAO landingSolutionValueDAO;

	@Autowired
	private LandingSolutionSubValueDAO landingSolutionSubValueDAO;

	/**
	 * 取得行銷頁內容
	 *
	 * @param templateName
	 * @return
	 */
	public List<List<BaseColumnResBean>> getColumnList( String templateName )
	{
		List<List<BaseColumnResBean>> resBeans = new ArrayList<>();

		List<Long> solutionIds = landingDAO.getInUseSolution( "web-page", templateName );

		for( Long solutionId : solutionIds )
			resBeans.add( mapColumnListResBean( solutionId ) );

		return resBeans;
	}

	/**
	 * 取得試算方案內容
	 *
	 * @param riskLevel
	 * @return
	 */
	public RiskInfoResBean getRiskInfo( String riskLevel )
	{
		List<Long> solutionIds = landingDAO.getInUseSolution( "trial", riskLevel );

		if( solutionIds.isEmpty() )
			return new RiskInfoResBean();

		return mapRiskInfoResBean( solutionIds.get( 0 ) );
	}

	private String getValue( Long solutionId, Long templateColumnId )
	{
		List<LandingSolutionValue> solutionValues = landingSolutionValueDAO.getPojosBySolutionId( solutionId, templateColumnId );

		return solutionValues.isEmpty() ? null : solutionValues.get( 0 ).getValue();
	}

	private List<BaseColumnResBean> mapColumnListResBean( Long solutionId )
	{
		LandingSolution solution = landingSolutionDAO.read( solutionId );

		List<LandingTemplateColumn> templateColumnList = landingTemplateColumnDAO
					.getPojosByTemplateOrder( solution.getLandingTemplate().getTemplateId() );

		List<BaseColumnResBean> resBeans = new ArrayList<>();

		for( LandingTemplateColumn templateColumn : templateColumnList )
		{
			Long templateColumnId = templateColumn.getTemplateColumnId();
			String columnType = templateColumn.getColumnType();

			if( "image".equals( columnType ) )
				resBeans.add( mapSolutionImageColumnBean( templateColumn,
														  landingSolutionImageDAO.getPojosBySolutionId( solutionId, templateColumnId ) ) );
			else
				resBeans.add( mapSolutionValueColumnBean( templateColumn,
														  landingSolutionValueDAO.getPojosBySolutionId( solutionId, templateColumnId ) ) );

		}

		return resBeans;
	}

	private BaseColumnResBean mapNumberColumnResBean( LandingTemplateColumn templateColumn, LandingSolutionValue landingSolutionValue )
	{
		NumberColumnResBean columnResBean = new NumberColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setValue( new BigDecimal( landingSolutionValue.getValue() ) );

		return columnResBean;
	}

	private BaseColumnResBean mapRadioButtonColumnResBean( LandingTemplateColumn templateColumn, LandingSolutionValue landingSolutionValue )
	{
		BooleanColumnResBean columnResBean = new BooleanColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setValue( BooleanUtils.toBoolean( landingSolutionValue.getValue() ) );

		return columnResBean;
	}

	private RiskInfoResBean mapRiskInfoResBean( Long solutionId )
	{
		RiskInfoResBean resBean = new RiskInfoResBean();

		LandingSolution solution = landingSolutionDAO.read( solutionId );

		List<LandingTemplateColumn> templateColumnList = landingTemplateColumnDAO
					.getPojosByTemplateOrder( solution.getLandingTemplate().getTemplateId() );

		for( LandingTemplateColumn templateColumn : templateColumnList )
		{
			Long templateColumnId = templateColumn.getTemplateColumnId();
			String columnName = templateColumn.getColumnName();

			String value = getValue( solutionId, templateColumnId );

			if( StringUtils.isBlank( value ) )
				continue;

			if( "risk-result".equals( columnName ) )
				resBean.setHasResult( BooleanUtils.toBoolean( value ) );
			else if( "risk-rate".equals( columnName ) )
				resBean.setRiskRate( new BigDecimal( value ) );
			else if( "risk-double".equals( columnName ) )
				resBean.setRiskDouble( NumberUtils.toInt( value ) );

		}

		return resBean;
	}

	private BaseColumnResBean mapSolutionImageColumnBean( LandingTemplateColumn templateColumn, LandingSolutionImage landingSolutionImage )
	{
		TextColumnResBean columnResBean = new TextColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setValue( CommonBase64Utils.base64Encoder( landingSolutionImage.getImageContent() ) );

		return columnResBean;
	}

	private BaseColumnResBean mapSolutionImageColumnBean( LandingTemplateColumn templateColumn, List<LandingSolutionImage> landingSolutionImage )
	{
		return mapSolutionImageColumnBean( templateColumn, landingSolutionImage.get( 0 ) );
	}

	private BaseColumnResBean mapSolutionValueColumnBean( LandingTemplateColumn templateColumn, List<LandingSolutionValue> landingSolutionValue )
	{
		String columnType = templateColumn.getColumnType();

		if( "integer".equals( columnType ) || "decimal".equals( columnType ) )
			return mapNumberColumnResBean( templateColumn, landingSolutionValue.get( 0 ) );
		else if( "radio-button".equals( columnType ) )
			return mapRadioButtonColumnResBean( templateColumn, landingSolutionValue.get( 0 ) );
		else if( "text-list".equals( columnType ) )
			return mapTextListColumnResBean( templateColumn, landingSolutionValue );
		else if( "text-group".equals( columnType ) )
			return mapTextGroupColumnResBean( templateColumn, landingSolutionValue );
		else
			return mapTextColumnResBean( templateColumn, landingSolutionValue.get( 0 ) );

	}

	private BaseColumnResBean mapTextColumnResBean( LandingTemplateColumn templateColumn, LandingSolutionValue landingSolutionValue )
	{
		TextColumnResBean columnResBean = new TextColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setValue( landingSolutionValue.getValue() );

		return columnResBean;
	}

	private List<TextGroupBean> mapTextGroupBeans( List<LandingSolutionValue> landingSolutionValue )
	{
		List<TextGroupBean> textGroupBeans = new ArrayList<>();
		for( LandingSolutionValue solutionValue : landingSolutionValue )
		{
			List<LandingSolutionSubValue> solutionSubValues = landingSolutionSubValueDAO
						.getPojosBySolutionValueId( solutionValue.getSolutionValueId() );

			TextGroupBean textGroupBean = new TextGroupBean();
			textGroupBean.setName( solutionValue.getValue() );
			textGroupBean.setElements( solutionSubValues.stream().map( LandingSolutionSubValue::getValue ).collect( Collectors.toList() ) );

			textGroupBeans.add( textGroupBean );
		}

		return textGroupBeans;
	}

	private BaseColumnResBean mapTextGroupColumnResBean( LandingTemplateColumn templateColumn, List<LandingSolutionValue> landingSolutionValue )
	{
		TextGroupColumnResBean columnResBean = new TextGroupColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setTextGroupBeans( mapTextGroupBeans( landingSolutionValue ) );

		return columnResBean;
	}

	private BaseColumnResBean mapTextListColumnResBean( LandingTemplateColumn templateColumn, List<LandingSolutionValue> landingSolutionValue )
	{
		TextListColumnResBean columnResBean = new TextListColumnResBean();
		columnResBean.setColumnId( templateColumn.getTemplateColumnId() );
		columnResBean.setColumnName( templateColumn.getColumnName() );
		columnResBean.setColumnTitle( templateColumn.getColumnTitle() );
		columnResBean.setColumnType( templateColumn.getColumnType() );
		columnResBean.setValue( landingSolutionValue.stream().map( LandingSolutionValue::getValue ).collect( Collectors.toList() ) );

		return columnResBean;
	}

}
