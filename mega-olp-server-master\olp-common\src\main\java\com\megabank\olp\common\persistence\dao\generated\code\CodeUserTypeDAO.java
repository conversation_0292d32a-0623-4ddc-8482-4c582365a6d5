package com.megabank.olp.common.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeUserType;

/**
 * The CodeUserTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeUserTypeDAO extends BasePojoDAO<CodeUserType, String>
{

	public CodeUserType read( String userType )
	{
		Validate.notBlank( userType );

		return getPojoByPK( userType, CodeUserType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeUserType> getPojoClass()
	{
		return CodeUserType.class;
	}
}
