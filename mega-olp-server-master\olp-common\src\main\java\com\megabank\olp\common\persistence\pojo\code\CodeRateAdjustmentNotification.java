package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeRateAdjustmentNotification is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_rate_adjustment_notification" )
public class CodeRateAdjustmentNotification extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_rate_adjustment_notification";

	public static final String RATE_ADJUSTMENT_NOTIFICAITON_CODE_CONSTANT = "rateAdjustmentNotificaitonCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String DISABLED_CONSTANT = "disabled";

	private String rateAdjustmentNotificaitonCode;

	private String name;

	private int displayOrder;

	private boolean disabled;

	public CodeRateAdjustmentNotification()
	{}

	public CodeRateAdjustmentNotification( String rateAdjustmentNotificaitonCode )
	{
		this.rateAdjustmentNotificaitonCode = rateAdjustmentNotificaitonCode;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "rate_adjustment_notificaiton_code", unique = true, nullable = false, length = 20 )
	public String getRateAdjustmentNotificaitonCode()
	{
		return rateAdjustmentNotificaitonCode;
	}

	@Column( name = "disabled", nullable = false, precision = 1, scale = 0 )
	public boolean isDisabled()
	{
		return disabled;
	}

	public void setDisabled( boolean disabled )
	{
		this.disabled = disabled;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setRateAdjustmentNotificaitonCode( String rateAdjustmentNotificaitonCode )
	{
		this.rateAdjustmentNotificaitonCode = rateAdjustmentNotificaitonCode;
	}
}