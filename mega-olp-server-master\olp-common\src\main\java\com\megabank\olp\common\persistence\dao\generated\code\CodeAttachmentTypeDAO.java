package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeAttachmentType;

import org.springframework.stereotype.Repository;

/**
 * The CodeAttachmentTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeAttachmentTypeDAO extends BasePojoDAO<CodeAttachmentType, String>
{
	@Override
	protected Class<CodeAttachmentType> getPojoClass()
	{
		return CodeAttachmentType.class;
	}
}
