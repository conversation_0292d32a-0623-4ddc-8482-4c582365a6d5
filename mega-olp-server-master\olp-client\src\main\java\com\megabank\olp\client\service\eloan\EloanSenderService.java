package com.megabank.olp.client.service.eloan;

import java.util.Date;

import com.megabank.olp.client.sender.eloan.getted.agreedDate.bean.EloanAgreedDateResultBean;
import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusResultBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.collateral.bean.ProviderAgreementSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.houseloan.bean.HouseLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.mydata.bean.MyDataSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.personalloan.bean.PersonalLoanApplySubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public interface EloanSenderService
{
	public EloanCaseStatusResultBean getCaseStatusList( String idNo, Date birthDate );

	public EloanCustInfoResultBean getEloanCustInfo( String idNo, Date birthDate );

	public EloanSubmittedResultBean submitAttachment( AttachmentSubmittedArgBean argBean );

	public EloanSubmittedResultBean submitHouseLoanApply( HouseLoanApplySubmittedArgBean argBean );

	public EloanSubmittedResultBean submitMyData( MyDataSubmittedArgBean argBean );

	public EloanSubmittedResultBean submitPersonalLoanApply( PersonalLoanApplySubmittedArgBean argBean );

	public EloanSubmittedResultBean submitProviderAgreement( ProviderAgreementSubmittedArgBean argBean );

	public EloanSubmittedResultBean submitSigningContract( SigningContractCompletedArgBean argBean );

	public EloanAgreedDateResultBean getAgreedDate(String idNo, Date birthDate, String loanType );
}
