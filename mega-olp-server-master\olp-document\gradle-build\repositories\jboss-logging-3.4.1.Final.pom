
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.jboss.logging</groupId>
    <artifactId>jboss-logging</artifactId>
    <version>3.4.1.Final</version>
    <packaging>jar</packaging>
    <name>JBoss Logging 3</name>
    <url>http://www.jboss.org</url>
    <description>The JBoss Logging Framework</description>
    <scm>
        <connection>scm:git:git://github.com/jboss-logging/jboss-logging.git</connection>
        <url>https://github.com/jboss-logging/jboss-logging</url>
    </scm>

    <parent>
        <groupId>org.jboss</groupId>
        <artifactId>jboss-parent</artifactId>
        <version>34</version>
    </parent>

    <licenses>
        <license>
            <name>Apache License, version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <!-- Dependency versions -->
        <version.org.apache.log4j>1.2.16</version.org.apache.log4j>
        <version.org.apache.logging.log4j>2.11.2</version.org.apache.logging.log4j>
        <version.org.jboss.logmanager>2.1.9.Final</version.org.jboss.logmanager>
        <version.org.sfl4j>1.7.25</version.org.sfl4j>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jboss.logmanager</groupId>
            <artifactId>jboss-logmanager</artifactId>
            <version>${version.org.jboss.logmanager}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${version.org.apache.log4j}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.jdmk</groupId>
                    <artifactId>jmxtools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jmx</groupId>
                    <artifactId>jmxri</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${version.org.apache.logging.log4j}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${version.org.sfl4j}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <!-- Adding OSGI metadata to the JAR without changing the packaging type. -->
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultSpecificationEntries>false</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestEntries>
                            <Automatic-Module-Name>org.jboss.logging</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                    <instructions>
                        <Export-Package>
                            ${project.groupId}.*;version=${project.version};-split-package:=error
                        </Export-Package>
                        <Import-Package>
                            org.apache.log4j.config;resolution:=optional,
                            *;resolution:=optional
                        </Import-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
