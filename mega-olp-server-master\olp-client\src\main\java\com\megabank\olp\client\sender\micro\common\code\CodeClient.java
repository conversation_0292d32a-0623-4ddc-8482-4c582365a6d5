package com.megabank.olp.client.sender.micro.common.code;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.common.BaseCommonClient;
import com.megabank.olp.client.sender.micro.common.code.bean.code.CodeArgBean;
import com.megabank.olp.client.sender.micro.common.code.bean.code.CodeResultBean;

@Component
public class CodeClient extends BaseCommonClient<CodeArgBean, List<CodeResultBean>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return CodeResultBean.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/open/code/getCodeList";
	}
}
