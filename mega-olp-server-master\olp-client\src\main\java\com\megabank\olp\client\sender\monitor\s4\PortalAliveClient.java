package com.megabank.olp.client.sender.monitor.s4;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.monitor.BaseMonitorClient;
import com.megabank.olp.client.sender.monitor.bean.EventMsgBean;
import com.megabank.olp.client.sender.monitor.bean.MsgObjBean;
import com.megabank.olp.client.sender.monitor.bean.SendBean;
import com.megabank.olp.client.sender.monitor.bean.SoapBodyBean;
import com.megabank.olp.client.sender.monitor.s4.bean.PortalAliveArgBean;
import com.megabank.olp.client.sender.monitor.s4.bean.PortalAliveReqBean;

@Component
public class PortalAliveClient extends BaseMonitorClient<PortalAliveArgBean, PortalAliveReqBean>
{

	@Override
	protected String getSimulatorCode( PortalAliveArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/WEBSERVICE/SERVICE1.asmx";
	}

	@Override
	protected PortalAliveReqBean transArg2Req( PortalAliveArgBean argBean )
	{
		String msgCode = "tp".equals( openshiftLocation ) ? "000003" : "000004";
		String occurDate = new SimpleDateFormat( "yyyyMMddHHmmssSS" ).format( new Date() );
		occurDate = occurDate.length() > 16 ? occurDate.substring( 0, 16 ) : StringUtils.rightPad( occurDate, 16, "0" );

		EventMsgBean eventMsgBean = new EventMsgBean();
		eventMsgBean.setMsgCode( msgCode );
		eventMsgBean.setHostName( "PLOAN" );
		eventMsgBean.setPgcName( "PLOAN_S4" );
		eventMsgBean.setCtiCode( "PLOAN" );
		eventMsgBean.setPlatform( "LINUX" );
		eventMsgBean.setEventSource( "A" );
		eventMsgBean.setDataType( "S4" );
		eventMsgBean.setIsOverwrite( "N" );
		eventMsgBean.setStatus( "00" );
		eventMsgBean.setParamCount( eventMsgBean.getEventMsgDetailBeans().size() );
		eventMsgBean.setOccurDate( occurDate );

		MsgObjBean msgObjBean = new MsgObjBean();
		msgObjBean.setEventMsgBean( eventMsgBean );

		SendBean sendBean = new SendBean();
		sendBean.setMsgObjBean( msgObjBean );

		SoapBodyBean soapBodyBean = new SoapBodyBean();
		soapBodyBean.setSendBean( sendBean );

		PortalAliveReqBean reqBean = new PortalAliveReqBean();
		reqBean.setSoapBodyBean( soapBodyBean );

		return reqBean;
	}

}
