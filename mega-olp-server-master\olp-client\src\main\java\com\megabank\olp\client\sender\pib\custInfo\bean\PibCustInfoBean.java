/**
 *
 */
package com.megabank.olp.client.sender.pib.custInfo.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class PibCustInfoBean extends BaseBean
{
	@JsonProperty( "USER_IXD" )
	private String idNo;

	@JsonProperty( "BIRTH_DATE" )
	private String birthDate;

	@JsonProperty( "NAME" )
	private String name;

	@JsonProperty( "BUSINESS_CODE" )
	private String businessCode;

	@JsonProperty( "MPHONE_NC" )
	private String mobileNC;

	@JsonProperty( "MPHONE_NO" )
	private String mobileNumber;

	@JsonProperty( "EMAIL" )
	private String email;

	@JsonProperty( "AGEN_BR" )
	private String age;

	@JsonProperty( "ASSIST_FLAG" )
	private String assistFlag;

	public String getAge()
	{
		return age;
	}

	public String getAssistFlag()
	{
		return assistFlag;
	}

	public String getBirthDate()
	{
		return birthDate;
	}

	public String getBusinessCode()
	{
		return businessCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNC()
	{
		return mobileNC;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public void setAge( String age )
	{
		this.age = age;
	}

	public void setAssistFlag( String assistFlag )
	{
		this.assistFlag = assistFlag;
	}

	public void setBirthDate( String birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setBusinessCode( String businessCode )
	{
		this.businessCode = businessCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNC( String mobileNC )
	{
		this.mobileNC = mobileNC;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}
