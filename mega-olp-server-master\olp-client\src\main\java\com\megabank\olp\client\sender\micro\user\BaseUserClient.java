package com.megabank.olp.client.sender.micro.user;

import java.io.UnsupportedEncodingException;
import java.net.URI;

import com.megabank.olp.client.sender.micro.BaseMicroServicesClient;

public abstract class BaseUserClient<TArg, TResult> extends BaseMicroServicesClient<TArg, TResult>
{
	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlMicroUser() + getSuffixUrl() );
	}

	@Override
	protected String getSystemName()
	{
		return "user";
	}
}
