package com.megabank.olp.client.sender.micro.apply.management.house;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.HouseLoanTrialListClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialListedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialListedResultBean;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ClientServiceConfig.class, BaseServiceConfig.class, SystemConfig.class } )
public class HouseLoanTrialListClientIntegration
{
	@Autowired
	private HouseLoanTrialListClient client;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void send()
	{
		int page = 1;

		HouseLoanTrialListedArgBean argBean = new HouseLoanTrialListedArgBean();
		argBean.setPage( page );

		HouseLoanTrialListedResultBean resultBean = client.send( argBean, new JwtArgBean() );

		logger.info( "resultBean:{}", resultBean );

	}
}
