package com.megabank.olp.client.sender.micro.modal.survey;

import java.math.BigDecimal;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.modal.survey.SurveyResultClient;
import com.megabank.olp.client.sender.micro.modal.survey.bean.SurveyResultArgBean;
import com.megabank.olp.client.sender.micro.modal.survey.bean.SurveyResultResultBean;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ClientServiceConfig.class, BaseServiceConfig.class, SystemConfig.class } )
public class SurveyResultClientIntegration
{
	@Autowired
	private SurveyResultClient client;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void send()
	{
		SurveyResultArgBean argBean = getSurveyResultArgBean();

		SurveyResultResultBean result = client.send( argBean, new JwtArgBean() );

		logger.info( "result:{}", result );
	}

	private SurveyResultArgBean getSurveyResultArgBean()
	{
		int annualIncome = 100;
		BigDecimal debitCardTotalAmt = new BigDecimal( 0 );
		BigDecimal holdingPersonalLoan = new BigDecimal( 10 );
		BigDecimal revovingCredit = new BigDecimal( 0 );
		String riskLevel = "low";

		SurveyResultArgBean paramBean = new SurveyResultArgBean();
		paramBean.setAnnualIncome( annualIncome );
		paramBean.setDebitCardTotalAmt( debitCardTotalAmt );
		paramBean.setHoldingPersonalLoan( holdingPersonalLoan );
		paramBean.setRevovingCredit( revovingCredit );
		paramBean.setRiskLevel( riskLevel );

		return paramBean;
	}
}
