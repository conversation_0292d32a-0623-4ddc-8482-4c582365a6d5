package com.megabank.olp.common.persistence.dao.generated.landing;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.landing.LandingTemplate;

/**
 * The LandingTemplateDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class LandingTemplateDAO extends BasePojoDAO<LandingTemplate, Long>
{
	public LandingTemplate read( Long templateId )
	{
		Validate.notNull( templateId );

		return getPojoByPK( templateId, LandingTemplate.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<LandingTemplate> getPojoClass()
	{
		return LandingTemplate.class;
	}
}
