package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeLandingRequestType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_landing_request_type" )
public class CodeLandingRequestType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_landing_request_type";

	public static final String LANDING_REQUEST_TYPE_CONSTANT = "landingRequestType";

	public static final String NAME_CONSTANT = "name";

	private String landingRequestType;

	private String name;

	public CodeLandingRequestType()
	{}

	public CodeLandingRequestType( String landingRequestType )
	{
		this.landingRequestType = landingRequestType;
	}

	@Id
	@Column( name = "landing_request_type", unique = true, nullable = false, length = 20 )
	public String getLandingRequestType()
	{
		return landingRequestType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setLandingRequestType( String landingRequestType )
	{
		this.landingRequestType = landingRequestType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}