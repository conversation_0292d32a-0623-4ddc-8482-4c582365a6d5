package com.megabank.olp.client.sender.billhunter.bean;

import java.io.Serializable;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlValue;

@XmlRootElement( name = "Message" )
@XmlAccessorType( XmlAccessType.FIELD )
public class MessageBean implements Serializable
{
	@XmlAttribute( name = "type" )
	private String type;

	@XmlValue
	private String value;

	public MessageBean()
	{}

	public String getType()
	{
		return type;
	}

	public String getValue()
	{
		return value;
	}

	public void setType( String type )
	{
		this.type = type;
	}

	public void setValue( String value )
	{
		this.value = value;
	}

}
