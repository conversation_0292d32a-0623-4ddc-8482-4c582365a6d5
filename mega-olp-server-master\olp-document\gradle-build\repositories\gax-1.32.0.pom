<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api</groupId>
  <artifactId>gax</artifactId>
  <version>1.32.0</version>
  <name>GAX (Google Api eXtensions)</name>
  <description>Google Api eXtensions</description>
  <url>https://github.com/googleapis</url>
  <licenses>
    <license>
      <name>BSD</name>
      <url>https://github.com/googleapis/gax-java/blob/master/LICENSE</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>GoogleAPIs</id>
      <name>GoogleAPIs</name>
      <email><EMAIL></email>
      <url>https://github.com/googleapis</url>
      <organization>org.apache.maven.model.Organization@1a82eeb4</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/googleapis/gax-java.git</connection>
    <url>https://github.com/googleapis/gax-java</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value</artifactId>
      <version>1.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava-jdk5</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>20.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava-jdk5</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
      <version>1.3.3</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava-jdk5</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
      <version>0.11.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava-jdk5</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
      <version>1.7.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava-jdk5</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.21.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>2.0.0-beta.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <version>2.0.0-beta.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.truth</groupId>
      <artifactId>truth</artifactId>
      <version>0.42</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
