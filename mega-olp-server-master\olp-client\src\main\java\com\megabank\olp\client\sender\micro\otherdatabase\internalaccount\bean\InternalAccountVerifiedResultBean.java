/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import java.util.Collections;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class InternalAccountVerifiedResultBean extends BaseBean
{
	private Boolean isDepositor;

	private Boolean isLoanee;

	private Boolean matchIdNo;

	private Boolean matchBirthDate;

	private List<String> mobileNumberList = Collections.emptyList();

	private String dataSource;

	private Boolean isBuckleCust;

	private Boolean isDeathCust;

	public InternalAccountVerifiedResultBean()
	{}

	public String getDataSource()
	{
		return dataSource;
	}

	public Boolean getIsBuckleCust()
	{
		return isBuckleCust;
	}

	public Boolean getIsDeathCust()
	{
		return isDeathCust;
	}

	public Boolean getIsDepositor()
	{
		return isDepositor;
	}

	public Boolean getIsLoanee()
	{
		return isLoanee;
	}

	public Boolean getMatchBirthDate()
	{
		return matchBirthDate;
	}

	public Boolean getMatchIdNo()
	{
		return matchIdNo;
	}

	public List<String> getMobileNumberList()
	{
		return mobileNumberList;
	}

	public void setDataSource( String dataSource )
	{
		this.dataSource = dataSource;
	}

	public void setIsBuckleCust( Boolean isBuckleCust )
	{
		this.isBuckleCust = isBuckleCust;
	}

	public void setIsDeathCust( Boolean isDeathCust )
	{
		this.isDeathCust = isDeathCust;
	}

	public void setIsDepositor( Boolean isDepositor )
	{
		this.isDepositor = isDepositor;
	}

	public void setIsLoanee( Boolean isLoanee )
	{
		this.isLoanee = isLoanee;
	}

	public void setMatchBirthDate( Boolean matchBirthDate )
	{
		this.matchBirthDate = matchBirthDate;
	}

	public void setMatchIdNo( Boolean matchIdNo )
	{
		this.matchIdNo = matchIdNo;
	}

	public void setMobileNumberList( List<String> mobileNumberList )
	{
		this.mobileNumberList = mobileNumberList;
	}

}
