<?xml version="1.0" encoding="utf-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.yaml</groupId>
    <artifactId>snakeyaml</artifactId>
    <version>2.0</version>
    <packaging>bundle</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.scm.id>bitbucket</project.scm.id>
        <release.repo.url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</release.repo.url>
        <snapshot.repo.url>https://oss.sonatype.org/content/repositories/snapshots/</snapshot.repo.url>
        <maven.compiler.source>7</maven.compiler.source>
        <maven.compiler.target>7</maven.compiler.target>
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
        <maven-bundle-plugin.version>5.1.8</maven-bundle-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version><!-- for Github CI -->
        <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
        <jdk11-illegal-access-level>deny</jdk11-illegal-access-level>
    </properties>
    <name>SnakeYAML</name>
    <description>YAML 1.1 parser and emitter for Java</description>
    <inceptionYear>2008</inceptionYear>
    <url>https://bitbucket.org/snakeyaml/snakeyaml</url>
    <issueManagement>
        <system>Bitbucket</system>
        <url>https://bitbucket.org/snakeyaml/snakeyaml/issues</url>
    </issueManagement>
    <mailingLists>
        <mailingList>
            <name>SnakeYAML developers and users List</name>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>
    <scm>
        <connection>scm:git:http://bitbucket.org/snakeyaml/snakeyaml</connection>
        <developerConnection>scm:git:ssh://*****************/snakeyaml/snakeyaml</developerConnection>
        <url>https://bitbucket.org/snakeyaml/snakeyaml/src</url>
        <tag>snakeyaml-2.0</tag>
    </scm>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>asomov</id>
            <name>Andrey Somov</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>maslovalex</id>
            <name>Alexander Maslov</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.11.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <name>Nexus Release Repository</name>
            <url>${release.repo.url}</url>
        </repository>
        <snapshotRepository>
            <id>sonatype-nexus-staging</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>${snapshot.repo.url}</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <testResources>
            <testResource>
                <directory>${basedir}/src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.4.1</version>
                    <configuration>
                        <excludePackageNames>org.yaml.snakeyaml.external.*</excludePackageNames>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <environmentVariables>
                            <EnvironmentKey1>EnvironmentValue1</EnvironmentKey1>
                            <EnvironmentEmpty />
                        </environmentVariables>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.3.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
                <executions>
                    <execution>
                        <id>module-info-compile</id>
                        <goals><goal>compile</goal></goals>
                        <configuration>
                            <release>9</release>
                            <compileSourceRoots>${project.basedir}/src/main/java9</compileSourceRoots>
                            <multiReleaseOutput>true</multiReleaseOutput>
                              <jdkToolchain>
                                <version>[11,)</version>
                              </jdkToolchain>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-Xmx512m</argLine>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/StressTest.java</exclude>
                        <exclude>**/ParallelTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.10</version>
                <configuration>
                    <buildOutputDirectory>bin</buildOutputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>2.12.1</version>
                <executions>
                    <execution>
                        <id>validate-changes</id>
                        <phase>pre-site</phase>
                        <goals>
                            <goal>changes-validate</goal>
                        </goals>
                        <configuration>
                            <failOnError>true</failOnError>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.mycila.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <version>1.10.b1</version>
                <configuration>
                    <header>src/etc/header.txt</header>
                    <quiet>false</quiet>
                    <failIfMissing>true</failIfMissing>
                    <aggregate>false</aggregate>
                    <includes>
                        <include>src/**/*.java</include>
                    </includes>
                    <excludes>
                        <exclude>src/main/java/org/yaml/snakeyaml/external/**</exclude>
                    </excludes>
                    <useDefaultExcludes>true</useDefaultExcludes>
                    <useDefaultMapping>true</useDefaultMapping>
                    <strictCheck>true</strictCheck>
                    <encoding>UTF-8</encoding>
                </configuration>
                <executions>
                    <execution>
                        <phase>site</phase>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${maven-bundle-plugin.version}</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <_nouses>true</_nouses>
                        <Export-Package>
                            !org.yaml.snakeyaml.external*,
                            org.yaml.snakeyaml.*;version=${project.version}
                        </Export-Package>
                        <Multi-Release>true</Multi-Release>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>${maven-site-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-descriptor</id>
                        <goals>
                            <goal>attach-descriptor</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <useReleaseProfile>false</useReleaseProfile>
                    <releaseProfiles>release</releaseProfiles>
                    <goals>deploy nexus-staging:release</goals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <!-- the version is compatible with Nexus, do not change -->
                <version>1.6.8</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>sonatype-nexus-staging</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>false</autoReleaseAfterClose>
                    <keepStagingRepositoryOnFailure>true</keepStagingRepositoryOnFailure>
                    <keepStagingRepositoryOnCloseRuleFailure>true</keepStagingRepositoryOnCloseRuleFailure>
                </configuration>
            </plugin>
            <plugin>
                <groupId>net.revelc.code.formatter</groupId>
                <artifactId>formatter-maven-plugin</artifactId>
                <version>2.20.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>format</goal>
                        </goals>
                        <configuration>
                            <configFile>src/etc/eclipse-java-google-style.xml</configFile>
                            <encoding>UTF-8</encoding>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>2.12.1</version>
                <configuration>
                    <issueLinkTemplate>https://bitbucket.org/snakeyaml/snakeyaml/issues/%ISSUE%</issueLinkTemplate>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>changes-report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <showSuccess>true</showSuccess>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>html</id>
                        <configuration>
                            <doctitle>API for ${project.name} ${project.version}</doctitle>
                            <windowtitle>API for ${project.name} ${project.version}</windowtitle>
                            <testDoctitle>Test API for ${project.name} ${project.version}</testDoctitle>
                            <testWindowtitle>Test API for ${project.name} ${project.version}</testWindowtitle>
                        </configuration>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>
    <profiles>
        <profile>
            <id>with-java11-tests</id>
            <properties>
                <maven.compiler.testSource>11</maven.compiler.testSource>
                <maven.compiler.testTarget>11</maven.compiler.testTarget>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <argLine>--illegal-access=${jdk11-illegal-access-level} -Xmx512m</argLine>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <compilerArgs>
                                <arg>-Xlint:deprecation</arg>
                            </compilerArgs>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>build-helper-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>add-java11-test-source</id>
                                <phase>generate-test-sources</phase>
                                <goals>
                                    <goal>add-test-source</goal>
                                </goals>
                                <configuration>
                                    <sources>
                                        <source>${basedir}/src/test/java8/</source>
                                        <source>${basedir}/src/test/java11/</source>
                                    </sources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
