<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2014 jsonwebtoken.io
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt</artifactId>
    <version>0.9.1</version>
    <name>JSON Web Token support for the JVM</name>
    <packaging>jar</packaging>
    <url>https://github.com/jwtk/jjwt</url>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:https://github.com/jwtk/jjwt.git</connection>
        <developerConnection>scm:git:**************:jwtk/jjwt.git</developerConnection>
        <url>**************:jwtk/jjwt.git</url>
        <tag>0.9.1</tag>
    </scm>
    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/jwtk/jjwt/issues</url>
    </issueManagement>
    <ciManagement>
        <system>TravisCI</system>
        <url>https://travis-ci.org/jwtk/jjwt</url>
    </ciManagement>

    <!-- temporary fix until official release of coverall-maven-plugin with clover support -->
    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>bintray-jwtk-coveralls-maven-plugin</id>
            <name>bintray</name>
            <url>https://dl.bintray.com/jwtk/coveralls-maven-plugin</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>bintray-jwtk-coveralls-maven-plugin</id>
            <name>bintray-plugins</name>
            <url>https://dl.bintray.com/jwtk/coveralls-maven-plugin</url>
        </pluginRepository>
    </pluginRepositories>
    <!-- temporary fix until official release of coverall-maven-plugin with clover support -->

    <properties>

        <maven.jar.version>3.0.2</maven.jar.version>
        <maven.compiler.version>3.6.1</maven.compiler.version>

        <jdk.version>1.7</jdk.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <buildNumber>${user.name}-${maven.build.timestamp}</buildNumber>

        <jackson.version>2.9.6</jackson.version>

        <!-- Optional Runtime Dependencies: -->
        <bouncycastle.version>1.56</bouncycastle.version>

        <!-- Test Dependencies: Only required for testing when building.  Not required by users at runtime: -->
        <groovy.version>2.4.11</groovy.version>
        <logback.version>1.2.3</logback.version>
        <easymock.version>3.5</easymock.version>
        <junit.version>4.12</junit.version>
        <powermock.version>2.0.0-beta.5</powermock.version> <!-- necessary for Java 9 support -->
        <failsafe.plugin.version>2.20.1</failsafe.plugin.version>
        <surefire.plugin.version>2.20.1</surefire.plugin.version>
        <clover.version>4.2.0</clover.version>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- Optional Dependencies: -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>

        <!-- Provided scope: only used in Android environments - not downloaded as a transitive dependency.
             This dependency is only a stub of the actual implementation, which means we can't use it at test time.
             An Android environment is required to test for real. -->
        <dependency>
            <groupId>com.google.android</groupId>
            <artifactId>android</artifactId>
            <version>4.1.1.4</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Test Dependencies: Only required for testing when building.  Not required by users at runtime: -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <version>${easymock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce-banned-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <bannedDependencies>
                                    <searchTransitive>true</searchTransitive>
                                    <excludes>
                                        <exclude>commons-logging</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven.jar.version}</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestFile>
                            ${project.build.outputDirectory}/META-INF/MANIFEST.MF
                        </manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <!-- Allow for writing tests in Groovy: -->
            <plugin>
                <groupId>org.codehaus.gmaven</groupId>
                <artifactId>gmaven-plugin</artifactId>
                <version>1.5</version>
                <configuration>
                    <providerSelection>2.0</providerSelection>
                    <source />
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>generateTestStubs</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.gmaven.runtime</groupId>
                        <artifactId>gmaven-runtime-2.0</artifactId>
                        <version>1.5</version>
                        <exclusions>
                            <exclusion>
                                <groupId>org.codehaus.groovy</groupId>
                                <artifactId>groovy-all</artifactId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                        <version>${groovy.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!-- Override OSS parent to support Java 9. Doesn't hurt Java 8 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>${failsafe.plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/*IT.java</include>
                        <include>**/*IT.groovy</include>
                        <include>**/*ITCase.java</include>
                        <include>**/*ITCase.groovy</include>
                    </includes>
                    <excludes>
                        <exclude>**/*ManualIT.java</exclude>
                        <exclude>**/*ManualIT.groovy</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openclover</groupId>
                <artifactId>clover-maven-plugin</artifactId>
                <version>${clover.version}</version>
                <configuration>
                    <excludes>
                        <exclude>**/*Test*</exclude>
                        <!-- leaving out lang as it mostly comes from other sources -->
                        <exclude>io/jsonwebtoken/lang/*</exclude>
                    </excludes>
                    <methodPercentage>100%</methodPercentage>
                    <statementPercentage>100%</statementPercentage>
                    <conditionalPercentage>100%</conditionalPercentage>
                    <targetPercentage>100%</targetPercentage>
                </configuration>
                <executions>
                    <execution>
                        <id>clover</id>
                        <phase>test</phase>
                        <goals>
                            <goal>instrument</goal>
                            <goal>check</goal>
                            <goal>clover</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-gitexe</artifactId>
                        <version>1.9.5</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <useReleaseProfile>false</useReleaseProfile>
                    <arguments>-Psonatype-oss-release -Pdocs -Psign</arguments>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>3.3.0</version>
                <extensions>true</extensions>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <instructions>
                        <Import-Package><![CDATA[
                            android.util;resolution:=optional,
                            org.bouncycastle.jce;resolution:=optional,
                            org.bouncycastle.jce.spec;resolution:=optional,
                            *
                        ]]>
                        </Import-Package>
                    </instructions>
                </configuration>
            </plugin>

            <!-- Temporarily host coveralls SNAPSHOT with clover support locally  -->
            <plugin>
                <groupId>org.jwtk.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>4.4.0</version>
            </plugin>
            <!-- Temporarily host coveralls SNAPSHOT with clover support locally  -->

            <!-- Override OSS parent to support Java 9 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <!-- Workaround for Java 9 -->
                    <dependency>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                        <version>2.6</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <!-- Turn off JDK 8's lint checks: -->
                <additionalparam>-Xdoclint:none</additionalparam>
            </properties>
        </profile>
        <profile>
            <id>sign</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docs</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>2.10.4</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <dependencies>
                            <!-- Workaround for Java 9 -->
                            <dependency>
                                <groupId>commons-lang</groupId>
                                <artifactId>commons-lang</artifactId>
                                <version>2.6</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
