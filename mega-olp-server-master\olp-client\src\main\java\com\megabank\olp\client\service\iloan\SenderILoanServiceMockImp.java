package com.megabank.olp.client.service.iloan;

import java.math.BigDecimal;
import java.util.Date;

import com.megabank.olp.client.sender.iloan.getted.caseDetailStatus.bean.ILoanCaseDetailStatusResultBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusResultBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.mydata.bean.MyDataSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.bean.PersonalLoanApplySubmittedIloanArgBean;

@Service
@Profile( { "dev" } )
public class SenderILoanServiceMockImp implements SenderILoanService
{

	private final Logger logger = LogManager.getLogger( getClass() );

	@Override
	public EloanCaseStatusResultBean getCaseStatusList( String idNo, Date birthDate )
	{
		return new EloanCaseStatusResultBean();
	}

	@Override
	public EloanCustInfoResultBean getILoanCustInfo( String idNo, Date birthDate )
	{
		EloanCustInfoResultBean resultBean = new EloanCustInfoResultBean();
		resultBean.setDupNo( "1" );
		resultBean.setName( "測試者名稱" );
		resultBean.setEducationLevel( "05" );
		resultBean.setMarriageStatus( "1" );
		resultBean.setChildrenCount( 0 );
		resultBean.setNationality( "TW" );
		resultBean.setHomePhoneCode( "" );
		resultBean.setHomePhone( "" );
		resultBean.setResidenceStatus( "1" );
		resultBean.setEmail( "<EMAIL>" );
		resultBean.setJobType( "01" );
		resultBean.setJobSubType( "A" );
		resultBean.setCompanyName( "" );
		resultBean.setCompanyTaxNo( "" );
		resultBean.setCompanyPhoneCode( "" );
		resultBean.setCompanyPhone( "" );
		resultBean.setCompanyPhoneExt( "" );
		resultBean.setAllowApplyILoan( true );

		if( idNo.startsWith( "F1543" ) || idNo.startsWith( "G1368" ) || idNo.equals( "A164737561" ) || idNo.equals( "L112377329" )
			|| idNo.equals( "E180166459" ) || idNo.equals( "X123400025" ) || idNo.equals( "Y101010108" ) )
			resultBean.setGrpCntrNo( "999999999999" );
		else if( idNo.startsWith( "A1" ) || idNo.startsWith( "B2" ) )
			resultBean.setGrpCntrNo( "918111000325" ); // select * from mis.pteamapp where grpcntrno='918111000325' ,【中鋼第46次消費性貸款】
		else
			resultBean.setGrpCntrNo( "" );

		if( StringUtils.isNotBlank( resultBean.getGrpCntrNo() ) )
		{
			resultBean.setEmail( "test" + idNo + "@csc.com.tw" );
			resultBean.setHomePhoneCode( "02" );
			resultBean.setHomePhone( "27064100" );
			// ~~~~~~~~~~~~~~~
			resultBean.setCompanyName( "中國鋼鐵股份有限公司" );
			resultBean.setCompanyTaxNo( "30414175" );
			resultBean.setCompanyPhoneCode( "07" );
			resultBean.setCompanyPhone( "8021111" );
			resultBean.setCompanyPhoneExt( "9876" );
			resultBean.setSnrY( 11 );
			resultBean.setSnrM( null );
			resultBean.setPayAmt( new BigDecimal( ( idNo.startsWith( "A" ) ? 98 : 76 ) * 10000 ) );
			// ~~~~~~~~~~~~~~~
			resultBean.setEmpNo( StringUtils.substring( idNo, 0, 6 ) );
			resultBean.setGrpOwnBrId( "002" );
			resultBean.setGrpApplyAmt( new BigDecimal( ( StringUtils.equals( StringUtils.substring( idNo, 1, 2 ), "1" ) ? 61 : 62 ) * 10000 ) );
			resultBean.setEngName( "Michael Lin" );
			resultBean.setJobPosition( "設備工程師" );

			if( !idNo.startsWith( "Y" ) )
			{
				String fAddrPostalCode = "235";
				String fAddr = "新北市中和區連城路一段2號3樓之4";

				if( idNo.startsWith( "A" ) || idNo.startsWith( "B" ) || idNo.startsWith( "F" ) )
				{
					fAddrPostalCode = "10658";
					fAddr = "台北市大安區信義路三段182號6樓";
				}
				else if( idNo.startsWith( "E" ) )
				{
					fAddrPostalCode = "807070";
					fAddr = "高雄市三民區民族一路519號";
				}
				resultBean.setfAddrPostalCode( fAddrPostalCode );
				resultBean.setfAddr( fAddr );
			}
			// ~~~~~~~~~~~~~~~
			if( !idNo.startsWith( "Y" ) )
			{
				String coAddrPostalCode = "806";
				String coAddr = "高雄市前鎮區成功二路88號";

				if( idNo.startsWith( "A" ) || idNo.startsWith( "B" ) || idNo.startsWith( "F" ) )
				{
					coAddrPostalCode = "812";
					coAddr = "高雄市小港區中鋼路1號";
				}
				resultBean.setCoAddrPostalCode( coAddrPostalCode );
				resultBean.setCoAddr( coAddr );
			}
			resultBean.setAppnWay( "B" );
			resultBean.setAppnBankCode( "700" );
			resultBean.setDpAcct( "**************" );
		}

		return resultBean;
	}

	@Override
	public EloanSubmittedResultBean submitAttachment( AttachmentSubmittedArgBean argBean )
	{
		EloanSubmittedResultBean resultBean = new EloanSubmittedResultBean();
		resultBean.setStat( true );

		return resultBean;
	}

	@Override
	public EloanSubmittedResultBean submitPersonalLoanApply( PersonalLoanApplySubmittedIloanArgBean argBean )
	{
		EloanSubmittedResultBean resultBean = new EloanSubmittedResultBean();
		resultBean.setStat( true );

		return resultBean;
	}

	@Override
	public EloanSubmittedResultBean submitMyData( MyDataSubmittedArgBean argBean )
	{
		EloanSubmittedResultBean resultBean = new EloanSubmittedResultBean();
		resultBean.setStat( true );

		return resultBean;
	}

	@Override
	public EloanSubmittedResultBean submitSigningContract( SigningContractCompletedArgBean argBean )
	{
		EloanSubmittedResultBean resultBean = new EloanSubmittedResultBean();
		resultBean.setStat( true );

		return resultBean;
	}

	@Override
	public ILoanCaseDetailStatusResultBean getCaseDetailStatus( String caseNo )
	{
		ILoanCaseDetailStatusResultBean resultBean = new ILoanCaseDetailStatusResultBean();
		resultBean.setCaseStatus( "報價中" );

		return resultBean;
	}
}
