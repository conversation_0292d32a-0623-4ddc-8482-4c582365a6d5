package com.megabank.olp.client.sender.micro.apply.mydata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseScheduleApplyClient;
import com.megabank.olp.client.utility.bean.EmptyArgBean;

@Component
public class MyDataStatusCheckedClient extends BaseScheduleApplyClient<EmptyArgBean, String>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/mydata/checkMyDataStatus";
	}
}
