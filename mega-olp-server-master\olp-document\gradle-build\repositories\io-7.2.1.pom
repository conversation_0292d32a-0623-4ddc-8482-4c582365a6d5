<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.itextpdf</groupId>
    <artifactId>root</artifactId>
    <version>7.2.1</version>
  </parent>

  <artifactId>io</artifactId>

  <name>iText 7 - io</name>
  <url>https://itextpdf.com/</url>

  <dependencies>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>commons</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>pdftest</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.lng</include>
          <include>**/*.afm</include>
          <include>**/*.html</include>
          <include>**/*.txt</include>
          <include>**/*.properties</include>
        </includes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
        <includes>
          <include>**/*.*</include>
        </includes>
      </testResource>
    </testResources>
  </build>
</project>
