<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.spring.gradle</groupId>
  <artifactId>dependency-management-plugin</artifactId>
  <version>1.0.8.RELEASE</version>
  <name>Dependency management plugin</name>
  <description>A Gradle plugin that provides Maven-like dependency management functionality</description>
  <url>https://github.com/spring-gradle-plugins/dependency-management-plugin</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>wilkinsona</id>
      <name>Andy Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/spring-gradle-plugins/dependency-management-plugin</connection>
    <url>https://github.com/spring-gradle-plugins/dependency-management-plugin</url>
  </scm>
</project>
