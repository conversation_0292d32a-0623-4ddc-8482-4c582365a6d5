package com.megabank.olp.client.sender.mydata.userdata.bean;

import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.client.sender.mydata.BaseMyDataResBean;

public class MyDataFileResBean extends BaseMyDataResBean
{
	private int waitSec;

	@JsonProperty( "data" )
	private List<MyDataFileBean> myDataFileBeans = Collections.emptyList();

	public MyDataFileResBean()
	{}

	public List<MyDataFileBean> getMyDataFileBeans()
	{
		return myDataFileBeans;
	}

	public int getWaitSec()
	{
		return waitSec;
	}

	public void setMyDataFileBeans( List<MyDataFileBean> myDataFileBeans )
	{
		this.myDataFileBeans = myDataFileBeans;
	}

	public void setWaitSec( int waitSec )
	{
		this.waitSec = waitSec;
	}

}
