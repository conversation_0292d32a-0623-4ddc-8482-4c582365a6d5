<?xml version="1.0" encoding="ISO-8859-1"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>7</version>
  </parent>
  <groupId>org.apache.commons</groupId>
  <artifactId>commons-parent</artifactId>
  <packaging>pom</packaging>
  <!-- TODO: dummy version. In Maven 2.1, this will be auto-versioned being a generic parent -->
  <version>17</version>
  <name>Commons Parent</name>
  <url>http://commons.apache.org/</url>

  <ciManagement>
    <system>continuum</system>
    <url>http://vmbuild.apache.org/continuum/</url>
  </ciManagement>

  <!--
    This section *must* be overwritten by subprojects. It is only to allow
    a release of the commons-parent POM.
  -->
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/commons-parent/tags/commons-parent-17</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/commons-parent/tags/commons-parent-17</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/commons-parent/tags/commons-parent-17</url>
  </scm>

  <mailingLists>
    <!-- N.B. commons-site now uses the Apache POM so has its own copy of the mailing list definitions -->
    <mailingList>
      <name>Commons User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-user/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.users/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---User-f319.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.jakarta.commons.user</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-dev/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.dev/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---Dev-f317.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.jakarta.commons.devel</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-issues/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.issues/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---Issues-f25499.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-commits/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.commits/</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/www-announce/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.announce/</otherArchive>
        <otherArchive>http://old.nabble.com/Apache-News-and-Announce-f109.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.apache.announce</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>
  <build>
    <resources>
      <resource>
        <directory>${basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>
        <!-- org.apache.maven.plugins, alpha order by artifact id -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <!-- 
          Warning: 1.4 has regression bug, see: http://jira.codehaus.org/browse/MANTRUN-143
           -->
          <version>1.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2-beta-5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <source>${maven.compile.source}</source>
            <target>${maven.compile.target}</target>
            <encoding>${commons.encoding}</encoding>
            <fork>${commons.compiler.fork}</fork>
            <compilerVersion>${commons.compiler.compilerVersion}</compilerVersion>
            <executable>${commons.compiler.javac}</executable>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <!-- keep only errors and warnings -->
            <quiet>true</quiet>
            <encoding>${commons.encoding}</encoding>
            <docEncoding>${commons.docEncoding}</docEncoding>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <!-- override version 1.1 from apache parent to ensure JDK 1.4 compatibilty -->
          <version>1.0</version>
          <configuration>
            <!--
                apache parent pom automatically adds "LICENSE" and "NOTICE" files
                to jars - duplciating the "LICENSE.txt" and "NOTICE.txt"
                files that components already have.
             -->
            <skip>true</skip>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>2.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.1</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${commons.surefire.version}</version>
        </plugin>
        <!-- Other plugins, alpha order by groupId and artifactId -->
        <plugin>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-build-plugin</artifactId>
          <version>1.3</version>
          <configuration>
            <commons.release.name>${commons.release.name}</commons.release.name>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <!-- version 1.4.x is required for JDK 1.4 compatibilty -->
          <version>1.4.3</version>
          <inherited>true</inherited>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <jvm>${commons.surefire.java}</jvm>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${commons.manifestfile}</manifestFile>
            <manifestEntries>
              <Specification-Title>${project.name}</Specification-Title>
              <Specification-Version>${project.version}</Specification-Version>
              <Specification-Vendor>${project.organization.name}</Specification-Vendor>
              <Implementation-Title>${project.name}</Implementation-Title>
              <Implementation-Version>${project.version}</Implementation-Version>
              <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
              <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
              <X-Compile-Source-JDK>${maven.compile.source}</X-Compile-Source-JDK>
              <X-Compile-Target-JDK>${maven.compile.target}</X-Compile-Target-JDK>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <excludeDependencies>true</excludeDependencies>
          <manifestLocation>target/osgi</manifestLocation>
          <instructions>
            <!-- stops the "uses" clauses being added to "Export-Package" manifest entry -->
            <_nouses>true</_nouses>
            <!-- Stop the JAVA_1_n_HOME variables from being treated as headers by Bnd -->
            <_removeheaders>JAVA_1_3_HOME,JAVA_1_4_HOME,JAVA_1_5_HOME,JAVA_1_6_HOME</_removeheaders>
            <Bundle-SymbolicName>${commons.osgi.symbolicName}</Bundle-SymbolicName>
            <Export-Package>${commons.osgi.export}</Export-Package>
            <Private-Package>${commons.osgi.private}</Private-Package>
            <Import-Package>${commons.osgi.import}</Import-Package>
            <DynamicImport-Package>${commons.osgi.dynamicImport}</DynamicImport-Package>
            <Bundle-DocURL>${project.url}</Bundle-DocURL>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-idea-plugin</artifactId>
        <!--
         | N.B. The version is deliberately not provided - see COMMONSSITE-56
         |      This may result in some warnings, e.g. from the Maven 2 Eclipse plugin.
         -->
        <configuration>
          <jdkLevel>${maven.compile.source}</jdkLevel>
        </configuration>
      </plugin>
      <plugin>
        <!--
          - Copy LICENSE.txt and NOTICE.txt so that they are included
          - in the -javadoc jar file for the component.
          -->
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>javadoc.resources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <tasks>
                <copy todir="${project.build.directory}/apidocs/META-INF">
                  <fileset dir="${basedir}">
                    <include name="LICENSE.txt" />
                    <include name="NOTICE.txt" />
                  </fileset>
                </copy>
              </tasks>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-build-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <!-- N.B. plugins defined here in the <reporting> section ignore what's defined in <pluginManagement>
         in the <build> section above, so we have to define the versions here. -->
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.1.2</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.5</version>
        <configuration> 
          <!-- keep only errors and warnings -->
          <quiet>true</quiet>
          <source>${maven.compile.source}</source>
          <encoding>${commons.encoding}</encoding>
          <docEncoding>${commons.docEncoding}</docEncoding>
          <linksource>true</linksource>
          <links>
            <link>http://java.sun.com/javase/6/docs/api/</link>
          </links>
        </configuration> 
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.1</version>
        <configuration> 
          <aggregate>false</aggregate>
        </configuration> 
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>2.0.1</version>
        <configuration>
          <!-- Exclude the navigation file for Maven 1 sites
               and the changes file used by the changes-plugin,
               as they interfere with the site generation. -->
          <moduleExcludes>
            <xdoc>navigation.xml,changes.xml</xdoc>
          </moduleExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${commons.surefire.version}</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
        <version>2.0-beta-2</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>rat-maven-plugin</artifactId>
        <version>1.0-alpha-3</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>

    <profile>
      <id>ci</id>
      <distributionManagement>
        <repository>
          <id>apache.snapshots</id>
          <name>Apache Development Snapshot Repository</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/repo/m2-snapshot-repository</url>
        </repository>
        <snapshotRepository>
          <id>apache.snapshots</id>
          <name>Apache Development Snapshot Repository</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/repo/m2-snapshot-repository</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>

    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>create-source-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- Pass these arguments to the deploy plugin. -->
              <arguments>-Prelease</arguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>create-javadoc-jar</id>
                <goals>
                  <goal>javadoc</goal>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
            <configuration>
              <source>${maven.compile.source}</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>attached</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>rc</id>
      <distributionManagement>
        <repository>
          <id>apache.snapshots</id>
          <name>Apache Development Snapshot Repository</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/builds/commons/${commons.componentid}/${commons.release.version}/${commons.rc.version}/staged</url>
        </repository>
      </distributionManagement>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>create-source-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- Pass these arguments to the deploy plugin. -->
              <arguments>-Prc</arguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>create-javadoc-jar</id>
                <goals>
                  <goal>javadoc</goal>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
            <configuration>
              <source>${maven.compile.source}</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>attached</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
       Profile for running the build using JDK 1.3
       (JAVA_1_3_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.3</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.3</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_3_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_3_HOME}/bin/java</commons.surefire.java>
        <commons.surefire.version>2.2</commons.surefire.version>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.4
       (JAVA_1_4_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.4</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.4</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_4_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_4_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.5
       (JAVA_1_5_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.5</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.5</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_5_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_5_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.6
       (JAVA_1_6_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.6</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.6</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_6_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_6_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!-- N.B. when adding new java profiles, be sure to update 
         the _removeheaders list in the maven_bundle_plugin configuration -->

    <!-- 
     | Profile to allow testing of deploy phase
     | e.g.
     | mvn deploy -Ptest-deploy -Prelease -Dgpg.skip
     -->
    <profile>
      <id>test-deploy</id>
      <properties>
        <altDeploymentRepository>id::default::file:target/deploy</altDeploymentRepository>
      </properties>
    </profile>
   
    <!--
      Profile to build all Commons "proper" components.

      The trunks of all "proper" components can be checked out using:
          https://svn.apache.org/repos/asf/commons/trunks-proper/

      This profile is a convenience which can be used, for example, to build all the component sites:
          mvn -Ptrunks-proper site

      or, to clean up:
          mvn -Ptrunks-proper clean

      see http://issues.apache.org/jira/browse/COMMONSSITE-30
      -->
    <profile>
      <id>trunks-proper</id>
      <modules>
        <module>../beanutils</module>
        <module>../betwixt</module>
        <module>../chain</module>
        <module>../cli</module>
        <module>../codec</module>
        <module>../collections</module>
        <module>../compress</module>
        <module>../configuration</module>
        <module>../daemon</module>
        <module>../dbcp</module>
        <module>../dbutils</module>
        <module>../digester</module>
        <module>../discovery</module>
        <module>../el</module>
        <module>../email</module>
        <module>../exec</module>
        <module>../fileupload</module>
        <module>../io</module>
        <module>../jci</module>
        <module>../jexl</module>
        <module>../jxpath</module>
        <module>../lang</module>
        <module>../launcher</module>
        <module>../logging</module>
        <module>../math</module>
        <module>../modeler</module>
        <module>../net</module>
        <module>../pool</module>
        <module>../primitives</module>
        <module>../proxy</module>
        <module>../sanselan</module>
        <module>../scxml</module>
        <module>../validator</module>
        <module>../vfs</module>
      </modules>
    </profile>
  </profiles>

  <properties>

    <!-- Default configuration for compiler source and target JVM -->
    <maven.compile.source>1.3</maven.compile.source>
    <maven.compile.target>1.3</maven.compile.target>

    <!-- compiler and surefire plugin settings for "java" profiles -->
    <commons.compiler.fork>false</commons.compiler.fork>
    <commons.compiler.compilerVersion />
    <commons.compiler.javac />
    <commons.surefire.java />
    <commons.surefire.version>2.5</commons.surefire.version>

    <!-- Default values for the download-page generation by commons-build-plugin -->
    <commons.release.name>${project.artifactId}-${commons.release.version}</commons.release.name>
    <commons.release.desc />
    <commons.binary.suffix>-bin</commons.binary.suffix>
    <commons.release.2.name>${project.artifactId}-${commons.release.2.version}</commons.release.2.name>
    <commons.release.2.desc />
    <commons.release.2.binary.suffix>-bin</commons.release.2.binary.suffix>

    <!-- Commons Component Id -->
    <commons.componentid>${project.artifactId}</commons.componentid>

    <!-- The RC version used in the staging repository URL. -->
    <commons.rc.version>RC1</commons.rc.version>

    <!-- Configuration properties for the OSGi maven-bundle-plugin -->
    <commons.osgi.symbolicName>org.apache.commons.${commons.componentid}</commons.osgi.symbolicName>
    <commons.osgi.export>org.apache.commons.*;version=${project.version};-noimport:=true</commons.osgi.export>
    <commons.osgi.import>*</commons.osgi.import>
    <commons.osgi.dynamicImport />
    <commons.osgi.private />

    <!-- location of any manifest file used by maven-jar-plugin -->
    <commons.manifestfile>target/osgi/MANIFEST.MF</commons.manifestfile>

    <!--
      Make the deployment protocol pluggable. This allows to switch to
      other protocols like scpexe, which some users prefer over scp.
    -->
    <commons.deployment.protocol>scp</commons.deployment.protocol>

    <!--
      Encoding of Java source files: Make sure, that the compiler and
      the javadoc generator use the right encoding. Subprojects may
      overwrite this, if they are using another encoding.
    -->
    <commons.encoding>iso-8859-1</commons.encoding>
    <commons.docEncoding>${commons.encoding}</commons.docEncoding>
    <!-- Define encoding for filtering -->
    <project.build.sourceEncoding>${commons.encoding}</project.build.sourceEncoding>
    <project.reporting.outputEncoding>${commons.encoding}</project.reporting.outputEncoding>

  </properties>

</project>
