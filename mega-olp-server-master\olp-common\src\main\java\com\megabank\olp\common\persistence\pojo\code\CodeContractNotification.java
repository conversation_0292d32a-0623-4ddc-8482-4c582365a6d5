package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeContractNotification is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_contract_notification" )
public class CodeContractNotification extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_contract_notification";

	public static final String CONTRACT_NOTIFICATION_CODE_CONSTANT = "contractNotificationCode";

	public static final String NAME_CONSTANT = "name";

	private String contractNotificationCode;

	private String name;

	public CodeContractNotification()
	{}

	public CodeContractNotification( String contractNotificationCode )
	{
		this.contractNotificationCode = contractNotificationCode;
	}

	@Id
	@Column( name = "contract_notification_code", unique = true, nullable = false, length = 20 )
	public String getContractNotificationCode()
	{
		return contractNotificationCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setContractNotificationCode( String contractNotificationCode )
	{
		this.contractNotificationCode = contractNotificationCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}