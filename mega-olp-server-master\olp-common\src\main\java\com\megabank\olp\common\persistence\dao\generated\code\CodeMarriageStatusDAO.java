package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeMarriageStatus;

import org.springframework.stereotype.Repository;

/**
 * The CodeMarriageStatusDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeMarriageStatusDAO extends BasePojoDAO<CodeMarriageStatus, String>
{
	@Override
	protected Class<CodeMarriageStatus> getPojoClass()
	{
		return CodeMarriageStatus.class;
	}
}
