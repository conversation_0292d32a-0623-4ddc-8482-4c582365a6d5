package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeTown is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_town" )
public class CodeTown extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_town";

	public static final String TOWN_CODE_CONSTANT = "townCode";

	public static final String CODE_CITY_CONSTANT = "codeCity";

	public static final String NAME_CONSTANT = "name";

	public static final String POSTAL_CODE_CONSTANT = "postalCode";

	public static final String CODE_BRANCH_BANKS_CONSTANT = "codeBranchBanks";

	private String townCode;

	private transient CodeCity codeCity;

	private String name;

	private String postalCode;

	private transient Set<CodeBranchBank> codeBranchBanks = new HashSet<>( 0 );

	public CodeTown()
	{}

	public CodeTown( String townCode )
	{
		this.townCode = townCode;
	}

	public CodeTown( String townCode, CodeCity codeCity, String name, String postalCode )
	{
		this.townCode = townCode;
		this.codeCity = codeCity;
		this.name = name;
		this.postalCode = postalCode;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeTown" )
	public Set<CodeBranchBank> getCodeBranchBanks()
	{
		return codeBranchBanks;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "city_code", nullable = false )
	public CodeCity getCodeCity()
	{
		return codeCity;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Column( name = "postal_code", nullable = false, length = 5 )
	public String getPostalCode()
	{
		return postalCode;
	}

	@Id
	@Column( name = "town_code", unique = true, nullable = false, length = 20 )
	public String getTownCode()
	{
		return townCode;
	}

	public void setCodeBranchBanks( Set<CodeBranchBank> codeBranchBanks )
	{
		this.codeBranchBanks = codeBranchBanks;
	}

	public void setCodeCity( CodeCity codeCity )
	{
		this.codeCity = codeCity;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setPostalCode( String postalCode )
	{
		this.postalCode = postalCode;
	}

	public void setTownCode( String townCode )
	{
		this.townCode = townCode;
	}
}