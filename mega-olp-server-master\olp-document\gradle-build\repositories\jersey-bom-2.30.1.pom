<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
        <relativePath/>
    </parent>

    <groupId>org.glassfish.jersey</groupId>
    <artifactId>jersey-bom</artifactId>
    <version>2.30.1</version>
    <packaging>pom</packaging>
    <name>jersey-bom</name>

    <description>Jersey Bill of Materials (BOM)</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-server</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.bundles</groupId>
                <artifactId>jaxrs-ri</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-apache-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-grizzly-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-jetty-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-jdk-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-netty-connector</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-jetty-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-grizzly2-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-grizzly2-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-jetty-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-jdk-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-netty-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-simple-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers.glassfish</groupId>
                <artifactId>jersey-gf-ejb</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-bean-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-entity-filtering</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-metainf-services</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.microprofile</groupId>
                <artifactId>jersey-mp-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-mvc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-mvc-bean-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-mvc-freemarker</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-mvc-jsp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-mvc-mustache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-proxy-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-servlet-portability</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-spring4</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-spring5</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-declarative-linking</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-wadl-doclet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-weld2-se</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-cdi1x</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-cdi1x-transaction</artifactId>
                <version>${project.version}</version>
            </dependency>
             <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-cdi1x-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-cdi1x-servlet</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.cdi</groupId>
                <artifactId>jersey-cdi1x-ban-custom-hk2-binding</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.rx</groupId>
                <artifactId>jersey-rx-client-guava</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.rx</groupId>
                <artifactId>jersey-rx-client-rxjava</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.rx</groupId>
                <artifactId>jersey-rx-client-rxjava2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext.microprofile</groupId>
                <artifactId>jersey-mp-rest-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-jaxb</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson1</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jettison</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-processing</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-binding</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-kryo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-moxy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-multipart</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-sse</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.security</groupId>
                <artifactId>oauth1-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.security</groupId>
                <artifactId>oauth1-server</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.security</groupId>
                <artifactId>oauth1-signature</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.security</groupId>
                <artifactId>oauth2-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-hk2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-cdi2-se</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework</groupId>
                <artifactId>jersey-test-framework-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-bundle</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-external</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-grizzly2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-inmemory</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-jdk-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-simple</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework.providers</groupId>
                <artifactId>jersey-test-framework-provider-jetty</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.test-framework</groupId>
                <artifactId>jersey-test-framework-util</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.7.1</version>
                <configuration>
                    <generateProjectInfo>false</generateProjectInfo>
                    <generateReports>true</generateReports>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>project-info</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>

            <!-- placeholder required for site:stage -->
            <distributionManagement>
                <site>
                    <id>localhost</id>
                    <url>http://localhost</url>
                </site>
            </distributionManagement>
        </profile>
    </profiles>
</project>
