package com.megabank.olp.common.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.common.controller.bean.url.UrlDisplayedArgBean;
import com.megabank.olp.common.service.UrlService;

@RestController
@RequestMapping( "open/url" )
public class UrlController extends BaseController
{
	@Autowired
	private UrlService urlService;

	/**
	 * 查詢連結是否顯示
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "isUrlDisplayed" )
	public Map<String, Object> isUrlDisplayed( @RequestBody @Validated UrlDisplayedArgBean argBean )
	{
		return getResponseMap( urlService.isUrlDisplayed( argBean.getKey() ) );
	}

}
