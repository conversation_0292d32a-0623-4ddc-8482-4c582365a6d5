package com.megabank.olp.client.sender.micro.user;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.user.bean.IdentityIdsArgBean;

@Component
public class IdentityIdsClient extends BaseUserClient<IdentityIdsArgBean, List<Long>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return Long.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/user/getIdentityIds";
	}
}
