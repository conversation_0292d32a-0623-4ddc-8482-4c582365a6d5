package com.megabank.olp.client.sender.micro.modal;

import java.io.UnsupportedEncodingException;
import java.net.URI;

import com.megabank.olp.client.sender.micro.BaseMicroServicesClient;

public abstract class BaseModalClient<TArg, TResult> extends BaseMicroServicesClient<TArg, TResult>
{
	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlMicroModal() + getSuffixUrl() );
	}

	@Override
	protected String getSystemName()
	{
		return "modal";
	}
}
