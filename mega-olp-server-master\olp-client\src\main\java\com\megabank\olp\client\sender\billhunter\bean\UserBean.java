package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "name", "id", "batchNo", "notifyInfo", "toEmpId", "toMemo", "toRefNo" } )
public class UserBean extends BaseBean
{

	/**
	 * 收件人戶名MailToName
	 */
	@XmlElement( name = "Name" )
	private String name;

	/**
	 * 收件人ID MailToId
	 */
	@XmlElement( name = "ID" )
	private String id;

	/**
	 * 批號
	 */
	@XmlElement( name = "BatchNO" )
	private String batchNo = "";

	/**
	 * 電子郵件MailTo
	 */
	@XmlElement( name = "NotifyInfo" )
	private String notifyInfo;

	/**
	 * 員工編號
	 */
	@XmlElement( name = "ToEmpId" )
	private String toEmpId = "";

	/**
	 * 附言
	 */
	@XmlElement( name = "ToMemo" )
	private String toMemo = "";

	/**
	 * 企參號號
	 */
	@XmlElement( name = "ToRefNo" )
	private String toRefNo = "";

	public UserBean()
	{}

	public String getBatchNo()
	{
		return batchNo;
	}

	public String getId()
	{
		return id;
	}

	public String getName()
	{
		return name;
	}

	public String getNotifyInfo()
	{
		return notifyInfo;
	}

	public String getToEmpId()
	{
		return toEmpId;
	}

	public String getToMemo()
	{
		return toMemo;
	}

	public String getToRefNo()
	{
		return toRefNo;
	}

	public void setBatchNo( String batchNo )
	{
		this.batchNo = batchNo;
	}

	public void setId( String id )
	{
		this.id = id;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotifyInfo( String notifyInfo )
	{
		this.notifyInfo = notifyInfo;
	}

	public void setToEmpId( String toEmpId )
	{
		this.toEmpId = toEmpId;
	}

	public void setToMemo( String toMemo )
	{
		this.toMemo = toMemo;
	}

	public void setToRefNo( String toRefNo )
	{
		this.toRefNo = toRefNo;
	}
}
