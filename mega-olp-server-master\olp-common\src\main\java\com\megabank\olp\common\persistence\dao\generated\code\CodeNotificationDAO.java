package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeNotification;

/**
 * The CodeNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNotificationDAO extends BasePojoDAO<CodeNotification, String>
{
	public List<CodeNotification> getAllPojosOrderBydisplayOrder()
	{
		OrderBean orderBean = new OrderBean( CodeNotification.DISPLAY_ORDER_CONSTANT );

		return getAllPojosOrderBy( orderBean );
	}

	@Override
	protected Class<CodeNotification> getPojoClass()
	{
		return CodeNotification.class;
	}
}
