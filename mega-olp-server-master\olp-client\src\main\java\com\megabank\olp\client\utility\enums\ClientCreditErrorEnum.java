package com.megabank.olp.client.utility.enums;

import com.megabank.olp.base.enums.ErrorEnum;

public enum ClientCreditErrorEnum implements ErrorEnum
{

	/**
	 * 取得卡處電文發生錯誤，錯誤代碼:{0}
	 */
	CREDIT_CARD_COMMON( "01001" ),

	/**
	 * 手機無法辨識
	 */
	MOBILE_ERROR( "01002" );

	private String errorCode;

	private ClientCreditErrorEnum( String errorCode )
	{
		this.errorCode = errorCode;
	}

	@Override
	public String getCode()
	{
		return "CLIENT-CREDIT" + errorCode;
	}
}
