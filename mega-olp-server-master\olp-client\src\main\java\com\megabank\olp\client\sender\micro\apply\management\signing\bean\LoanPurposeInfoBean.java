package com.megabank.olp.client.sender.micro.apply.management.signing.bean;

import com.megabank.olp.base.bean.BaseBean;

public class LoanPurposeInfoBean extends BaseBean
{
	private String loanPurpose;

	private Boolean isChecked;

	public LoanPurposeInfoBean()
	{}

	public Boolean getIsChecked()
	{
		return isChecked;
	}

	public String getLoanPurpose()
	{
		return loanPurpose;
	}

	public void setIsChecked( Boolean isChecked )
	{
		this.isChecked = isChecked;
	}

	public void setLoanPurpose( String loanPurpose )
	{
		this.loanPurpose = loanPurpose;
	}
}
