<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>com.nimbusds</groupId>
	<artifactId>oauth2-oidc-sdk</artifactId>
	<version>6.14</version>
	<packaging>jar</packaging>

	<name>OAuth 2.0 SDK with OpenID Connect extensions</name>
	<description>
		OAuth 2.0 SDK with OpenID Connection extensions for developing
		client and server applications.
	</description>

	<url>https://bitbucket.org/connect2id/oauth-2.0-sdk-with-openid-connect-extensions</url>

	<organization>
		<name>Connect2id Ltd.</name>
		<url>http://connect2id.com</url>
	</organization>

	<licenses>
		<license>
			<name>Apache License, version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.html‎</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<connection>scm:git:https://bitbucket.org/connect2id/oauth-2.0-sdk-with-openid-connect-extensions.git</connection>
		<developerConnection>scm:git:*****************:connect2id/oauth-2.0-sdk-with-openid-connect-extensions.git</developerConnection>
		<url>https://bitbucket.org/connect2id/oauth-2.0-sdk-with-openid-connect-extensions</url>
		<tag>6.14</tag>
	</scm>

	<developers>
		<developer>
			<id>vdzhuvinov</id>
			<name>Vladimir Dzhuvinov</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.0.1</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.stephenc.jcip</groupId>
			<artifactId>jcip-annotations</artifactId>
			<version>1.0-1</version>
		</dependency>
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>[1.3.1,2.3]</version>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>lang-tag</artifactId>
			<version>[1.4.3,)</version>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>[6.0.1,)</version>
		</dependency>
		<dependency>
			<groupId>org.cryptomator</groupId>
			<artifactId>siv-mode</artifactId>
			<version>[1.1.1,)</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.opensaml</groupId>
			<artifactId>opensaml-core</artifactId>
			<version>[3.0.0,)</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.opensaml</groupId>
			<artifactId>opensaml-saml-api</artifactId>
			<version>[3.0.0,)</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.opensaml</groupId>
			<artifactId>opensaml-saml-impl</artifactId>
			<version>[3.0.0,)</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>net.jadler</groupId>
			<artifactId>jadler-all</artifactId>
			<version>1.3.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.60</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>1.60</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<fork>true</fork>
					<compilerArgument>-Xlint</compilerArgument>
				</configuration>
				<dependencies>
					<!-- Using the eclipse compiler allows for different source and target,
                            which is a good thing (outweighing that this is a rarely used combination,
                            and most people use javac) This should also allow us to run maven builds
                            on a JRE and not a JDK. -->
					<dependency>
						<groupId>org.codehaus.plexus</groupId>
						<artifactId>plexus-compiler-eclipse</artifactId>
						<version>2.5</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>default-compile</id>
					</execution>
					<execution>
						<id>jdk6-compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<source>1.7</source>
							<target>1.6</target>
							<compilerId>eclipse</compilerId>
							<outputDirectory>${project.build.outputDirectory}_jdk6</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>jdk8-compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<source>1.7</source>
							<target>1.8</target>
							<outputDirectory>${project.build.outputDirectory}_jdk8</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>jdk10-compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<source>1.7</source>
							<target>10</target>
							<outputDirectory>${project.build.outputDirectory}_jdk10</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<archive>
						<manifestEntries>
							<Build-Date>${timestamp}</Build-Date>
							<Build-Number>${buildNumber}</Build-Number>
							<Build-Tag>${project.scm.tag}</Build-Tag>
						</manifestEntries>
						<!-- Make sure MANIFEST.MF generated by maven-bundle-plugin is used for final JAR -->
						<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
						</manifest>
					</archive>
				</configuration>
				<executions>
					<execution>
						<id>default-jar</id>
					</execution>
					<execution>
						<id>jdk6-jar</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classesDirectory>${project.build.outputDirectory}_jdk6</classesDirectory>
							<classifier>jdk6</classifier>
						</configuration>
					</execution>
					<execution>
						<id>jdk8-jar</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classesDirectory>${project.build.outputDirectory}_jdk8</classesDirectory>
							<classifier>jdk8</classifier>
						</configuration>
					</execution>
					<execution>
						<id>jdk10-jar</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<archive>
								<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
								<manifestEntries>
									<Automatic-Module-Name>com.nimbusds.oauth2.sdk</Automatic-Module-Name>
								</manifestEntries>
							</archive>
							<classesDirectory>${project.build.outputDirectory}_jdk10</classesDirectory>
							<classifier>jdk10</classifier>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.1</version>
				<configuration>
                    <source>8</source>
                    <author>true</author>
					<version>true</version>
					<use>true</use>
					<linksource>true</linksource>
					<windowtitle>Nimbus OAuth 2.0 SDK with OpenID Connect 1.0 extensions v${project.version}
					</windowtitle>
					<doctitle>Nimbus OAuth 2.0 SDK with OpenID Connect 1.0 extensions v${project.version}</doctitle>
					<overview>${basedir}/src/main/javadoc/overview.html
					</overview>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.5.3</version>
				<configuration>
					<useReleaseProfile>false</useReleaseProfile>
					<goals>deploy</goals>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.6</version>
				<!-- Pass password with
                    mvn release:prepare|perform -Darguments=-Dgpg.passphrase=thephrase
                -->
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.3</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<getRevisionOnlyOnce>true</getRevisionOnlyOnce>
					<doCheck>false</doCheck>
					<doUpdate>false</doUpdate>
					<timestampFormat>{0,date,yyyyMMdd.HHmmss.SSS}
					</timestampFormat>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.0</version>
				<configuration>
					<!-- Remove when fixed: https://issues.apache.org/jira/browse/SUREFIRE-1588 -->
					<useSystemClassLoader>false</useSystemClassLoader>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>3.5.1</version>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
						<Bundle-Version>${project.version}</Bundle-Version>
						<Export-Package>com.nimbusds.oauth2.*;version=${project.version},com.nimbusds.openid.*;version=${project.version}</Export-Package>
						<Import-Package>*</Import-Package>
					</instructions>
				</configuration>
				<executions>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.7</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

</project>
