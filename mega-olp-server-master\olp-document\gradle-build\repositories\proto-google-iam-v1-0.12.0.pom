<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api.grpc</groupId>
  <artifactId>proto-google-iam-v1</artifactId>
  <version>0.12.0</version>
  <name>proto-google-iam-v1</name>
  <description>PROTO library for proto-google-iam-v1</description>
  <url>https://github.com/googleapis/api-client-staging</url>
  <organization>
    <name>Google LLC</name>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>garrettjonesgoogle</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>pongad</id>
      <name>Michael Darakananda</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>michaelbausor</id>
      <name>Micheal Bausor</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>vam-google</id>
      <name>Vadym Matsishevskyi</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>neozwu</id>
      <name>Neo Wu</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>andreamlin</id>
      <name>Andrea Lin</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>hzyi-google</id>
      <name>Hanzhen Yi</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/googleapis/api-client-staging.git</connection>
    <url>https://github.com/googleapis/api-client-staging</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.5.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
      <version>1.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
      <version>1.11.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
