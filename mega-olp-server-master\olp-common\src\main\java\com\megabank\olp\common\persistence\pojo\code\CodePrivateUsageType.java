package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodePrivateUsageType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_private_usage_type" )
public class CodePrivateUsageType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_private_usage_type";

	public static final String PRIVATE_USAGE_TYPE_CONSTANT = "privateUsageType";

	public static final String NAME_CONSTANT = "name";

	private String privateUsageType;

	private String name;

	public CodePrivateUsageType()
	{}

	public CodePrivateUsageType( String privateUsageType )
	{
		this.privateUsageType = privateUsageType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "private_usage_type", unique = true, nullable = false, length = 20 )
	public String getPrivateUsageType()
	{
		return privateUsageType;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setPrivateUsageType( String privateUsageType )
	{
		this.privateUsageType = privateUsageType;
	}
}