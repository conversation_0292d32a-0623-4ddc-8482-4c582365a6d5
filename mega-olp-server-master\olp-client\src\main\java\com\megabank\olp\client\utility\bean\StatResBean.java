package com.megabank.olp.client.utility.bean;

import java.io.Serializable;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public class StatResBean<T extends Serializable> extends BaseBean
{
	private String errorCode;

	private String errorMsg;

	private T result;

	private String stat;

	public StatResBean()
	{
		// default constructor
	}

	/**
	 * @return the errorCode
	 */
	public String getErrorCode()
	{

		return errorCode;
	}

	/**
	 * @return the errorMsg
	 */
	public String getErrorMsg()
	{
		return errorMsg;
	}

	/**
	 * @return the result
	 */
	public T getResult()
	{
		return result;
	}

	/**
	 * @return the stat
	 */
	public String getStat()
	{
		return stat;
	}

	/**
	 * @param errorCode the errorCode to set
	 */
	public void setErrorCode( String errorCode )
	{
		this.errorCode = errorCode;
	}

	/**
	 * @param errorMsg the errorMsg to set
	 */
	public void setErrorMsg( String errorMsg )
	{
		this.errorMsg = errorMsg;
	}

	/**
	 * @param result the result to set
	 */
	public void setResult( T result )
	{
		this.result = result;
	}

	/**
	 * @param stat the stat to set
	 */
	public void setStat( String stat )
	{
		this.stat = stat;
	}

}
