/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import java.util.Collections;
import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */

public class InternalPbAccountResultBean extends BaseBean
{

	private List<String> accountList = Collections.emptyList();

	public InternalPbAccountResultBean()
	{}

	public List<String> getAccountList()
	{
		return accountList;
	}

	public void setAccountList( List<String> accountList )
	{
		this.accountList = accountList;
	}

}
