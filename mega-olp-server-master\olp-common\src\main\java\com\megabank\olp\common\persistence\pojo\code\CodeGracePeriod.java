package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeGracePeriod is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_grace_period" )
public class CodeGracePeriod extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_grace_period";

	public static final String GRACE_PERIOD_CODE_CONSTANT = "gracePeriodCode";

	public static final String NAME_CONSTANT = "name";

	private String gracePeriodCode;

	private String name;

	public CodeGracePeriod()
	{}

	public CodeGracePeriod( String gracePeriodCode )
	{
		this.gracePeriodCode = gracePeriodCode;
	}

	@Id
	@Column( name = "grace_period_code", unique = true, nullable = false, length = 20 )
	public String getGracePeriodCode()
	{
		return gracePeriodCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setGracePeriodCode( String gracePeriodCode )
	{
		this.gracePeriodCode = gracePeriodCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}