<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	
	<sql-query name="branchbank.getCodeCity">
		SELECT city.*
	  	FROM code_branch_bank bank
	  	JOIN code_town town ON bank.town_code = town.town_code
	  	JOIN code_city city ON town.city_code = city.city_code
		JOIN code_branch_bank_loan loan ON bank.branch_bank_id = loan.branch_bank_id
		WHERE loan.loan_type = coalesce( :loanType, loan.loan_type )
		AND bank.head_office = 0
		AND bank.disabled = 0
		AND loan.allowed_user IN ( :allowedUser )
	  	GROUP BY city.city_code, city.name, city.display_order
		ORDER BY city.display_order
	</sql-query>
	
	<sql-query name="branchbank.getCodeTownByCity">
	  	SELECT town.*
	  	FROM code_branch_bank bank
	  	JOIN code_town town ON bank.town_code = town.town_code
	  	JOIN code_city city ON town.city_code = city.city_code
		JOIN code_branch_bank_loan loan ON bank.branch_bank_id = loan.branch_bank_id
	  	WHERE loan.loan_type = coalesce( :loanType, loan.loan_type )
		AND city.city_code = coalesce( :cityCode, city.city_code )
		AND loan.allowed_user IN ( :allowedUser )
		AND bank.head_office = 0
		AND bank.disabled = 0
	  	GROUP BY town.town_code, town.name, town.city_code, town.postal_code
	</sql-query>
	
	<sql-query name="branchbank.getList">
	  	SELECT bank.*
	  	FROM code_branch_bank bank
		JOIN code_branch_bank_loan loan ON bank.branch_bank_id = loan.branch_bank_id
	  	WHERE loan.loan_type = coalesce( :loanType, loan.loan_type )
	  	AND bank.branch_bank_id = coalesce( :branchBankId, bank.branch_bank_id )
	  	AND bank.head_office = coalesce( :headOffice, bank.head_office )
	  	AND bank.disabled = 0
	</sql-query>
	
	<sql-query name="branchbank.getListByTownCode">
	  	SELECT bank.*
	  	FROM code_branch_bank bank
	  	JOIN code_town town ON bank.town_code = town.town_code
	  	JOIN code_branch_bank_loan loan ON bank.branch_bank_id = loan.branch_bank_id
	  	WHERE loan.loan_type = coalesce( :loanType, loan.loan_type )
		AND town.town_code = coalesce( :townCode, town.town_code )
		AND loan.allowed_user IN ( :allowedUser )
		AND bank.head_office = 0
		AND bank.disabled = 0
	</sql-query>
	
	<sql-query name="branchbank.getBranchBankByEmpId">
		SELECT bank.*
		FROM code_branch_bank bank
		JOIN code_emp_info emp ON bank.bank_code = IIF(emp.sub_branch_bank_code != '', emp.sub_branch_bank_code, emp.branch_bank_code)
		WHERE emp.emp_id = :empId
	</sql-query>
	
</hibernate-mapping>
