package com.megabank.olp.client.sender.micro.modal.survey.bean;

import java.math.BigDecimal;

import com.megabank.olp.base.bean.BaseBean;

public class SurveyResultResultBean extends BaseBean
{
	private BigDecimal loanRequestAmt;

	private Integer loanPeriod;

	private BigDecimal loanRate;

	private Boolean hasResult;

	public SurveyResultResultBean()
	{}

	public Boolean getHasResult()
	{
		return hasResult;
	}

	public Integer getLoanPeriod()
	{
		return loanPeriod;
	}

	public BigDecimal getLoanRate()
	{
		return loanRate;
	}

	public BigDecimal getLoanRequestAmt()
	{
		return loanRequestAmt;
	}

	public void setHasResult( Boolean hasResult )
	{
		this.hasResult = hasResult;
	}

	public void setLoanPeriod( Integer loanPeriod )
	{
		this.loanPeriod = loanPeriod;
	}

	public void setLoanRate( BigDecimal loanRate )
	{
		this.loanRate = loanRate;
	}

	public void setLoanRequestAmt( BigDecimal loanRequestAmt )
	{
		this.loanRequestAmt = loanRequestAmt;
	}
}
