package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeEducationLevel is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_education_level" )
public class CodeEducationLevel extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_education_level";

	public static final String EDUCATION_LEVEL_CODE_CONSTANT = "educationLevelCode";

	public static final String NAME_CONSTANT = "name";

	private String educationLevelCode;

	private String name;

	public CodeEducationLevel()
	{}

	public CodeEducationLevel( String educationLevelCode )
	{
		this.educationLevelCode = educationLevelCode;
	}

	@Id
	@Column( name = "education_level_code", unique = true, nullable = false, length = 20 )
	public String getEducationLevelCode()
	{
		return educationLevelCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setEducationLevelCode( String educationLevelCode )
	{
		this.educationLevelCode = educationLevelCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}