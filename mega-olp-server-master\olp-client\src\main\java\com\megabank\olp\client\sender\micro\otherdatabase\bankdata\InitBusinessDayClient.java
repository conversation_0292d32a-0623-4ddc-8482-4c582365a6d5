package com.megabank.olp.client.sender.micro.otherdatabase.bankdata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseScheduleOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.bankdata.bean.BussinessDayGetListResultBean;
import com.megabank.olp.client.utility.bean.EmptyArgBean;

@Component
public class InitBusinessDayClient extends BaseScheduleOtherDatabaseClient<EmptyArgBean, BussinessDayGetListResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/megaibl/bankdata/getBusinessDay";
	}
}
