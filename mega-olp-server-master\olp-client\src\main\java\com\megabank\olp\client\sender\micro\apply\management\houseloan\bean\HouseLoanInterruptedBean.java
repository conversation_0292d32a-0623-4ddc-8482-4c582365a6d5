package com.megabank.olp.client.sender.micro.apply.management.houseloan.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanInterruptedBean extends BaseBean
{
	@JsonProperty( "id" )
	private Long loanId;

	private String caseNo;

	private String idNo;

	private String name;

	private String birthDate;

	private String mobileNumber;

	private Date createdDate;

	private String notificationStatusName;

	private String processStatusName;

	private String branchBankName;

	public HouseLoanInterruptedBean()
	{
		// default constructor
	}

	public String getBirthDate()
	{
		return birthDate;
	}

	public String getBranchBankName()
	{
		return branchBankName;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public Long getLoanId()
	{
		return loanId;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatusName()
	{
		return notificationStatusName;
	}

	public String getProcessStatusName()
	{
		return processStatusName;
	}

	public void setBirthDate( String birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setBranchBankName( String branchBankName )
	{
		this.branchBankName = branchBankName;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setLoanId( Long loanId )
	{
		this.loanId = loanId;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatusName( String notificationStatusName )
	{
		this.notificationStatusName = notificationStatusName;
	}

	public void setProcessStatusName( String processStatusName )
	{
		this.processStatusName = processStatusName;
	}

}