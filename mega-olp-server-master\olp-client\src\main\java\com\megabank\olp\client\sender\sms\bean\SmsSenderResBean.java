package com.megabank.olp.client.sender.sms.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlRootElement( name = "aMsgToHiB2BResponse" )
@XmlAccessorType( XmlAccessType.FIELD )
public class SmsSenderResBean extends BaseBean
{

	@XmlElement( name = "Result" )
	private ResultBean resultBean = new ResultBean();

	public SmsSenderResBean()
	{}

	public ResultBean getResultBean()
	{
		return resultBean;
	}

	public void setResultBean( ResultBean resultBean )
	{
		this.resultBean = resultBean;
	}

}
