package com.megabank.olp.client.sender.mydata.report.bean;

import com.megabank.olp.client.sender.mydata.BaseMyDataResultBean;

public class MyDataReportedResultBean extends BaseMyDataResultBean
{
	private boolean isSuccess;

	public MyDataReportedResultBean()
	{}

	public boolean isSuccess()
	{
		return isSuccess;
	}

	public void setSuccess( boolean isSuccess )
	{
		this.isSuccess = isSuccess;
	}

}
