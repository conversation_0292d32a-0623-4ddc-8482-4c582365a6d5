<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opencensus</groupId>
  <artifactId>opencensus-api</artifactId>
  <version>0.15.0</version>
  <name>OpenCensus</name>
  <description>null</description>
  <url>https://github.com/census-instrumentation/opencensus-java</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>io.opencensus</id>
      <name>OpenCensus Contributors</name>
      <email><EMAIL></email>
      <url>opencensus.io</url>
      <organization>OpenCensus Authors</organization>
      <organizationUrl>https://www.opencensus.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:svn:https://github.com/census-instrumentation/opencensus-java</connection>
    <developerConnection>scm:git:**************/census-instrumentation/opencensus-java</developerConnection>
    <url>https://github.com/census-instrumentation/opencensus-java</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-context</artifactId>
      <version>1.12.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>20.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava-testlib</artifactId>
      <version>20.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>1.9.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.truth</groupId>
      <artifactId>truth</artifactId>
      <version>0.30</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
