package com.megabank.olp.client.sender.eloan.getted.casestatus.bean;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class EloanCaseStatusResultBean extends BaseBean
{
	private Integer caseCount;

	@JsonProperty( "caseList" )
	private List<EloanCaseStatusBean> eloanCaseStatusBeans = new ArrayList<>();

	public EloanCaseStatusResultBean()
	{}

	public Integer getCaseCount()
	{
		return caseCount;
	}

	public List<EloanCaseStatusBean> getEloanCaseStatusBeans()
	{
		return eloanCaseStatusBeans;
	}

	public void setCaseCount( Integer caseCount )
	{
		this.caseCount = caseCount;
	}

	public void setEloanCaseStatusBeans( List<EloanCaseStatusBean> eloanCaseStatusBeans )
	{
		this.eloanCaseStatusBeans = eloanCaseStatusBeans;
	}

}
