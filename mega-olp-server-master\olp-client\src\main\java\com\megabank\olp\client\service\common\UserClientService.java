package com.megabank.olp.client.service.common;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.threadlocal.SessionInfoThreadLocal;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.user.CurrentIdentityInfoClient;
import com.megabank.olp.client.sender.micro.user.IdentityIdsClient;
import com.megabank.olp.client.sender.micro.user.IdentityInfoClient;
import com.megabank.olp.client.sender.micro.user.UserSubTypeUpdatedClient;
import com.megabank.olp.client.sender.micro.user.UserTypesClient;
import com.megabank.olp.client.sender.micro.user.bean.IdentityIdsArgBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoArgBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;
import com.megabank.olp.client.sender.micro.user.bean.UserSubTypeUpdatedArgBean;
import com.megabank.olp.client.sender.micro.user.bean.UserTypesArgBean;
import com.megabank.olp.client.utility.BaseClientService;
import com.megabank.olp.client.utility.bean.EmptyArgBean;

@Service
@Transactional
public class UserClientService extends BaseClientService
{
	@Autowired
	private CurrentIdentityInfoClient currentIdentityInfoClient;

	@Autowired
	private IdentityIdsClient identityIdsClient;

	@Autowired
	private IdentityInfoClient identityInfoClient;

	@Autowired
	private UserTypesClient userTypesClient;

	@Autowired
	private UserSubTypeUpdatedClient userSubTypeUpdatedClient;
	
	@Autowired
	private SessionInfoThreadLocal sessionInfoThreadLocal;

	public IdentityInfoResultBean getCurrentIdentityInfoResult()
	{
		EmptyArgBean argBean = new EmptyArgBean();

		return currentIdentityInfoClient.send( argBean );
	}

	public List<Long> getIdentitiyIdsResult( String idNo, Date birthDate )
	{
		return getIdentitiyIdsResult( idNo, birthDate, null );
	}

	public List<Long> getIdentitiyIdsResult( String idNo, Date birthDate, String userType )
	{
		IdentityIdsArgBean argBean = new IdentityIdsArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( birthDate );
		argBean.setUserType( userType );

		return sessionInfoThreadLocal.get() == null ? identityIdsClient.send( argBean, new JwtArgBean() ) : identityIdsClient.send( argBean );
	}

	public IdentityInfoResultBean getIdentityInfoResult( Long validatedIdentityId )
	{
		IdentityInfoArgBean argBean = new IdentityInfoArgBean();
		argBean.setValidatedIdentityId( validatedIdentityId );

		return sessionInfoThreadLocal.get() == null ? identityInfoClient.send( argBean, new JwtArgBean() ) : identityInfoClient.send( argBean );
	}

	public List<String> getUserTypesResult( List<Long> identityIds )
	{
		UserTypesArgBean argBean = new UserTypesArgBean();
		argBean.setIdentityIds( identityIds );

		return sessionInfoThreadLocal.get() == null ? userTypesClient.send( argBean, new JwtArgBean() ) : userTypesClient.send( argBean );
	}

	public void updateUserSubType( String userSubType )
	{
		UserSubTypeUpdatedArgBean argBean = new UserSubTypeUpdatedArgBean();
		argBean.setUserSubType( userSubType );

		userSubTypeUpdatedClient.send( argBean );
	}

}
