<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright DataStax, Inc.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.datastax.oss</groupId>
  <artifactId>java-driver-bom</artifactId>
  <version>4.14.1</version>
  <packaging>pom</packaging>
  <name>DataStax Java driver for Apache Cassandra(R) - Bill Of Materials</name>
  <description>A driver for Apache Cassandra(R) 2.1+ that works exclusively with the Cassandra Query Language version 3 (CQL3) and Cassandra's native protocol versions 3 and above.</description>
  <url>https://github.com/datastax/java-driver/java-driver-bom</url>
  <inceptionYear>2017</inceptionYear>
  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>Apache License Version 2.0</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Various</name>
      <organization>DataStax</organization>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:datastax/java-driver.git/java-driver-bom</connection>
    <developerConnection>scm:git:**************:datastax/java-driver.git/java-driver-bom</developerConnection>
    <tag>4.14.1</tag>
    <url>https://github.com/datastax/java-driver/java-driver-bom</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core-shaded</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-processor</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-runtime</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-query-builder</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-test-infra</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-metrics-micrometer</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-metrics-microprofile</artifactId>
        <version>4.14.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>native-protocol</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-shaded-guava</artifactId>
        <version>25.1-jre-graal-sub-1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
