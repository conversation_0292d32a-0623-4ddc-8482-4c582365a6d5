/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase;

import com.megabank.olp.client.sender.micro.BaseScheduleMicroServicesClient;

import java.io.UnsupportedEncodingException;
import java.net.URI;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public abstract class BaseScheduleOtherDatabaseClient<TArg, TResult> extends BaseScheduleMicroServicesClient<TArg, TResult>
{
	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlMicroOtherDatabase() + getSuffixUrl() );
	}

	@Override
	protected String getSystemName()
	{
		return "other-database";
	}
}