<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>com.google.apis</groupId>
  <artifactId>google-api-services-translate</artifactId>
  <version>v2-rev51-1.24.1</version>
  <name>Google Cloud Translation API v2-rev51-1.24.1</name>
  <packaging>jar</packaging>

  <inceptionYear>2011</inceptionYear>

  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.3.1</version>
        <configuration>
          <archive>
            <manifestEntries>
              <Built-By>Google</Built-By>
              <Build-Jdk>1.6.x</Build-Jdk>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <doctitle>Google Cloud Translation API ${project.version}</doctitle>
          <windowtitle>Google Cloud Translation API ${project.version}</windowtitle>
          <links>
            <link>http://docs.oracle.com/javase/7/docs/api</link>
          
            <link>http://javadoc.google-http-java-client.googlecode.com/hg/1.24.1</link>
          
            <link>http://javadoc.google-oauth-java-client.googlecode.com/hg/1.24.1</link>
          
            <link>http://javadoc.google-api-java-client.googlecode.com/hg/1.24.1</link>
          </links>
        </configuration>
      </plugin>
    </plugins>
    <sourceDirectory>.</sourceDirectory>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.google.api-client</groupId>
      <artifactId>google-api-client</artifactId>
      <version>1.24.1</version>
    </dependency>
  </dependencies>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>