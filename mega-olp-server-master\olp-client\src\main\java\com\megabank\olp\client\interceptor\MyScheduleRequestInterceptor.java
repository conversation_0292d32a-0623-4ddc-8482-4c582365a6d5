package com.megabank.olp.client.interceptor;

import com.megabank.olp.base.bean.threadlocal.RequestInfoThreadLocalBean;
import com.megabank.olp.base.threadlocal.ScheduleRequestInfoThreadLocal;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

public class MyScheduleRequestInterceptor implements ClientHttpRequestInterceptor
{
	private ScheduleRequestInfoThreadLocal scheduleRequestInfoThreadLocal;

	public MyScheduleRequestInterceptor( ScheduleRequestInfoThreadLocal scheduleRequestInfoThreadLocal )
	{
		this.scheduleRequestInfoThreadLocal = scheduleRequestInfoThreadLocal;
	}
	
	@Override
	public ClientHttpResponse intercept( HttpRequest request, byte[] body, ClientHttpRequestExecution execution ) throws IOException
	{
		long startTime = System.currentTimeMillis();

		try
		{
			return execution.execute( request, body );
		}
		finally
		{
			long endTime = System.currentTimeMillis();

			RequestInfoThreadLocalBean localBean = scheduleRequestInfoThreadLocal.get();

			long elpased = endTime - startTime;

			localBean.setElapsedOtherSystem( localBean.getElapsedOtherSystem() + elpased );
		}
	}
}
