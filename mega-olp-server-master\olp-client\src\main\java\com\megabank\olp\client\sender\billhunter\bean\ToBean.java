package com.megabank.olp.client.sender.billhunter.bean;

import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class ToBean extends BaseBean
{
	@XmlElement( name = "User" )
	private List<UserBean> userBeans;

	public ToBean()
	{}

	public List<UserBean> getUserBeans()
	{
		return userBeans;
	}

	public void setUserBeans( List<UserBean> userBean )
	{
		userBeans = userBean;
	}
}
