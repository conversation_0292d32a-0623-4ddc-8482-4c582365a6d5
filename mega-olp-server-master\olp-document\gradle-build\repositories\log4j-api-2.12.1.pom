<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements. See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache license, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License. You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the license for the specific language governing permissions and
  ~ limitations under the license.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j</artifactId>
    <version>2.12.1</version>
    <relativePath>../</relativePath>
  </parent>
  <artifactId>log4j-api</artifactId>
  <packaging>jar</packaging>
  <name>Apache Log4j API</name>
  <description>The Apache Log4j API</description>
  <properties>
    <log4jParentDir>${basedir}/..</log4jParentDir>
    <docLabel>API Documentation</docLabel>
    <projectDir>/api</projectDir>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api-java9</artifactId>
      <scope>provided</scope>
      <type>zip</type>
    </dependency>
    <!-- Place Felix before Equinox because Felix is signed. / also place it before org.osgi.core so that its versions of the OSGi classes are used -->
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.apache.felix.framework</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.core</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.tycho</groupId>
      <artifactId>org.eclipse.osgi</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Required for JSON support -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Required for JSON support -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.0.2</version>
        <executions>
          <execution>
            <id>unpack-classes</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>unpack</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>org.apache.logging.log4j</groupId>
                  <artifactId>log4j-api-java9</artifactId>
                  <version>${project.version}</version>
                  <type>zip</type>
                  <overWrite>false</overWrite>
                </artifactItem>
              </artifactItems>
              <includes>**/*.class</includes>
              <excludes>**/*.java</excludes>
              <outputDirectory>${project.build.directory}</outputDirectory>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>true</overWriteSnapshots>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>add-source</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${project.build.directory}/log4j-api-java9</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>default-compile</id>
            <!-- recompile everything for target VM except the module-info.java -->
            <configuration>
              <source>1.7</source>
              <target>1.7</target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration combine.self="override">
              <archive>
                <manifestFile>${manifestfile}</manifestFile>
                <manifestEntries>
                  <Specification-Title>${project.name}</Specification-Title>
                  <Specification-Version>${project.version}</Specification-Version>
                  <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                  <Implementation-Title>${project.name}</Implementation-Title>
                  <Implementation-Version>${project.version}</Implementation-Version>
                  <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                  <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
                  <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                  <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                  <Multi-Release>true</Multi-Release>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
          <execution>
            <id>default</id>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <archive>
                <manifestFile>${manifestfile}</manifestFile>
                <manifestEntries>
                  <Specification-Title>${project.name}</Specification-Title>
                  <Specification-Version>${project.version}</Specification-Version>
                  <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                  <Implementation-Title>${project.name}</Implementation-Title>
                  <Implementation-Version>${project.version}</Implementation-Version>
                  <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                  <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
                  <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                  <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- Include the standard NOTICE and LICENSE -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <instructions>
            <Export-Package>org.apache.logging.log4j.*</Export-Package>
            <Import-Package>
              sun.reflect;resolution:=optional,
              *
            </Import-Package>
            <Bundle-Activator>org.apache.logging.log4j.util.Activator</Bundle-Activator>
            <_fixupmessages>"Classes found in the wrong directory";is:=warning</_fixupmessages>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>${deploy.plugin.version}</version>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${changes.plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <issueLinkTemplate>%URL%/show_bug.cgi?id=%ISSUE%</issueLinkTemplate>
          <useJql>true</useJql>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.plugin.version}</version>
        <configuration>
          <!--<propertiesLocation>${vfs.parent.dir}/checkstyle.properties</propertiesLocation> -->
          <configLocation>${log4jParentDir}/checkstyle.xml</configLocation>
          <suppressionsLocation>${log4jParentDir}/checkstyle-suppressions.xml</suppressionsLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <propertyExpansion>basedir=${basedir}</propertyExpansion>
          <propertyExpansion>licensedir=${log4jParentDir}/checkstyle-header.txt</propertyExpansion>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${javadoc.plugin.version}</version>
        <configuration>
          <bottom><![CDATA[<p align="center">Copyright &#169; {inceptionYear}-{currentYear} {organizationName}. All Rights Reserved.<br />
            Apache Logging, Apache Log4j, Log4j, Apache, the Apache feather logo, the Apache Logging project logo,
            and the Apache Log4j logo are trademarks of The Apache Software Foundation.</p>]]></bottom>
          <!-- module link generation is completely broken in the javadoc plugin for a multi-module non-aggregating
               project -->
          <doclint>none</doclint>
          <detectOfflineLinks>false</detectOfflineLinks>
          <linksource>true</linksource>
          <links>
            <link>http://www.osgi.org/javadoc/r4v43/core/</link>
          </links>
        </configuration>
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${findbugs.plugin.version}</version>
        <configuration>
          <fork>true</fork>
          <jvmArgs>-Duser.language=en</jvmArgs>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${log4jParentDir}/findbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${jxr.plugin.version}</version>
        <reportSets>
          <reportSet>
            <id>non-aggregate</id>
            <reports>
              <report>jxr</report>
            </reports>
          </reportSet>
          <reportSet>
            <id>aggregate</id>
            <reports>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${pmd.plugin.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
</project>

