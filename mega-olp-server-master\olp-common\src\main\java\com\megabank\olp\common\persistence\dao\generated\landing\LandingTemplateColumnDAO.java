package com.megabank.olp.common.persistence.dao.generated.landing;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.landing.LandingTemplateColumn;

/**
 * The LandingTemplateColumnDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class LandingTemplateColumnDAO extends BasePojoDAO<LandingTemplateColumn, Long>
{
	@Autowired
	private LandingTemplateDAO landingTemplateDAO;

	public List<LandingTemplateColumn> getPojosByTemplateOrder( Long templateId )
	{
		Validate.notNull( templateId );

		NameValueBean condition = new NameValueBean( LandingTemplateColumn.LANDING_TEMPLATE_CONSTANT, landingTemplateDAO.read( templateId ) );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( LandingTemplateColumn.COLUMN_ORDER_CONSTANT ) };

		return this.getPojosByPropertyOrderBy( condition, orderBeans );
	}

	public LandingTemplateColumn read( Long templateColumnId )
	{
		Validate.notNull( templateColumnId );

		return getPojoByPK( templateColumnId, LandingTemplateColumn.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<LandingTemplateColumn> getPojoClass()
	{
		return LandingTemplateColumn.class;
	}
}
