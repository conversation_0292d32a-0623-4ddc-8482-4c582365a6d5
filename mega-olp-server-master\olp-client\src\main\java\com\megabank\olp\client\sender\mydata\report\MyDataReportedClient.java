package com.megabank.olp.client.sender.mydata.report;

import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.client.sender.mydata.BaseMyDataClient;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedArgBean;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedReqBean;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedResBean;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedResultBean;
import com.megabank.olp.client.utility.enums.ClientMyDataErrorEnum;

@Component
public class MyDataReportedClient
			extends BaseMyDataClient<MyDataReportedArgBean, MyDataReportedReqBean, MyDataReportedResBean, MyDataReportedResultBean>
{

	@Override
	protected ErrorEnum getMyDataErrorEnum()
	{
		return ClientMyDataErrorEnum.REPORTED_MY_DATA_COMMON;
	}

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000" };
	}

	@Override
	protected MyDataReportedResultBean getResultBean( MyDataReportedResBean resBean )
	{
		MyDataReportedResultBean resultBean = new MyDataReportedResultBean();
		resultBean.setSuccess( "0000".equals( resBean.getReturnCode() ) );

		return resultBean;
	}

	@Override
	protected String getSimulatorCode( MyDataReportedArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/reportUserDataOk";
	}

	@Override
	protected MyDataReportedReqBean transArg2Req( MyDataReportedArgBean argBean )
	{
		MyDataReportedReqBean reqBean = new MyDataReportedReqBean();
		reqBean.setServiceId( argBean.getServiceId() );
		reqBean.setTxId( argBean.getTxId() );

		return reqBean;
	}

}
