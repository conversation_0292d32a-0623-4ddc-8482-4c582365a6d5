package com.megabank.olp.client.sender.micro.apply.management.house;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HousePricingBranchBankUpdatedArgBean;

@Component
public class HousePricingBranchBankUpdatedClient extends BaseApplyClient<HousePricingBranchBankUpdatedArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/housepricing/updateBranchBank";
	}
}
