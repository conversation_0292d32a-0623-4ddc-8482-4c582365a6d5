<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.microsoft.sqlserver</groupId>
	<artifactId>mssql-jdbc</artifactId>
	<version>7.4.1.jre8</version>
	<packaging>jar</packaging>

	<name>Microsoft JDBC Driver for SQL Server</name>

	<description>
		Microsoft JDBC Driver for SQL Server.
	</description>

	<url>https://github.com/Microsoft/mssql-jdbc</url>

	<licenses>
		<license>
			<name>MIT License</name>
			<url>http://www.opensource.org/licenses/mit-license.php</url>
		</license>
	</licenses>

	<organization>
		<name>Microsoft Corporation</name>
	</organization>

	<developers>
		<developer>
			<organization>Microsoft</organization>
			<organizationUrl>http://www.microsoft.com</organizationUrl>
		</developer>
	</developers>

	<scm>
		<url>https://github.com/Microsoft/mssql-jdbc</url>
	</scm>

	<properties>
		<!-- Allowed values for excluded Groups here - - - - - - - - - - - - - 
			xJDBC42 - - - - - - For tests not compatible with JDBC 42 Specifications 
			xGradle - - - - - - For tests not compatible with Gradle Script - - - - - 
			xSQLv12 - - - - - - For tests not compatible with SQL Server 2008 R2 - 2014 
			xSQLv14 - - - - - - For tests not compatible with SQL Server 2016 - 2017 
			xSQLv15 - - - - - - For tests not compatible with SQL Server 2019 - - - - 
			xAzureSQLDB - - - - For tests not compatible with Azure SQL Database - - 
			xAzureSQLDW - - - - For tests not compatible with Azure Data Warehouse - 
			xAzureSQLMI - - - - For tests not compatible with Azure SQL Managed Instance 
			NTLM - - - - - - For tests using NTLM Authentication mode (excluded by default) 
			- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
			Default testing enabled with SQL Server 2019 (SQLv14) -->
		<excludedGroups>xSQLv15, NTLM</excludedGroups>

		<!-- Driver Dependencies -->
		<azure.keyvault.version>1.2.1</azure.keyvault.version>
		<azure.adal4j.version>1.6.4</azure.adal4j.version>
		<rest.client.version>1.6.10</rest.client.version>
		<osgi.core.version>6.0.0</osgi.core.version>
		<osgi.comp.version>5.0.0</osgi.comp.version>
		<antlr.runtime.version>4.7.2</antlr.runtime.version>

		<!-- JUnit Test Dependencies -->
		<junit.platform.version>[1.3.2, 1.4.2]</junit.platform.version>
		<junit.jupiter.version>5.4.2</junit.jupiter.version>
		<hikaricp.version>3.3.1</hikaricp.version>
		<dbcp2.version>2.6.0</dbcp2.version>
		<slf4j.nop.version>1.7.26</slf4j.nop.version>
		<gemini.mock.version>2.1.0.RELEASE</gemini.mock.version>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>${project.build.sourceEncoding}</project.reporting.outputEncoding>
		<enforcer.skip>false</enforcer.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.microsoft.azure</groupId>
			<artifactId>azure-keyvault</artifactId>
			<version>${azure.keyvault.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.microsoft.azure</groupId>
			<artifactId>adal4j</artifactId>
			<version>${azure.adal4j.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.microsoft.rest</groupId>
			<artifactId>client-runtime</artifactId>
			<version>${rest.client.version}</version>
			<optional>true</optional>
		</dependency>

		<!-- dependencies for ANTLR -->
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4-runtime</artifactId>
			<version>${antlr.runtime.version}</version>
			<optional>true</optional>
		</dependency>

		<!-- dependencies provided by an OSGi-Framework -->
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.core</artifactId>
			<version>${osgi.core.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.osgi</groupId>
			<artifactId>org.osgi.compendium</artifactId>
			<version>${osgi.comp.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- dependencies for running tests -->
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-console</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-commons</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-engine</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-launcher</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-runner</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-surefire-provider</artifactId>
			<version>${junit.platform.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>${junit.jupiter.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>${junit.jupiter.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
			<version>${hikaricp.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-dbcp2 </artifactId>
			<version>${dbcp2.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-nop</artifactId>
			<version>${slf4j.nop.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.eclipse.gemini.blueprint</groupId>
			<artifactId>gemini-blueprint-mock</artifactId>
			<version>${gemini.mock.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<profiles>
		<profile>
			<id>jre8</id>
			<build>
				<finalName>${project.artifactId}-${project.version}.jre8</finalName>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>3.8.0</version>
						<configuration>
							<excludes>
								<exclude>**/com/microsoft/sqlserver/jdbc/SQLServerJdbc43.java</exclude>
							</excludes>
							<source>1.8</source>
							<target>1.8</target>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<configuration>
							<source>8</source>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-jar-plugin</artifactId>
						<version>3.1.1</version>
						<configuration>
							<archive>
								<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
							</archive>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<version>3.0.0-M1</version>
						<configuration>
							<!-- Exclude [xJDBC42] For tests not compatible with JDBC 4.2 Specifications -->
							<excludedGroups>${excludedGroups}, xJDBC42</excludedGroups>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>jre11</id>
			<build>
				<finalName>${project.artifactId}-${project.version}.jre11</finalName>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>3.8.0</version>
						<configuration>
							<excludes>
								<exclude>**/com/microsoft/sqlserver/jdbc/SQLServerJdbc42.java</exclude>
							</excludes>
							<source>11</source>
							<target>11</target>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-jar-plugin</artifactId>
						<version>3.1.1</version>
						<configuration>
							<archive>
								<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
								<manifestEntries>
									<Automatic-Module-Name>com.microsoft.sqlserver.jdbc</Automatic-Module-Name>
								</manifestEntries>
							</archive>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>jre12</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<finalName>${project.artifactId}-${project.version}.jre12</finalName>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-compiler-plugin</artifactId>
						<version>3.8.0</version>
						<configuration>
							<excludes>
								<exclude>**/com/microsoft/sqlserver/jdbc/SQLServerJdbc42.java</exclude>
							</excludes>
							<source>12</source>
							<target>12</target>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-jar-plugin</artifactId>
						<version>3.1.1</version>
						<configuration>
							<archive>
								<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
								<manifestEntries>
									<Automatic-Module-Name>com.microsoft.sqlserver.jdbc</Automatic-Module-Name>
								</manifestEntries>
							</archive>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
	<build>
		<resources>
			<resource>
				<directory>${basedir}</directory>
				<includes>
					<include>META-INF/services/java.sql.Driver</include>
				</includes>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>src/test/resources</directory>
				<includes>
					<include>**/*.csv</include>
				</includes>
			</testResource>
			<testResource>
				<directory>AE_Certificates</directory>
				<includes>
					<include>**/*.txt</include>
					<include>**/*.jks</include>
				</includes>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<!-- Run shade goal on package phase -->
					<execution>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<excludes>
									<exclude>junit:junit</exclude>
									<exclude>jmock:*</exclude>
									<exclude>*:xml-apis</exclude>
									<exclude>org.apache.maven:lib:tests</exclude>
									<exclude>log4j:log4j:jar:</exclude>
								</excludes>
							</artifactSet>
							<shadedArtifactAttached>true</shadedArtifactAttached>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
										<exclude>META-INF/NOTICE*</exclude>
									</excludes>
								</filter>
							</filters>
							<outputFile>${project.build.directory}\${project.build.finalName}-shaded.jar</outputFile>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.0.0-M2</version>
				<executions>
					<execution>
						<id>enforce-versions</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<bannedPlugins>
									<!-- will only display a warning but does not fail the build. -->
									<level>WARN</level>
									<excludes>
										<exclude>org.apache.maven.plugins:maven-verifier-plugin</exclude>
									</excludes>
									<message>Please consider using the maven-invoker-plugin
										(http://maven.apache.org/plugins/maven-invoker-plugin/)!</message>
								</bannedPlugins>
								<requireMavenVersion>
									<version>3.5.0</version>
								</requireMavenVersion>
								<requireJavaVersion>
									<version>11</version>
								</requireJavaVersion>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0-M1</version>
				<configuration>
					<forkCount>3</forkCount>
					<reuseForks>true</reuseForks>
					<argLine>${argLine} --illegal-access=permit -Xmx1024m
						-XX:MaxPermSize=256m
					</argLine>
					<excludedGroups>${excludedGroups}</excludedGroups>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!-- Create OSGI Headers -->
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>4.2.0</version>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<_exportcontents>
							com.microsoft.sqlserver.jdbc,
							com.microsoft.sqlserver.jdbc.osgi,
							com.microsoft.sqlserver.jdbc.dataclassification,
							microsoft.sql
						</_exportcontents>
						<Import-Package>!microsoft.sql,*</Import-Package>
						<Bundle-Activator>com.microsoft.sqlserver.jdbc.osgi.Activator</Bundle-Activator>
					</instructions>
				</configuration>
				<executions>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<failOnError>true</failOnError>
					<excludePackageNames>mssql.googlecode.*:mssql.security.provider.MD4</excludePackageNames>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<version>2.5</version>
				<inherited>true</inherited>
				<configuration>
					<outputFile>outdated-dependencies.txt</outputFile>
					<rulesUri>file:///${session.executionRootDirectory}/maven-version-rules.xml</rulesUri>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.3</version>
				<executions>
					<execution>
						<id>pre-test</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<fileSets>
						<fileSet>
							<directory>jacoco-execs/</directory>
							<includes>
								<include>*.exec</include>
							</includes>
						</fileSet>
					</fileSets>
					<!-- File containing the merged coverage data -->
					<destFile>${project.build.directory}/jacoco.exec</destFile>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<version>2.5.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.7.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven</groupId>
				<artifactId>maven-archiver</artifactId>
				<version>3.4.0</version>
			</plugin>
		</plugins>
	</build>
</project>
