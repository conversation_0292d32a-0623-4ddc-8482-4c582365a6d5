package com.megabank.olp.client.sender.micro.user.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class UserDataCreatedArgBean extends BaseBean
{
	private String idNo;

	private Date birthDate;

	private String mobileNumber;

	private String clientAddress;

	private String identityType;

	private Long identityId;

	private String userSubType;

	public UserDataCreatedArgBean()
	{}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getClientAddress()
	{
		return clientAddress;
	}

	public Long getIdentityId()
	{
		return identityId;
	}

	public String getIdentityType()
	{
		return identityType;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getUserSubType()
	{
		return userSubType;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setClientAddress( String clientAddress )
	{
		this.clientAddress = clientAddress;
	}

	public void setIdentityId( Long identityId )
	{
		this.identityId = identityId;
	}

	public void setIdentityType( String identityType )
	{
		this.identityType = identityType;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setUserSubType( String userSubType )
	{
		this.userSubType = userSubType;
	}

}
