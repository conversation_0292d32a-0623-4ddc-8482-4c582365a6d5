package com.megabank.olp.common.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.common.controller.bean.landing.ColumnListGetterArgBean;
import com.megabank.olp.common.controller.bean.landing.RiskInfoGetterArgBean;
import com.megabank.olp.common.service.LandingService;

@RestController
@RequestMapping( "open/landing" )
public class LandingController extends BaseController
{
	@Autowired
	private LandingService landingService;

	/**
	 * 取得行銷頁內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getColumnList" )
	public Map<String, Object> getColumnList( @RequestBody @Validated ColumnListGetterArgBean argBean )
	{
		return getResponseMap( landingService.getColumnList( argBean.getTemplateName() ) );
	}

	/**
	 * 取得試算方案資訊
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getRiskInfo" )
	public Map<String, Object> getRiskInfo( @RequestBody @Validated RiskInfoGetterArgBean argBean )
	{
		return getResponseMap( landingService.getRiskInfo( argBean.getRiskLevel() ) );
	}

}
