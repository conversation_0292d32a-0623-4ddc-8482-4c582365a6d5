<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.javassist</groupId>
  <artifactId>javassist</artifactId>
  <packaging>bundle</packaging>
  <description>
  	Javassist (JAVA programming ASSISTant) makes Java bytecode manipulation
    simple.  It is a class library for editing bytecodes in Java.
  </description>
  <version>3.24.0-GA</version>
  <name>Javassist</name>
  <url>http://www.javassist.org/</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <organization>
    <name><PERSON><PERSON><PERSON> Chiba, www.javassist.org</name>
  </organization>

  <issueManagement>
    <system>JIRA</system>
    <url>https://jira.jboss.org/jira/browse/JASSIST/</url>
  </issueManagement>
  <licenses>
    <!-- this is the license under which javassist is usually distributed
      -->
    <license>
      <name>MPL 1.1</name>
      <url>http://www.mozilla.org/MPL/MPL-1.1.html</url>
    </license>
    <!-- this is the license under which javassist is distributed when
	 it is bundled with JBoss
      -->
    <license>
      <name>LGPL 2.1</name>
      <url>http://www.gnu.org/licenses/lgpl-2.1.html</url>
    </license>
    <!-- this is the license under which javassist can be distributed.
      -->
    <license>
      <name>Apache License 2.0</name>
      <url>http://www.apache.org/licenses/</url>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:**************:jboss-javassist/javassist.git</connection>
    <developerConnection>scm:git:**************:jboss-javassist/javassist.git</developerConnection>
    <url>scm:git:**************:jboss-javassist/javassist.git</url>
  </scm>

  <developers>
    <developer>
      <id>chiba</id>
      <name>Shigeru Chiba</name>
      <email><EMAIL></email>
      <organization>The Javassist Project</organization>
      <organizationUrl>http://www.javassist.org/</organizationUrl>
      <roles>
        <role>project lead</role>
      </roles>
      <timezone>9</timezone>
    </developer>

    <developer>
      <id>adinn</id>
      <name>Andrew Dinn</name>
      <email><EMAIL></email>
      <organization>JBoss</organization>
      <organizationUrl>http://www.jboss.org/</organizationUrl>
      <roles>
        <role>contributing developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>

    <developer>
      <id><EMAIL></id>
      <name>Kabir Khan</name>
      <email><EMAIL></email>
      <organization>JBoss</organization>
      <organizationUrl>http://www.jboss.org/</organizationUrl>
      <roles>
        <role>contributing developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    
    <developer>
      <id>scottmarlow</id>
      <name>Scott Marlow</name>
      <email><EMAIL></email>
      <organization>JBoss</organization>
      <organizationUrl>http://www.jboss.org/</organizationUrl>
      <roles>
        <role>contributing developer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>


  </developers>

  <distributionManagement>
  <!--  
      You need entries in your .m2/settings.xml like this:
   <servers>
    <server>
       <id>jboss-releases-repository</id>
       <username>your_jboss.org_username</username>
       <password>password</password>
    </server>
    <server>
       <id>jboss-snapshots-repository</id>
       <username>your_jboss.org_username</username>
       <password>password</password>
    </server>
  </servers>
  
  To deploy a snapshot, you need to run
  
  mvn deploy -Dversion=3.x.y-SNAPSHOT

  To deploy a release you need to change the version to 3.x.y.GA and run

  mvn deploy
    -->
    <repository>
      <id>jboss-releases-repository</id>
      <name>JBoss Releases Repository</name>
      <url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>jboss-snapshots-repository</id>
      <name>JBoss Snapshots Repository</name>
      <url>https://repository.jboss.org/nexus/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <build>
    <sourceDirectory>src/main/</sourceDirectory>
    <testSourceDirectory>src/test/</testSourceDirectory>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.2</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <testSource>11</testSource>
          <testTarget>11</testTarget>
          <testCompilerArgument>-parameters</testCompilerArgument>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.18.1</version>
        <configuration>
          <includes>
            <include>javassist/JvstTest.java</include>
          </includes>
          <forkMode>once</forkMode>
          <additionalClasspathElements>
            <additionalClasspathElement>resources</additionalClasspathElement>
          </additionalClasspathElements>
          <workingDirectory>${project.build.directory}/runtest</workingDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.6</version>
        <configuration>
          <archive>
            <manifest>
              <mainClass>javassist.CtClass</mainClass>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.0.4</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.0.1</version>
	<configuration>
	  <attach>true</attach>
          <excludePackageNames>javassist.compiler:javassist.convert:javassist.scopedpool:javassist.bytecode.stackmap</excludePackageNames>
          <bottom><![CDATA[<i>Javassist, a Java-bytecode translator toolkit.<br>
Copyright (C) 1999- Shigeru Chiba. All Rights Reserved.</i>]]></bottom>
          <show>public</show>
          <nohelp>true</nohelp>
	</configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <supportedProjectTypes>
            <supportedProjectType>jar</supportedProjectType>
            <supportedProjectType>bundle</supportedProjectType>
            <supportedProjectType>war</supportedProjectType>
          </supportedProjectTypes>
          <instructions>
            <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
            <Bundle-Version>${project.version}</Bundle-Version>
            <Import-Package>!com.sun.jdi.*</Import-Package>	
            <Export-Package>!com.sun.jdi.*,javassist.*;version="${project.version}"</Export-Package>
          </instructions>
        </configuration>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <!-- profile for releasing to sonatype repo
	 exercise with mvn -PcentralRelease
      -->
    <profile>
      <id>centralRelease</id>
      <!-- obviously we need to use the Sonatype staging repo for upload -->
      <distributionManagement>
	<repository>
	  <id>sonatype-releases-repository</id>
	  <name>Sonatype Releases Repository</name>
	  <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
	</repository>
      </distributionManagement>
      <!-- we need to be able to sign the jars we install -->
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
              <useAgent>${gpg.useAgent}</useAgent>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- profiles to add tools jar containing com.sun.jdi code
	 needed by sample code
	 -->
    <profile>
      <id>default-tools</id>
      <activation>
        <jdk>[,1.8]</jdk>
      </activation>
      <dependencies>
        <dependency>
          <groupId>com.sun</groupId>
          <artifactId>tools</artifactId>
          <version>${java.version}</version>
          <scope>system</scope>
          <optional>true</optional>
          <systemPath>${java.home}/../lib/tools.jar</systemPath>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>java9-tools</id>
      <activation>
        <jdk>[1.9,]</jdk>
      </activation>
      <dependencies>
        <dependency>
          <groupId>com.sun</groupId>
          <artifactId>tools</artifactId>
          <version>${java.version}</version>
          <scope>system</scope>
          <optional>true</optional>
          <systemPath>${java.home}/lib/jrt-fs.jar</systemPath>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.12</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-all</artifactId>
        <version>1.3</version>
        <scope>test</scope>
    </dependency>
  </dependencies>
</project>

