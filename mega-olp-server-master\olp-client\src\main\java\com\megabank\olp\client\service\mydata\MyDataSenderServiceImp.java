package com.megabank.olp.client.service.mydata;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.mydata.report.MyDataReportedClient;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedArgBean;
import com.megabank.olp.client.sender.mydata.report.bean.MyDataReportedResultBean;
import com.megabank.olp.client.sender.mydata.txid.MyDataTxIdClient;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdArgBean;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdResultBean;
import com.megabank.olp.client.sender.mydata.userdata.MyDataFileClient;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileArgBean;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileResultBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ArgBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ResultBean;
import com.megabank.olp.client.sender.webcomm.mydata.type1.MyDataType1Client;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "sit", "stress", "uat", "prod" } )
public class MyDataSenderServiceImp implements MyDataSenderService
{
	@Autowired
	private MyDataFileClient myDataClient;

	@Autowired
	private MyDataReportedClient reportedClient;

	@Autowired
	private MyDataTxIdClient txIdClient;
	
	@Autowired
	private MyDataType1Client type1Client;

	@Override
	public String getTxId( String serviceId )
	{
		MyDataTxIdArgBean argBean = new MyDataTxIdArgBean();
		argBean.setServiceId( serviceId );

		MyDataTxIdResultBean resultBean = txIdClient.send( argBean );

		return resultBean.getTxId();
	}

	@Override
	public MyDataFileResultBean getUserData( String serviceId, String txId )
	{
		MyDataFileArgBean argBean = new MyDataFileArgBean();
		argBean.setServiceId( serviceId );
		argBean.setTxId( txId );

		return myDataClient.send( argBean );
	}

	@Override
	public boolean reportMyData( String serviceId, String txId )
	{
		MyDataReportedArgBean argBean = new MyDataReportedArgBean();
		argBean.setServiceId( serviceId );
		argBean.setTxId( txId );

		MyDataReportedResultBean resultBean = reportedClient.send( argBean );

		return resultBean.isSuccess();
	}

	@Override
	public MyDataType1ResultBean sendMyDataType1( MyDataType1ArgBean argBean )
	{
		return type1Client.send( argBean );
	}
}
