package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The LandingSolutionValue is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_solution_value" )
public class LandingSolutionValue extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_solution_value";

	public static final String SOLUTION_VALUE_ID_CONSTANT = "solutionValueId";

	public static final String LANDING_SOLUTION_CONSTANT = "landingSolution";

	public static final String LANDING_TEMPLATE_COLUMN_CONSTANT = "landingTemplateColumn";

	public static final String VALUE_CONSTANT = "value";

	public static final String SOLUTION_TEMP_ID_CONSTANT = "solutionTempId";

	public static final String LANDING_SOLUTION_SUB_VALUES_CONSTANT = "landingSolutionSubValues";

	private Long solutionValueId;

	private transient LandingSolution landingSolution;

	private transient LandingTemplateColumn landingTemplateColumn;

	private String value;

	private Long solutionTempId;

	private transient Set<LandingSolutionSubValue> landingSolutionSubValues = new HashSet<>( 0 );

	public LandingSolutionValue()
	{}

	public LandingSolutionValue( LandingTemplateColumn landingTemplateColumn )
	{
		this.landingTemplateColumn = landingTemplateColumn;
	}

	public LandingSolutionValue( Long solutionValueId )
	{
		this.solutionValueId = solutionValueId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "solution_id" )
	public LandingSolution getLandingSolution()
	{
		return landingSolution;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingSolutionValue" )
	public Set<LandingSolutionSubValue> getLandingSolutionSubValues()
	{
		return landingSolutionSubValues;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "template_column_id", nullable = false )
	public LandingTemplateColumn getLandingTemplateColumn()
	{
		return landingTemplateColumn;
	}

	@Column( name = "solution_temp_id" )
	public Long getSolutionTempId()
	{
		return solutionTempId;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "solution_value_id", unique = true, nullable = false )
	public Long getSolutionValueId()
	{
		return solutionValueId;
	}

	@Column( name = "value" )
	public String getValue()
	{
		return value;
	}

	public void setLandingSolution( LandingSolution landingSolution )
	{
		this.landingSolution = landingSolution;
	}

	public void setLandingSolutionSubValues( Set<LandingSolutionSubValue> landingSolutionSubValues )
	{
		this.landingSolutionSubValues = landingSolutionSubValues;
	}

	public void setLandingTemplateColumn( LandingTemplateColumn landingTemplateColumn )
	{
		this.landingTemplateColumn = landingTemplateColumn;
	}

	public void setSolutionTempId( Long solutionTempId )
	{
		this.solutionTempId = solutionTempId;
	}

	public void setSolutionValueId( Long solutionValueId )
	{
		this.solutionValueId = solutionValueId;
	}

	public void setValue( String value )
	{
		this.value = value;
	}
}