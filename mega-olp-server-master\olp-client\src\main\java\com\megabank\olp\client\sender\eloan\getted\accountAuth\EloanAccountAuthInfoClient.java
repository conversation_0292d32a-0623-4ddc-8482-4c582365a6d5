package com.megabank.olp.client.sender.eloan.getted.accountAuth;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.eloan.getted.EloanGettedClient;
import com.megabank.olp.client.sender.eloan.getted.accountAuth.bean.EloanAccountAuthInfoArgBean;
import com.megabank.olp.client.sender.eloan.getted.accountAuth.bean.EloanAccountAuthInfoResultBean;

@Component
public class EloanAccountAuthInfoClient extends EloanGettedClient<EloanAccountAuthInfoArgBean, EloanAccountAuthInfoResultBean>
{
	@Override
	protected String getSimulatorCode( EloanAccountAuthInfoArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/gw-web/txRecord/bankAccount";
	}

}
