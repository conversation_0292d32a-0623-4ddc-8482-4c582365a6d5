<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-modules-java8</artifactId>
    <version>2.11.2</version>
  </parent>
  <artifactId>jackson-module-parameter-names</artifactId>
  <name>Jackson-module-parameter-names</name>
  <packaging>bundle</packaging>
  <description>Add-on module for <PERSON> (http://jackson.codehaus.org) to support
introspection of method/constructor parameter names, without having to add explicit property name annotation.
</description>

  <properties>
    <!-- explicitly target JDK 8 -->
    <javac.src.version>1.8</javac.src.version>
    <javac.target.version>1.8</javac.target.version>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/module/paramnames</packageVersion.dir>
    <packageVersion.package>${project.groupId}.paramnames</packageVersion.package>

    <assertj-core.version>3.8.0</assertj-core.version>
    <mockito-core.version>1.10.19</mockito-core.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>${assertj-core.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito-core.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>[1.8,)</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.2</version>
        <inherited>true</inherited>
        <configuration>
          <source>${javac.src.version}</source>
          <target>${javac.target.version}</target>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
          <optimize>true</optimize>
          <compilerArgs>
            <arg>-Xlint</arg>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
     <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${version.plugin.surefire}</version>
        <configuration>
          <excludes>
            <exclude>${packageVersion.dir}/failing/*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!--  28-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
