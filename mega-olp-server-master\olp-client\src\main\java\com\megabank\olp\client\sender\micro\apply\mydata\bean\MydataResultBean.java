package com.megabank.olp.client.sender.micro.apply.mydata.bean;

import com.megabank.olp.base.bean.BaseBean;

public class MydataResultBean extends BaseBean
{

	private Integer status;

	private String msg;

	private String errorCode;

	public MydataResultBean()
	{}

	public Integer getStatus()
	{
		return status;
	}

	public void setStatus(Integer status)
	{
		this.status = status;
	}

	public String getMsg()
	{
		return msg;
	}

	public void setMsg(String msg)
	{
		this.msg = msg;
	}


	public String getErrorCode()
	{
		return errorCode;
	}

	public void setErrorCode(String errorCode)
	{
		this.errorCode = errorCode;
	}

}
