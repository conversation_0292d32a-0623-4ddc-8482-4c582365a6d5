package com.megabank.olp.client.sender.webcomm.mydata;

import com.megabank.olp.base.bean.BaseBean;

public class WebCommMyDataType1ResBean extends BaseBean
{
	private String returnCode;

	private String returnMsg;

	public WebCommMyDataType1ResBean()
	{}

	public String getReturnCode()
	{
		return returnCode;
	}

	public String getReturnMsg()
	{
		return returnMsg;
	}

	public void setReturnCode( String returnCode )
	{
		this.returnCode = returnCode;
	}

	public void setReturnMsg( String returnMsg )
	{
		this.returnMsg = returnMsg;
	}

}
