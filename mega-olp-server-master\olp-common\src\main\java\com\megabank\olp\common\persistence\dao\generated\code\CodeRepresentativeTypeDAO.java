package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeRepresentativeType;

import org.springframework.stereotype.Repository;

/**
 * The CodeRepresentativeTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRepresentativeTypeDAO extends BasePojoDAO<CodeRepresentativeType, String>
{
	@Override
	protected Class<CodeRepresentativeType> getPojoClass()
	{
		return CodeRepresentativeType.class;
	}
}
