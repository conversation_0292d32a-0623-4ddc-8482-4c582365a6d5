package com.megabank.olp.common.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.common.service.bean.code.BranchBankResBean;
import com.megabank.olp.common.service.bean.code.CodeResBean;
import com.megabank.olp.common.utility.enums.CodeTypeEnum;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { CommonConfig.class, SystemConfig.class } )
public class CodeServiceIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private CodeService service;

	@Test
	public void getAppropirationDateList()
	{

		List<String> result = service.getAppropirationDateList();

		logger.info( "result:{}", result );
	}

	@Test
	public void getBackendCodeList()
	{
		String codeType = CodeTypeEnum.HL_BRANCH_BANK.getContext();
		Long branchBankId = 109L;
		Long subBranchBankId = -1L;
		String originalBranchBankId = "943";
		String loanType = LoanTypeEnum.PERSONAL_LOAN.getContext();
		String recipient = RecipientSystemEnum.ILOAN.getContext();

		List<CodeResBean> resBeans = service.getBackendCodeList( codeType, branchBankId, subBranchBankId, originalBranchBankId, loanType, recipient );

		logger.info( "resBeans:{}", resBeans );
	}

	@Test
	public void getBranchBankList()
	{
		String bankType = CodeTypeEnum.PL_BRANCH_BANK.getContext();
		String townCode = "6801";

		List<BranchBankResBean> resBeans = service.getBranchBankList( townCode, bankType );

		logger.info( "resBeans:{}", resBeans );
	}

	@Test
	public void getCodeList()
	{
		String codeType = CodeTypeEnum.HL_USER_TYPE.getContext();

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		logger.info( "resBeans:{}", resBeans );
	}

	@Test
	public void getSubCodeList()
	{
		String codeType = CodeTypeEnum.HL_USER_TYPE.getContext();
		String codeId = "guarantor";

		List<CodeResBean> resBeans = service.getSubCodeList( codeType, codeId );

		logger.info( "resBeans:{}", resBeans );
	}

}
