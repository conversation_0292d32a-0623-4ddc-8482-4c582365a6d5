package com.megabank.olp.client.sender.micro.apply.loan.deliver;

import com.megabank.olp.client.sender.micro.apply.loan.deliver.bean.TargetRecipientSystemArgBean;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseScheduleApplyClient;

@Component
public class SigningContractRetriedClient extends BaseScheduleApplyClient<TargetRecipientSystemArgBean, String>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/loan/deliver/retrySigningContract";
	}
}
