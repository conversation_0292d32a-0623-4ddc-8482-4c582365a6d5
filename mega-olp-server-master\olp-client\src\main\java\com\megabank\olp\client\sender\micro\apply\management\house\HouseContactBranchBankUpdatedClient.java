package com.megabank.olp.client.sender.micro.apply.management.house;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.contract.HouseContactBranchBankUpdatedArgBean;

@Component
public class HouseContactBranchBankUpdatedClient extends BaseApplyClient<HouseContactBranchBankUpdatedArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/housecontact/updateBranchBank";
	}
}
