/**
 *
 */
package com.megabank.olp.client.sender.pib.custInfo.bean;

import com.megabank.olp.client.sender.pib.BasePibArgBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class PibCustInfoArgBean extends BasePibArgBean
{
	private String trackingIxd;

	private String clientIp;

	private String sessionId;

	private String accessToken;

	public String getAccessToken()
	{
		return accessToken;
	}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getSessionId()
	{
		return sessionId;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public void setAccessToken( String accessToken )
	{
		this.accessToken = accessToken;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setSessionId( String sessionId )
	{
		this.sessionId = sessionId;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}
}
