package com.megabank.olp.client.sender.sms.bean;

import java.text.SimpleDateFormat;
import java.util.Date;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "itemBean", "batchNo" } )
public class RequestBean extends BaseBean
{
	@XmlAttribute( name = "xmlns" )
	private String xmlns = "http://HiB2B/webservices";

	@XmlElement( name = "Item" )
	private ItemBean itemBean;

	/**
	 * 批號
	 */
	@XmlElement( name = "BatchNo" )
	private String batchNo = new SimpleDateFormat( "yyyyMMddHHmmss" ).format( new Date() );

	public RequestBean()
	{}

	public String getBatchNo()
	{
		return batchNo;
	}

	public ItemBean getItemBean()
	{
		return itemBean;
	}

	public String getXmlns()
	{
		return xmlns;
	}

	public void setBatchNo( String batchNo )
	{
		this.batchNo = batchNo;
	}

	public void setItemBean( ItemBean itemBean )
	{
		this.itemBean = itemBean;
	}

	public void setXmlns( String xmlns )
	{
		this.xmlns = xmlns;
	}

}
