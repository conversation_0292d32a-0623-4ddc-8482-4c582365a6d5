package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeLoanType;
import com.megabank.olp.common.persistence.pojo.code.CodeRateAdjustmentNotification;

/**
 * The CodeRateAdjustmentNotificationDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRateAdjustmentNotificationDAO extends BasePojoDAO<CodeRateAdjustmentNotification, String>
{
	public List<CodeRateAdjustmentNotification> getPojosOrderByDisplay()
	{
		NameValueBean condition = new NameValueBean( CodeRateAdjustmentNotification.DISABLED_CONSTANT, false );

		OrderBean[] orderBeans = new OrderBean[]{ new OrderBean( CodeLoanType.DISPLAY_ORDER_CONSTANT ) };

		return getPojosByPropertyOrderBy( condition, orderBeans );
	}

	@Override
	protected Class<CodeRateAdjustmentNotification> getPojoClass()
	{
		return CodeRateAdjustmentNotification.class;
	}
}
