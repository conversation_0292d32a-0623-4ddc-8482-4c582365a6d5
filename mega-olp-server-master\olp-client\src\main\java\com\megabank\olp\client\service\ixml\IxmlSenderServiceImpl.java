package com.megabank.olp.client.service.ixml;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.ixml.login.IxmlLoginClient;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginArgBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginResBean;
import com.megabank.olp.client.sender.ixml.querycert.IxmlQueryCertClient;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertArgBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertResBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.IxmlQueryVerifyResultClient;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultArgBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultResBean;
import com.megabank.olp.client.sender.ixml.revokecert.IxmlRevokeCertClient;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertArgBean;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertResBean;
import com.megabank.olp.client.sender.ixml.savecert.IxmlSaveCertClient;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertArgBean;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertResBean;

@Service
@Profile( { "dev", "sit", "stress", "uat", "prod" } )
public class IxmlSenderServiceImpl implements IxmlSenderService
{
	@Autowired
	private IxmlLoginClient ixmlLoginClient;

	@Autowired
	private IxmlQueryCertClient ixmlQueryCertClient;

	@Autowired
	private IxmlQueryVerifyResultClient ixmlQueryVerifyResultClient;

	@Autowired
	private IxmlRevokeCertClient ixmlRevokeCertClient;

	@Autowired
	private IxmlSaveCertClient ixmlSaveCertClient;

	@Override
	public IxmlLoginResBean login( IxmlLoginArgBean argBean )
	{
		return ixmlLoginClient.send( argBean );
	}

	@Override
	public IxmlQueryCertResBean queryCert( IxmlQueryCertArgBean argBean )
	{
		return ixmlQueryCertClient.send( argBean );
	}

	@Override
	public IxmlQueryVerifyResultResBean queryVerifyResult( IxmlQueryVerifyResultArgBean argBean )
	{
		return ixmlQueryVerifyResultClient.send( argBean );
	}

	@Override
	public IxmlRevokeCertResBean revokeCert( IxmlRevokeCertArgBean argBean )
	{
		return ixmlRevokeCertClient.send( argBean );
	}

	@Override
	public IxmlSaveCertResBean saveCert( IxmlSaveCertArgBean argBean )
	{
		return ixmlSaveCertClient.send( argBean );
	}
}
