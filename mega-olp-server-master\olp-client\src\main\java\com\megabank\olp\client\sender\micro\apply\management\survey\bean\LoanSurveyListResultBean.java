package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyListResultBean extends BaseBean
{
	@JsonProperty( "list" )
	private List<LoanSurveyBean> loanSurveyBeans;

	private Long totalCount;

	private Integer totalPage;

	public LoanSurveyListResultBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return loanSurveyBeans
	 */
	public List<LoanSurveyBean> getLoanSurveyBeans()
	{
		return loanSurveyBeans;
	}

	/**
	 *
	 * @return totalCount
	 */
	public Long getTotalCount()
	{
		return totalCount;
	}

	/**
	 *
	 * @return totalPage
	 */
	public Integer getTotalPage()
	{
		return totalPage;
	}

	/**
	 *
	 * @param loanSurveyBeans
	 */
	public void setLoanSurveyBeans( List<LoanSurveyBean> loanSurveyBeans )
	{
		this.loanSurveyBeans = loanSurveyBeans;
	}

	/**
	 *
	 * @param totalCount
	 */
	public void setTotalCount( Long totalCount )
	{
		this.totalCount = totalCount;
	}

	/**
	 *
	 * @param totalPage
	 */
	public void setTotalPage( Integer totalPage )
	{
		this.totalPage = totalPage;
	}

}
