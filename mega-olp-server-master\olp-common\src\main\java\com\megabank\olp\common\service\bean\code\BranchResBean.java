package com.megabank.olp.common.service.bean.code;

import com.megabank.olp.base.bean.BaseBean;

public class BranchResBean extends BaseBean
{
	private String code;

	private String name;

	private String address;

	public BranchResBean()
	{
		// default constructor
	}

	public String getAddress()
	{
		return address;
	}

	/**
	 *
	 * @return code
	 */
	public String getCode()
	{
		return code;
	}

	/**
	 *
	 * @return name
	 */
	public String getName()
	{
		return name;
	}

	public void setAddress( String address )
	{
		this.address = address;
	}

	/**
	 *
	 * @param code
	 */
	public void setCode( String code )
	{
		this.code = code;
	}

	/**
	 *
	 * @param name
	 */
	public void setName( String name )
	{
		this.name = name;
	}
}
