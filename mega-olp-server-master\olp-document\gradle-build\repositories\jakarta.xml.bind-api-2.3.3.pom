<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2018, 2020 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jakarta.xml.bind-api-parent</artifactId>
        <groupId>jakarta.xml.bind</groupId>
        <version>2.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jakarta.xml.bind-api</artifactId>
    <packaging>jar</packaging>
    <name>Jakarta XML Binding API</name>

    <properties>
        <config.dir>${project.basedir}/../etc/config</config.dir>
        <legal.doc.source>${project.basedir}/..</legal.doc.source>
    </properties>

    <dependencies>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
        </dependency>
    </dependencies>

    <build>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <configuration>
                        <rules>
                            <requireJavaVersion>
                                <version>[1.8,)</version>
                            </requireJavaVersion>
                            <requireMavenVersion>
                                <version>[3.0.3,)</version>
                            </requireMavenVersion>
                            <DependencyConvergence />
                        </rules>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <configuration>
                        <archive>
                            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <configuration>
                        <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                        <shortRevisionLength>7</shortRevisionLength>
                        <revisionOnScmFailure>false</revisionOnScmFailure>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <configuration>
                        <threshold>${spotbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            ${spotbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <configuration>
                        <source>1.8</source>
                        <release>8</release>
                        <additionalOptions>
                            <additionalOption>--frames</additionalOption>
                        </additionalOptions>
                        <sourceFileExcludes>
                            <sourceFileExclude>module-info.java</sourceFileExclude>
                        </sourceFileExcludes>
                        <nodeprecated>false</nodeprecated>
                        <notimestamp>true</notimestamp>
                        <use>false</use>
                        <author>true</author>
                        <version>true</version>
                        <description>Jakarta XML Binding API documentation</description>
                        <doctitle>Jakarta XML Binding API documentation</doctitle>
                        <windowtitle>Jakarta XML Binding API documentation</windowtitle>
                        <header><![CDATA[Jakarta XML Binding<br>v${project.version}]]>
                        </header>
                        <bottom>
                        <![CDATA[
Comments to : <a href="mailto:${release.spec.feedback}">${release.spec.feedback}</a>.<br>
Copyright &#169; 2019, 2020 Eclipse Foundation. All rights reserved.<br>
Use is subject to <a href="{@docRoot}/doc-files/speclicense.html" target="_top">license terms</a>.]]>
                        </bottom>
                        <detectJavaApiLink>false</detectJavaApiLink>
                        <docfilessubdirs>true</docfilessubdirs>
                        <groups>
                            <group>
                                <title>Jakarta XML Binding API Packages</title>
                                <packages>javax.xml.bind*</packages>
                            </group>
                        </groups>
                        <tags>
                            <tag>
                                <name>apiNote</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>API Note:</head>
                            </tag>
                            <tag>
                                <name>implSpec</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>Implementation Requirements:</head>
                            </tag>
                            <tag>
                                <name>implNote</name>
                                <!-- todo tag for all places -->
                                <placement>a</placement>
                                <head>Implementation Note:</head>
                            </tag>
                        </tags>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-legal-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${legal.doc.source}</directory>
                                    <includes>
                                        <include>NOTICE.md</include>
                                        <include>LICENSE.md</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-mr-resource</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/src/main/mr-jar</directory>
                                    <targetPath>META-INF/versions/9</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                </configuration>
                <executions>
                    <execution>
                        <id>default-compile-mr</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>9</release>
                            <compileSourceRoots>
                                <compileSourceRoot>${project.basedir}/src/main/mr-jar</compileSourceRoot>
                            </compileSourceRoots>
                            <outputDirectory>${project.build.outputDirectory}/META-INF/versions/9</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>8</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    <configuration>
                        <archive>
                            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        </archive>
                        <instructions>
                            <_removeheaders>Bnd-LastModified,Built-By,Include-Resource,Private-Package</_removeheaders>
                            <Bundle-Version>${project.version}</Bundle-Version>  <!-- 2.2.99.bnull -->
                            <Bundle-Description>
                                Jakarta XML Binding API ${spec.version} Design Specification
                            </Bundle-Description>
                            <Extension-Name>${extension.name}</Extension-Name>
                            <Implementation-Version>${spec.version}.${impl.version}</Implementation-Version>
                            <Specification-Version>${spec.version}</Specification-Version>
                            <Export-Package>${api.package}.*; version=${spec.version}.${impl.version}</Export-Package>
                            <Import-Package>
                                !org.glassfish.hk2.osgiresourcelocator,
                                javax.activation;version=!,
                                *
                            </Import-Package>
                            <Bundle-SymbolicName>${extension.name}-api</Bundle-SymbolicName>
                            <DynamicImport-Package>org.glassfish.hk2.osgiresourcelocator</DynamicImport-Package>
                            <Specification-Vendor>${vendor.name}</Specification-Vendor>
                            <Implementation-Build-Id>${scmBranch}-${buildNumber}</Implementation-Build-Id>
                            <Multi-Release>true</Multi-Release>
                        </instructions>
                    </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
