package com.megabank.olp.client.sender.micro.apply.mydata;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.mydata.bean.MyDataNotifiedArgBean;

@Component
public class MyDataNotifiedClient extends BaseApplyClient<MyDataNotifiedArgBean, String>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/mydata/notifyServlet";
	}
}
