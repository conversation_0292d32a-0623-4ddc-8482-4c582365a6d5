/**
 *
 */
package com.megabank.olp.client.sender.sso.custinfo.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoCustInfoRespDataBean extends BaseBean
{
	@JsonProperty( "USER_IXD" )
	private String idNo;

	@JsonProperty( "USER_IDDUP" )
	private String idDup;

	@JsonProperty( "BIRTH_DATE" )
	private String birthDate;

	@JsonProperty( "NAME" )
	private String name;

	@JsonProperty( "BUSINESS_CODE" )
	private String businessCode;

	@JsonProperty( "MPHONE_NC" )
	private String mobileCountryCode;

	@JsonProperty( "MPHONE_NO" )
	private String mobileNumber;

	@JsonProperty( "EMAIL" )
	private String email;

	@JsonProperty( "AGEN_BR" )
	private String age;

	@JsonProperty( "ASSIST_FLAG" )
	private String assistFlag;

	public SsoCustInfoRespDataBean()
	{}

	public String getAge()
	{
		return age;
	}

	public String getAssistFlag()
	{
		return assistFlag;
	}

	public String getBirthDate()
	{
		return birthDate;
	}

	public String getBusinessCode()
	{
		return businessCode;
	}

	public String getEmail()
	{
		return email;
	}

	public String getIdDup()
	{
		return idDup;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileCountryCode()
	{
		return mobileCountryCode;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public void setAge( String age )
	{
		this.age = age;
	}

	public void setAssistFlag( String assistFlag )
	{
		this.assistFlag = assistFlag;
	}

	public void setBirthDate( String birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setBusinessCode( String businessCode )
	{
		this.businessCode = businessCode;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setIdDup( String idDup )
	{
		this.idDup = idDup;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileCountryCode( String mobileCountryCode )
	{
		this.mobileCountryCode = mobileCountryCode;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

}
