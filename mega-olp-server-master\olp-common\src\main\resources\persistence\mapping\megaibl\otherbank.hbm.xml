<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<sql-query name="bank.getBankList">
	<return-scalar column="bankName" type="string"/>
	<return-scalar column="bankCode" type="string"/>
		SELECT ABBR_NAME AS bankName, BANK_CODE AS bankCode FROM MEGAIB.BANK WHERE DCROSVERIFY_FLAG = '1' ORDER BY BANK_CODE
	</sql-query>
</hibernate-mapping>
