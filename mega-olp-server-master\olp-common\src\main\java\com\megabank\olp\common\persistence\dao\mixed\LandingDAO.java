package com.megabank.olp.common.persistence.dao.mixed;

import java.util.Date;
import java.util.List;

import org.hibernate.query.NativeQuery;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BaseDAO;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Repository
public class LandingDAO extends BaseDAO
{
	private static final String CURRENT_TIME_CONSTANT = "currentTime";

	private static final String TEMPLATE_NAME_CONSTANT = "templateName";

	private static final String TEMPLATE_TYPE_CONSTANT = "templateType";

	public List<Long> getInUseSolution( String templateType, String templateName )
	{
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "landing.getInUseSolution" );
		nativeQuery.setParameter( CURRENT_TIME_CONSTANT, new Date(), Date.class );
		nativeQuery.setParameter( TEMPLATE_TYPE_CONSTANT, templateType, String.class );
		nativeQuery.setParameter( TEMPLATE_NAME_CONSTANT, templateName + "%", String.class );

		return nativeQuery.getResultList();
	}

}
