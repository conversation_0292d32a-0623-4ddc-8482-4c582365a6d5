package com.megabank.olp.client.sender.micro.version;

import java.io.UnsupportedEncodingException;
import java.net.URI;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.CommonErrorEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.micro.BaseMicroServicesClient;
import com.megabank.olp.client.sender.micro.version.bean.VersionArgBean;
import com.megabank.olp.client.sender.micro.version.bean.VersionResultBean;
import com.megabank.olp.client.utility.enums.MicroServiceEnum;

@Component
public class VersionClient extends BaseMicroServicesClient<VersionArgBean, VersionResultBean>
{
	private String getUrlMicroService( String microServiceName )
	{
		if( MicroServiceEnum.APPLY.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroApply();

		if( MicroServiceEnum.COMMON.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroCommon();

		if( MicroServiceEnum.IDENTTIY.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroIdentity();

		if( MicroServiceEnum.MODAL.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroModal();

		if( MicroServiceEnum.OTHER_DATABASE.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroOtherDatabase();

		if( MicroServiceEnum.USER.getContext().equals( microServiceName ) )
			return propertyBean.getApiUrlMicroUser();

		throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH_ARGUMENTS,
									  new String[]{ "microServiceUrl", "microServiceName" + "=" + microServiceName } );
	}

	@Override
	protected URI getApiURI( VersionArgBean argBean ) throws UnsupportedEncodingException
	{
		return URI.create( getUrlMicroService( argBean.getMicroServiceName() ) + getSuffixUrl() );
	}

	@Override
	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.GET;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/open/version";
	}

	@Override
	protected String getSystemName()
	{
		return "version";
	}

}
