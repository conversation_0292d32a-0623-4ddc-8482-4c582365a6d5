<?xml version="1.0" encoding="UTF-8"?>
<!-- vi:ts=2:sw=2:expandtab: -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <!-- Model Version -->
    <modelVersion>4.0.0</modelVersion>

    <!-- Artifact Configuration -->
    <groupId>org.jboss.shrinkwrap.descriptors</groupId>
    <artifactId>shrinkwrap-descriptors-bom</artifactId>
    <version>2.0.0-alpha-8</version>
    <packaging>pom</packaging>
    <name>ShrinkWrap Descriptors Bill of Materials</name>
    <description>Centralized dependencyManagement for the ShrinkWrap Descriptors Project</description>
    <url>http://www.jboss.org/shrinkwrap</url>

    <!-- Licenses -->
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>jboss.org</id>
            <name>JBoss.org Community</name>
            <organization>JBoss.org</organization>
            <organizationUrl>http://www.jboss.org</organizationUrl>
        </developer>
    </developers>

    <!-- SCM -->
    <scm>
        <connection>scm:git:git://github.com/shrinkwrap/descriptors.git</connection>
        <developerConnection>scm:git:**************:shrinkwrap/descriptors.git</developerConnection>
        <url>https://github.com/shrinkwrap/descriptors</url>
      <tag>2.0.0-alpha-8</tag>
  </scm>

    <!-- Properties -->
    <properties>

        <jboss.releases.repo.url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/
        </jboss.releases.repo.url>
        <jboss.snapshots.repo.url>https://repository.jboss.org/nexus/content/repositories/snapshots/
        </jboss.snapshots.repo.url>

    </properties>

    <!-- Dependency Management -->
    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-api-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-api-javaee</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-api-jboss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-gen</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-impl-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-impl-javaee</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-impl-jboss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-spi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jboss.shrinkwrap.descriptors</groupId>
                <artifactId>shrinkwrap-descriptors-depchain</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.1</version>
                    <configuration>
                        <pushChanges>false</pushChanges>
                        <localCheckout>true</localCheckout>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>jboss-releases-repository</id>
            <name>JBoss Releases Repository</name>
            <url>${jboss.releases.repo.url}</url>
        </repository>
        <snapshotRepository>
            <id>jboss-snapshots-repository</id>
            <name>JBoss Snapshots Repository</name>
            <url>${jboss.snapshots.repo.url}</url>
        </snapshotRepository>
    </distributionManagement>

</project>
