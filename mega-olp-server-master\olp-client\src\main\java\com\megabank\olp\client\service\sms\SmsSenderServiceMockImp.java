package com.megabank.olp.client.service.sms;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.sms.SmsSenderClient;
import com.megabank.olp.system.service.SystemService;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev", "sit", "stress" } )
public class SmsSenderServiceMockImp implements SmsSenderService
{
	@Autowired
	private SmsSenderClient smsSenderClient;

	@Autowired
	private SystemService systemService;

	@Override
	public void send( String contractNo, String branchBankCode, String mobileNumber, String idNo, String message )
	{
		SmsSenderThread thread = new SmsSenderThread( smsSenderClient, systemService );
		thread.setContractNo( contractNo );
		thread.setBranchBankCode( branchBankCode );
		thread.setMobileNumber( mobileNumber );
		thread.setIdNo( idNo );
		thread.setMessage( message );

		thread.start();
	}

}
