<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-bom</artifactId>
  <packaging>pom</packaging>
  <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-bom:current} -->
  <name>Google Cloud Java BOM</name>
  <url>https://github.com/GoogleCloudPlatform/google-cloud-java/tree/master/google-cloud-bom</url>
  <description>
    BOM for google-cloud-java
  </description>
  <developers>
    <developer>
      <id>garrettjonesgoogle</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>pongad</id>
      <name><PERSON> Dar<PERSON>nanda</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>shinfan</id>
      <name>Shin Fan</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michaelbausor</id>
      <name>Micheal Bausor</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>vam-google</id>
      <name>Vadym Matsishevskyi</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>tswast</id>
      <name>Tim Swast</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>neozwu</id>
      <name>Neo Wu</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>lesv</id>
      <name>Les Vogel</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>schmidt_sebastian</id>
      <name>Sebastian Schmidt</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>andreamlin</id>
      <name>Andrea Lin</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>hzyi-google</id>
      <name>Hanzhen Yi</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jart</id>
      <name>Justine Tunney</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jean-philippe-martin</id>
      <name>Jean-Philippe Martin</name>
      <email><EMAIL></email>
      <organization>Verily</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google</name>
  </organization>
  <scm>
    <connection>scm:git:**************:GoogleCloudPlatform/google-cloud.git</connection>
    <developerConnection>scm:git:**************:GoogleCloudPlatform/google-cloud.git</developerConnection>
    <url>https://github.com/GoogleCloudPlatform/google-cloud-java</url>
    <tag>HEAD</tag>
  </scm>
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <site>
      <id>github-pages-site</id>
      <name>Deployment through GitHub's site deployment plugin</name>
      <url>site/google-cloud-bom</url>
    </site>
  </distributionManagement>
  <licenses>
    <license>
      <name>Google Cloud Software License</name>
      <url>https://raw.githubusercontent.com/GoogleCloudPlatform/google-cloud-java/master/LICENSE</url>
    </license>
  </licenses>
  <dependencyManagement>
    <dependencies>
      <!-- Common dependencies -->
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>api-common</artifactId>
        <version>1.7.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax</artifactId>
        <version>1.32.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax</artifactId>
        <version>1.32.0</version>
        <classifier>testlib</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-grpc</artifactId>
        <version>1.32.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-grpc</artifactId>
        <version>1.32.0</version>
        <classifier>testlib</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-httpjson</artifactId>
        <version>0.49.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-httpjson</artifactId>
        <version>0.49.0</version>
        <classifier>testlib</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-common-protos</artifactId>
        <version>1.12.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-common-protos</artifactId>
        <version>1.12.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-iam-v1</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-iam-v1</artifactId>
        <version>0.12.0</version>
      </dependency>

      <!-- API-specific dependencies -->
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-asset</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-asset:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-asset-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-automl</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-automl:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-automl-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-automl-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-automl-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-automl-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-bigtable:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-bigtable:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigtable-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-bigtable-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigtable-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-bigtable-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable-admin</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-bigtable-admin:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable-admin</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-bigtable-admin:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigtable-admin-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-bigtable-admin-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigtable-admin-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-bigtable-admin-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquery</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-bigquery:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquery</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-bigquery:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerydatatransfer</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-bigquerydatatransfer:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerydatatransfer</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-bigquerydatatransfer:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-bigquerydatatransfer-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-bigquerydatatransfer-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-bigquerydatatransfer-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-bigquerydatatransfer-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-compute</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-compute:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-compute</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-compute:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-container</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-container:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-container</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-container:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-container-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-container-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-container-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-container-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-containeranalysis</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-containeranalysis:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-containeranalysis</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-containeranalysis:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-containeranalysis-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-containeranalysis-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-containeranalysis-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-containeranalysis-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-contrib</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-contrib:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-contrib</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-contrib:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-nio</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-contrib:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-nio</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-contrib:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-grpc</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core-grpc:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-grpc</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core-grpc:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-http</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core-http:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-http</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-core-http:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dataproc:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dataproc:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dataproc-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-dataproc-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dataproc-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-dataproc-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dataproc-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-dataproc-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dataproc-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-dataproc-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastore</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-datastore:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastore</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-datastore:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-datastore-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-datastore-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dlp</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-dlp:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dlp</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-dlp:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dlp-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-dlp-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dlp-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-dlp-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dialogflow</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dialogflow:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dialogflow</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dialogflow:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dialogflow-v2beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-dialogflow-v2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dialogflow-v2beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-dialogflow-v2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dialogflow-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-dialogflow-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dialogflow-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-dialogflow-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dns</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dns:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dns</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-dns:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-errorreporting</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-errorreporting:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-errorreporting</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-errorreporting:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-error-reporting-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-error-reporting-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-error-reporting-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-error-reporting-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-firestore</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-firestore:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-firestore</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-firestore:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-firestore-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-firestore-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-firestore-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-firestore-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-kms</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-kms:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-kms</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-kms:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-kms-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-kms-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-kms-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-kms-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-language</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-language:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-language</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-language:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-language-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:proto-google-cloud-language-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-language-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:grpc-google-cloud-language-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-language-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-language-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-language-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-language-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-logging:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-logging:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-logging-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-logging-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-logging-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-logging-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging-logback</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-logging-logback:current} -->
      </dependency>

      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-monitoring:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-monitoring:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-monitoring-v3</artifactId>
        <version>1.27.0</version><!-- {x-version-update:proto-google-cloud-monitoring-v3:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-monitoring-v3</artifactId>
        <version>1.27.0</version><!-- {x-version-update:grpc-google-cloud-monitoring-v3:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-login</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-os-login:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-login</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-os-login:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-os-login-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-os-login-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-os-login-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-os-login-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-pubsub:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-pubsub:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-pubsub-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:proto-google-cloud-pubsub-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-pubsub-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:grpc-google-cloud-pubsub-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-redis:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-redis:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-redis-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-redis-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-redis-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-redis-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-resourcemanager</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-resourcemanager:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-resourcemanager</artifactId>
        <version>0.63.0-alpha</version><!-- {x-version-update:google-cloud-resourcemanager:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-spanner:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-spanner:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-spanner-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-spanner-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-database-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-spanner-admin-database-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-database-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-spanner-admin-database-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-spanner-admin-instance-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-spanner-admin-instance-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-speech</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-speech:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-speech</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-speech:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-speech-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-speech-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-speech-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-speech-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-speech-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-speech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-speech-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-speech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-storage:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-storage:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-tasks</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-tasks:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-tasks</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-tasks:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-tasks-v2beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-tasks-v2beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-tasks-v2beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-tasks-v2beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-tasks-v2beta3</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-tasks-v2beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-tasks-v2beta3</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-tasks-v2beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-texttospeech</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-texttospeech:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-texttospeech</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-texttospeech:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-texttospeech-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-texttospeech-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-texttospeech-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-texttospeech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-texttospeech-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-texttospeech-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-trace</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-trace:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-trace</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-trace:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-trace-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-trace-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-trace-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-trace-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-trace-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-trace-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-trace-v2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-trace-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-translate</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-translate:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-translate</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-translate:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vision</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-vision:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vision</artifactId>
        <version>1.45.0</version><!-- {x-version-update:google-cloud-vision:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:proto-google-cloud-vision-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p2beta1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p2beta1</artifactId>
        <version>1.27.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p3beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p3beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-intelligence</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-video-intelligence:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-intelligence</artifactId>
        <version>0.63.0-beta</version><!-- {x-version-update:google-cloud-video-intelligence:current} -->
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-video-intelligence-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-video-intelligence-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-video-intelligence-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1beta2</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-video-intelligence-v1beta2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-video-intelligence-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-video-intelligence-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-video-intelligence-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1p1beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-video-intelligence-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-video-intelligence-v1p2beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-video-intelligence-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-video-intelligence-v1p2beta1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-video-intelligence-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-iot-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-iot-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-iot-v1</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-iot-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-websecurityscanner-v1alpha</artifactId>
        <version>0.28.0</version><!-- {x-version-update:proto-google-cloud-websecurityscanner-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-websecurityscanner-v1alpha</artifactId>
        <version>0.28.0</version><!-- {x-version-update:grpc-google-cloud-websecurityscanner-v1alpha:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>1.6</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>deploy</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.6</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>false</autoReleaseAfterClose>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.4</version>
        <configuration>
          <skipDeploy>true</skipDeploy>
          <reportPlugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-project-info-reports-plugin</artifactId>
              <version>2.8.1</version>
              <reportSets>
                <reportSet>
                  <reports>
                    <report>index</report>
                    <report>dependency-info</report>
                    <report>project-team</report>
                    <report>cim</report>
                    <report>issue-tracking</report>
                    <report>license</report>
                    <report>scm</report>
                    <report>dependency-management</report>
                    <report>distribution-management</report>
                    <report>summary</report>
                    <report>modules</report>
                  </reports>
                </reportSet>
              </reportSets>
              <configuration>
                <aggregate>true</aggregate>
                <quiet>true</quiet>
                <dependencyDetailsEnabled>true</dependencyDetailsEnabled>
                <dependencyLocationsEnabled>true</dependencyLocationsEnabled>
                <artifactId>google-cloud-bom</artifactId>
                <packaging>jar</packaging>
              </configuration>
            </plugin>
          </reportPlugins>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
