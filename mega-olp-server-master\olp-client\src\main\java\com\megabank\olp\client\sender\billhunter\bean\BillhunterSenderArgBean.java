package com.megabank.olp.client.sender.billhunter.bean;

import java.util.List;

import com.megabank.olp.base.bean.BaseBean;

public class BillhunterSenderArgBean extends BaseBean
{
	/**
	 * mail主旨
	 */
	private String subject;

	/**
	 * 帳單類別
	 */
	private String category;

	/**
	 * 內文是否為純文字
	 */
	private boolean isTextFormat;

	/**
	 * 內文
	 */
	private String message;

	/**
	 * 附件資訊
	 */
	private List<AttachmentFileBean> attachmentFileBeans;

	/**
	 * 收件人資訊
	 */
	private List<ToMailInfoBean> toMailInfoBeans;

	/**
	 * 案件編號
	 */
	private String caseNo;

	public BillhunterSenderArgBean()
	{

	}

	public List<AttachmentFileBean> getAttachmentFileBeans()
	{
		return attachmentFileBeans;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getCategory()
	{
		return category;
	}

	public String getMessage()
	{
		return message;
	}

	public String getSubject()
	{
		return subject;
	}

	public List<ToMailInfoBean> getToMailInfoBeans()
	{
		return toMailInfoBeans;
	}

	public boolean isTextFormat()
	{
		return isTextFormat;
	}

	public void setAttachmentFileBeans( List<AttachmentFileBean> attachmentFileBeans )
	{
		this.attachmentFileBeans = attachmentFileBeans;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setCategory( String category )
	{
		this.category = category;
	}

	public void setMessage( String message )
	{
		this.message = message;
	}

	public void setSubject( String subject )
	{
		this.subject = subject;
	}

	public void setTextFormat( boolean isTextFormat )
	{
		this.isTextFormat = isTextFormat;
	}

	public void setToMailInfoBeans( List<ToMailInfoBean> toMailInfoBeans )
	{
		this.toMailInfoBeans = toMailInfoBeans;
	}

}
