package com.megabank.olp.client.service.billhunter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.billhunter.BillhunterSenderClient;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderArgBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "sit", "stress", "uat", "prod" } )
public class BillhunterSenderServiceImp implements BillhunterSenderService
{

	@Autowired
	private BillhunterSenderClient billhunterSenderClient;

	@Override
	public BillhunterSenderResultBean send( BillhunterSenderArgBean argBean )
	{
		return billhunterSenderClient.send( argBean );
	}

}
