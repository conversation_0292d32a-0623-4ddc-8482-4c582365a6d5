package com.megabank.olp.client.sender.micro.apply.management.house;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HousePricingArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.housepricing.HousePricingReassignBranchBankResultBean;

@Component
public class HousePricingReassignBranchBankClient extends BaseApplyClient<HousePricingArgBean, List<HousePricingReassignBranchBankResultBean>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return HousePricingReassignBranchBankResultBean.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/management/housepricing/getReassignBranchBank";
	}
}
