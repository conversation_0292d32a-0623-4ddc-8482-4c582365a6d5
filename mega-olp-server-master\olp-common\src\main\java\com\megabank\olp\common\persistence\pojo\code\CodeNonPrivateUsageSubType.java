package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeNonPrivateUsageSubType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_non_private_usage_subtype" )
public class CodeNonPrivateUsageSubType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_non_private_usage_subtype";

	public static final String NON_PRIVATE_USAGE_SUBTYPE_CONSTANT = "nonPrivateUsageSubType";

	public static final String NAME_CONSTANT = "name";

	private String nonPrivateUsageSubType;

	private String name;

	public CodeNonPrivateUsageSubType()
	{}

	public CodeNonPrivateUsageSubType( String nonPrivateUsageSubType )
	{
		this.nonPrivateUsageSubType = nonPrivateUsageSubType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "non_private_usage_subtype", unique = true, nullable = false, length = 20 )
	public String getNonPrivateUsageSubType()
	{
		return nonPrivateUsageSubType;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNonPrivateUsageSubType( String nonPrivateUsageSubType )
	{
		this.nonPrivateUsageSubType = nonPrivateUsageSubType;
	}
}