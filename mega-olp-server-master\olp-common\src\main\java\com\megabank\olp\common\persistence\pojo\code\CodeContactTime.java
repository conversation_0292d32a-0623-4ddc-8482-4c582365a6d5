package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeContactTime is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_contact_time" )
public class CodeContactTime extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_contact_time";

	public static final String CONTACT_TIME_CODE_CONSTANT = "contactTimeCode";

	public static final String NAME_CONSTANT = "name";

	private String contactTimeCode;

	private String name;

	public CodeContactTime()
	{}

	public CodeContactTime( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	@Id
	@Column( name = "contact_time_code", unique = true, nullable = false, length = 20 )
	public String getContactTimeCode()
	{
		return contactTimeCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setContactTimeCode( String contactTimeCode )
	{
		this.contactTimeCode = contactTimeCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}