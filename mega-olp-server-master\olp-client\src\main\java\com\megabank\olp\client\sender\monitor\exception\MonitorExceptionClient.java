package com.megabank.olp.client.sender.monitor.exception;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.threadlocal.ScheduleSessionInfoThreadLocal;
import com.megabank.olp.client.sender.monitor.BaseMonitorClient;
import com.megabank.olp.client.sender.monitor.bean.EventMsgBean;
import com.megabank.olp.client.sender.monitor.bean.EventMsgDetailBean;
import com.megabank.olp.client.sender.monitor.bean.MsgObjBean;
import com.megabank.olp.client.sender.monitor.bean.SendBean;
import com.megabank.olp.client.sender.monitor.bean.SoapBodyBean;
import com.megabank.olp.client.sender.monitor.exception.bean.MonitorExceptionArgBean;
import com.megabank.olp.client.sender.monitor.exception.bean.MonitorExceptionReqBean;

@Component
public class MonitorExceptionClient extends BaseMonitorClient<MonitorExceptionArgBean, MonitorExceptionReqBean>
{
	@Autowired
	private ScheduleSessionInfoThreadLocal scheduleSessionInfoThreadLocal;

	private EventMsgDetailBean getDetailBean( String index, String content )
	{
		EventMsgDetailBean detailBean = new EventMsgDetailBean();
		detailBean.setParameters( index );
		detailBean.setContent( StringUtils.isBlank( content ) ? "" : content );
		detailBean.setContentLength( StringUtils.isBlank( content ) ? 0 : content.length() );

		return detailBean;
	}

	private List<EventMsgDetailBean> getEventMsgDetailBeans( MonitorExceptionArgBean argBean )
	{
		List<EventMsgDetailBean> eventMsgDetailBeans = new ArrayList<>();

		String idNo = scheduleSessionInfoThreadLocal.get() == null ? "" : scheduleSessionInfoThreadLocal.get().getIdNo();
		String custInfo = StringUtils.isBlank( idNo ) ? "" : "cust=[" + idNo + "]";
		String errorLogId = argBean.getErrorLogId() == null ? "" : argBean.getErrorLogId().toString();
		String location = openshiftLocation;

		eventMsgDetailBeans.add( getDetailBean( "1", custInfo ) );
		eventMsgDetailBeans.add( getDetailBean( "2", errorLogId ) );
		eventMsgDetailBeans.add( getDetailBean( "3", location ) );
		eventMsgDetailBeans.add( getDetailBean( "4", argBean.getOccurService() ) );
		eventMsgDetailBeans.add( getDetailBean( "5", argBean.getErrorMsg() ) );

		return eventMsgDetailBeans;
	}

	@Override
	protected String getSimulatorCode( MonitorExceptionArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/WEBSERVICE/SERVICE1.asmx";
	}

	@Override
	protected MonitorExceptionReqBean transArg2Req( MonitorExceptionArgBean argBean )
	{
		String occurDate = new SimpleDateFormat( "yyyyMMddHHmmssSS" ).format( new Date() );
		occurDate = occurDate.length() > 16 ? occurDate.substring( 0, 16 ) : StringUtils.rightPad( occurDate, 16, "0" );

		EventMsgBean eventMsgBean = new EventMsgBean();
		eventMsgBean.setMsgCode( "000005" );
		eventMsgBean.setHostName( "PLOAN" );
		eventMsgBean.setPgcName( "BACKEND" );
		eventMsgBean.setCtiCode( "PLOAN" );
		eventMsgBean.setPlatform( "LINUX" );
		eventMsgBean.setEventSource( "A" );
		eventMsgBean.setDataType( "E" );
		eventMsgBean.setIsOverwrite( "Y" );
		eventMsgBean.setStatus( "00" );
		eventMsgBean.setEventMsgDetailBeans( getEventMsgDetailBeans( argBean ) );
		eventMsgBean.setParamCount( eventMsgBean.getEventMsgDetailBeans().size() );
		eventMsgBean.setOccurDate( occurDate );

		MsgObjBean msgObjBean = new MsgObjBean();
		msgObjBean.setEventMsgBean( eventMsgBean );

		SendBean sendBean = new SendBean();
		sendBean.setMsgObjBean( msgObjBean );

		SoapBodyBean soapBodyBean = new SoapBodyBean();
		soapBodyBean.setSendBean( sendBean );

		MonitorExceptionReqBean reqBean = new MonitorExceptionReqBean();
		reqBean.setSoapBodyBean( soapBodyBean );

		return reqBean;
	}

}
