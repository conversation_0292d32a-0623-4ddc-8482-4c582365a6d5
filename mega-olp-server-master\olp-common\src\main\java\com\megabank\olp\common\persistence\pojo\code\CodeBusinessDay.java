package com.megabank.olp.common.persistence.pojo.code;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeBusinessDay is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_business_day" )
public class CodeBusinessDay extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_business_day";

	public static final String BUSINESS_DAY_CODE_CONSTANT = "businessDayCode";

	private Date businessDayCode;

	public CodeBusinessDay()
	{}

	public CodeBusinessDay( Date businessDayCode )
	{
		this.businessDayCode = businessDayCode;
	}

	@Id
	@Temporal( TemporalType.TIMESTAMP )
	@Column( name = "business_day_code", unique = true, nullable = false, length = 10 )
	public Date getBusinessDayCode()
	{
		return businessDayCode;
	}

	public void setBusinessDayCode( Date businessDayCode )
	{
		this.businessDayCode = businessDayCode;
	}
}