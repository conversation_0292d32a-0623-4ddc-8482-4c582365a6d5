package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeCity is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_city" )
public class CodeCity extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_city";

	public static final String CITY_CODE_CONSTANT = "cityCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String CODE_TOWNS_CONSTANT = "codeTowns";

	private String cityCode;

	private String name;

	private int displayOrder;

	private transient Set<CodeTown> codeTowns = new HashSet<>( 0 );

	public CodeCity()
	{}

	public CodeCity( String cityCode )
	{
		this.cityCode = cityCode;
	}

	public CodeCity( String cityCode, String name, int displayOrder )
	{
		this.cityCode = cityCode;
		this.name = name;
		this.displayOrder = displayOrder;
	}

	@Id
	@Column( name = "city_code", unique = true, nullable = false, length = 20 )
	public String getCityCode()
	{
		return cityCode;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeCity" )
	public Set<CodeTown> getCodeTowns()
	{
		return codeTowns;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setCityCode( String cityCode )
	{
		this.cityCode = cityCode;
	}

	public void setCodeTowns( Set<CodeTown> codeTowns )
	{
		this.codeTowns = codeTowns;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}