package com.megabank.olp.client.sender.billhunter;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.BaseXmlClient;
import com.megabank.olp.client.sender.billhunter.bean.AttachHeaderBean;
import com.megabank.olp.client.sender.billhunter.bean.AttachmentBean;
import com.megabank.olp.client.sender.billhunter.bean.AttachmentFileBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderArgBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderReqBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderResBean;
import com.megabank.olp.client.sender.billhunter.bean.BillhunterSenderResultBean;
import com.megabank.olp.client.sender.billhunter.bean.ContactInfoBean;
import com.megabank.olp.client.sender.billhunter.bean.FromBean;
import com.megabank.olp.client.sender.billhunter.bean.IsConvertToPDFBean;
import com.megabank.olp.client.sender.billhunter.bean.MessageBean;
import com.megabank.olp.client.sender.billhunter.bean.ReqInfoBean;
import com.megabank.olp.client.sender.billhunter.bean.ToBean;
import com.megabank.olp.client.sender.billhunter.bean.ToMailInfoBean;
import com.megabank.olp.client.sender.billhunter.bean.UserBean;

@Component
public class BillhunterSenderClient
			extends BaseXmlClient<BillhunterSenderArgBean, BillhunterSenderReqBean, BillhunterSenderResBean, BillhunterSenderResultBean>
{
	private List<AttachmentBean> getAttachmentBeans( List<AttachmentFileBean> attachmentFileBeans )
	{
		List<AttachmentBean> attachmentBeans = new ArrayList<>();

		for( AttachmentFileBean attachmentFileBean : attachmentFileBeans )
		{
			AttachHeaderBean attachHeaderBean = new AttachHeaderBean();
			attachHeaderBean.setIsConvertToPDFBean( new IsConvertToPDFBean() );
			attachHeaderBean.setAttachmentFileName( attachmentFileBean.getFileName() );

			AttachmentBean attachmentBean = new AttachmentBean();
			attachmentBean.setAttachHeaderBean( attachHeaderBean );
			attachmentBean.setAttachBody( attachmentFileBean.getFileContent() );

			attachmentBeans.add( attachmentBean );
		}

		return attachmentBeans;
	}

	private List<UserBean> getUserBeans( List<ToMailInfoBean> toMailInfoBeans )
	{
		List<UserBean> userBeans = new ArrayList<>();

		for( ToMailInfoBean toMailInfoBean : toMailInfoBeans )
		{
			UserBean userBean = new UserBean();
			userBean.setName( toMailInfoBean.getToUserName() );
			userBean.setId( toMailInfoBean.getToUserId() );
			userBean.setNotifyInfo( toMailInfoBean.getToMail() );

			userBeans.add( userBean );
		}

		return userBeans;
	}

	@Override
	protected URI getApiURI( BillhunterSenderArgBean argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlThirdpartyBillhunter() );
	}

	@Override
	protected String getLogForRequestBody( String requestBody )
	{
		return StringUtils.removeAll( requestBody, "<AttachBody>.*</AttachBody>" );
	}

	@Override
	protected String getSimulatorCode( BillhunterSenderArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSystemName()
	{
		return "mail";
	}

	@Override
	protected BillhunterSenderReqBean transArg2Req( BillhunterSenderArgBean argBean )
	{
		String notificationId = "PLOAN" + argBean.getCaseNo() + new SimpleDateFormat( "yyyyMMddHHmmss" ).format( new Date() );

		FromBean fromBean = new FromBean();

		ToBean toBean = new ToBean();
		toBean.setUserBeans( getUserBeans( argBean.getToMailInfoBeans() ) );

		ContactInfoBean contactInfoBean = new ContactInfoBean();
		contactInfoBean.setFromBean( fromBean );
		contactInfoBean.setToBean( toBean );

		MessageBean messageBean = new MessageBean();
		messageBean.setType( argBean.isTextFormat() ? "TXT" : "HTML" );
		messageBean.setValue( addCDATAIfNeed( argBean.getMessage() ) );

		ReqInfoBean reqInfoBean = new ReqInfoBean();
		reqInfoBean.setContactInfoBean( contactInfoBean );
		reqInfoBean.setSubject( argBean.getSubject() );
		reqInfoBean.setCategory( argBean.getCategory() );
		reqInfoBean.setMessageBean( messageBean );
		reqInfoBean.setNotificationId( notificationId );

		if( argBean.getAttachmentFileBeans() != null )
			reqInfoBean.setAttachmentBeans( getAttachmentBeans( argBean.getAttachmentFileBeans() ) );

		BillhunterSenderReqBean reqBean = new BillhunterSenderReqBean();
		reqBean.setReqInfoBean( reqInfoBean );

		return reqBean;
	}

	@Override
	protected BillhunterSenderResultBean transRes2Result( BillhunterSenderResBean resBean, HttpHeaders httpHeaders )
	{
		BillhunterSenderResultBean resultBean = new BillhunterSenderResultBean();
		resultBean.setSuccess( "0".equals( resBean.getResInfoBean().getStatusCode() ) );

		return resultBean;
	}

}
