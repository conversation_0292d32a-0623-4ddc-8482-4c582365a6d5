package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeResidenceStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_residence_status" )
public class CodeResidenceStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_residence_status";

	public static final String RESIDENCE_STATUS_CODE_CONSTANT = "residenceStatusCode";

	public static final String NAME_CONSTANT = "name";

	private String residenceStatusCode;

	private String name;

	public CodeResidenceStatus()
	{}

	public CodeResidenceStatus( String residenceStatusCode )
	{
		this.residenceStatusCode = residenceStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "residence_status_code", unique = true, nullable = false, length = 20 )
	public String getResidenceStatusCode()
	{
		return residenceStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setResidenceStatusCode( String residenceStatusCode )
	{
		this.residenceStatusCode = residenceStatusCode;
	}
}