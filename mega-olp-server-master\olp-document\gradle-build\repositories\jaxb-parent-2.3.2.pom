<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-bom-ext</artifactId>
        <version>2.3.2</version>
        <relativePath>boms/bom-ext/pom.xml</relativePath>
    </parent>

    <groupId>com.sun.xml.bind.mvn</groupId>
    <artifactId>jaxb-parent</artifactId>
    <version>2.3.2</version>
    <packaging>pom</packaging>
    <name>JAXB Reference Implementation</name>
    <description>
        Open source Reference Implementation of JSR-222: Java Architecture for XML Binding
    </description>

    <url>https://javaee.github.io/jaxb-v2/</url>
    <scm>
        <connection>scm:git:ssh://**************/eclipse-ee4j/jaxb-ri</connection>
        <developerConnection>scm:git:ssh://**************/eclipse-ee4j/jaxb-ri.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaxb-ri.git</url>
        <tag>HEAD</tag>
    </scm>

    <licenses>
        <license>
            <name>Eclipse Distribution License - v 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Roman Grigoriadi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaxb-ri/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>JAXB mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jaxb-dev</archive>
        </mailingList>
    </mailingLists>

    <repositories>
        <repository>
            <id>netbeans</id>
            <name>Repository hosting NetBeans modules</name>
            <url>http://bits.netbeans.org/nexus/content/groups/netbeans</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <junit.version>4.12</junit.version>
        <args4j.version>1.0</args4j.version>

        <module.path>${project.build.directory}/mods</module.path>
        <!-- This will work ONLY if mvn is run from root folder. In case of runing from submodules - fail :( -->
        <glassfish.findbugs.version>1.7</glassfish.findbugs.version>
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>High</findbugs.threshold>
        <findbugs.exclude/>
        <findbugs.version>3.0.1</findbugs.version>
        <skipOsgiTests>true</skipOsgiTests>
        <netbeans.hint.jdkPlatform>JDK_9</netbeans.hint.jdkPlatform>
        <felix.junit4osgi>1.0.0</felix.junit4osgi>
        <felix.osgi.core>6.0.0</felix.osgi.core>
        <jmockit.version>1.34</jmockit.version>
        <copyright.template.file>tools/config/copyright.txt</copyright.template.file>
        <copyright.exclude.file>tools/config/copyright-exclude</copyright.exclude.file>
        <mrjar.sourceDirectory>${project.basedir}/src/main/java-mr</mrjar.sourceDirectory>
        <base.java.level>7</base.java.level>
        <upper.java.level>9</upper.java.level>
        <root.dir>${session.executionRootDirectory}/..</root.dir>
        <oss.disallow.snapshots>true</oss.disallow.snapshots>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>args4j</groupId>
                <artifactId>args4j</artifactId>
                <version>${args4j.version}</version>
            </dependency>
            <!-- Test -->
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- JDK dependencies -->
            <dependency>
                <!-- required by com.sun.tools.xjc.Options on JDK < 9
               (com.sun.org.apache.xml.internal.resolver.CatalogManager
                com.sun.org.apache.xml.internal.resolver.tools.CatalogResolver) -->
                <groupId>com.sun.org.apache.xml.internal</groupId>
                <artifactId>resolver</artifactId>
                <version>20050927</version>
                <optional>true</optional>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.1</version>
                    <configuration>
                        <createDependencyReducedPom>false</createDependencyReducedPom>
                        <filters>
                            <filter>
                                <artifact>*:*</artifact>
                                <excludes>
                                    <exclude>module-info.java</exclude>
                                </excludes>
                            </filter>
                        </filters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <escapeString>\</escapeString>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>4.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                            <manifest>
                                <addClasspath>true</addClasspath>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                    <configuration>
                        <source>1.7</source>
                        <target>1.7</target>
                        <fork>true</fork>
                        <compilerArgs>
                            <arg>-Xlint:all</arg>
                            <!--<XDignore.symbol.file/>-->
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <aggregate>false</aggregate>
                        <dependencySourceExcludes>
                            <dependencySourceExclude>com.sun.xml:*</dependencySourceExclude>
                        </dependencySourceExcludes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                    <executions>
                        <execution>
                            <id>jaxb.version</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>parse-version</goal>
                            </goals>
                            <configuration>
                                <propertyPrefix>jaxb</propertyPrefix>
                            </configuration>
                        </execution>
                        <execution>
                            <id>jaxb-api.version</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>parse-version</goal>
                            </goals>
                            <configuration>
                                <propertyPrefix>jaxb-api</propertyPrefix>
                                <versionString>${jaxb-api.version}</versionString>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.50</version>
                    <configuration>
                        <templateFile>${copyright.template.file}</templateFile>
                        <!--todo: fix me-->
                        <excludeFile>${copyright.exclude.file}</excludeFile>
                        <!--svn|mercurial|git - defaults to svn-->
                        <scm>git</scm>
                        <!-- turn on/off debugging -->
                        <debug>false</debug>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>${findbugs.version}</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            ${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>${glassfish.findbugs.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>1.0-alpha-2</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                    <configuration>
                        <locale>en-US</locale>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${root.dir}</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.7,)</version>
                        </requireJavaVersion>
                        <requireMavenVersion>
                            <version>[3.3.9,)</version>
                        </requireMavenVersion>
                        <DependencyConvergence/>
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>create-buildnumber</id>
                        <goals>
                            <goal>create</goal>
                        </goals>
                        <configuration>
                            <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                            <shortRevisionLength>7</shortRevisionLength>
                            <revisionOnScmFailure>unknown</revisionOnScmFailure>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <configuration>
                            <source>${upper.java.level}</source>
                            <target>${upper.java.level}</target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <source>1.7</source>
                            <target>1.7</target>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>${findbugs.version}</version>
                <configuration>
                    <skip>${findbugs.skip}</skip>
                    <threshold>${findbugs.threshold}</threshold>
                    <excludeFilterFile>
                        ${findbugs.exclude}
                    </excludeFilterFile>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <modules>
        <module>boms/bom</module>
        <module>boms/bom-ext</module>
        <module>txw</module>
        <module>runtime</module>
        <module>xjc</module>
        <module>jxc</module>
        <module>bundles</module>
    </modules>

    <profiles>
        <profile>
            <id>default-profile</id>
            <activation>
                <property>
                    <name>!dev</name>
                </property>
            </activation>
            <!--            <properties>
                <skipOsgiTests>false</skipOsgiTests>
            </properties>-->
            <modules>
                <module>docs</module>
                <module>tools/osgi_tests</module>
            </modules>
        </profile>
        <profile>
            <id>oss-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>enforce-no-snapshots</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireReleaseDeps>
                                            <message>No SNAPSHOT dependency allowed!</message>
                                        </requireReleaseDeps>
                                        <requireReleaseVersion>
                                            <message>release version no SNAPSHOT Allowed!</message>
                                        </requireReleaseVersion>
                                    </rules>
                                    <fail>${oss.disallow.snapshots}</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>dev-impl</id>
            <activation>
                <property>
                    <name>dev</name>
                </property>
            </activation>
        </profile>
        <profile>
            <id>coverage</id>
            <activation>
                <property>
                    <name>cobertura-build</name>
                </property>
            </activation>
            <properties>
                <cobertura.report.format>xml</cobertura.report.format>
                <cobertura.version>*******</cobertura.version>
                <cobertura.skip>false</cobertura.skip>
                <net.sourceforge.cobertura.datafile>${basedir}/target/cobertura/cobertura.ser</net.sourceforge.cobertura.datafile>
            </properties>
            <dependencies>
                <!-- cobertura library -->
                <dependency>
                    <groupId>net.sourceforge.cobertura</groupId>
                    <artifactId>cobertura</artifactId>
                    <version>${cobertura.version}</version>
                    <exclusions>
                        <exclusion>
                            <groupId>oro</groupId>
                            <artifactId>oro</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>asm</groupId>
                            <artifactId>asm</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>asm</groupId>
                            <artifactId>asm-tree</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>log4j</groupId>
                            <artifactId>log4j</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <!-- needed for cobertura-instrument ant task to work
                and to not be included in assemblies/shaded jars -->
                <dependency>
                    <groupId>oro</groupId>
                    <artifactId>oro</artifactId>
                    <version>2.0.8</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                    <version>3.3.1</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>asm</groupId>
                    <artifactId>asm-tree</artifactId>
                    <version>3.3.1</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                    <version>1.2.17</version>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>cobertura-maven-plugin</artifactId>
                            <version>2.5.2</version>
                        </plugin>
                        <plugin>
                            <groupId>org.osgi</groupId>
                            <artifactId>maven-bundle-plugin</artifactId>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <attach>true</attach>
                            <skip>${cobertura.skip}</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <id>instrument-code</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>instrument</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
