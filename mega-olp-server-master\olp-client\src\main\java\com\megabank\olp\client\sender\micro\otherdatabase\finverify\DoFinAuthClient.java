/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.finverify;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.finverify.bean.DoFinAuthArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.finverify.bean.DoFinAuthResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class DoFinAuthClient extends BaseOtherDatabaseClient<DoFinAuthArgBean, DoFinAuthResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/megaibl/finverify/doAuth";
	}

}
