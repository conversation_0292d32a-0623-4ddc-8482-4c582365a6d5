package com.megabank.olp.common.service;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import com.megabank.olp.base.enums.UploadStatusEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.common.persistence.pojo.code.CodeApplyStatus;
import com.megabank.olp.common.persistence.pojo.code.CodeLandingAction;
import com.megabank.olp.common.persistence.pojo.code.CodeLandingRequestType;
import com.megabank.olp.common.persistence.pojo.code.CodeTransmissionStatus;
import com.megabank.olp.common.service.bean.code.CodeResBean;
import com.megabank.olp.common.utility.enums.CodeTypeEnum;

import mockit.Expectations;
import mockit.Tested;

public class CodeServiceTest extends AbstractCommonServiceTest
{
	@Tested
	private CodeService service;

	@Test
	public void getCodeList_CodeApplyStatus_Return_ResBean()
	{
		String codeType = CodeTypeEnum.APPLY_STATUS.getContext();
		String code = "complete";
		String name = "申請完成";

		List<CodeApplyStatus> applyStatusList = new ArrayList<>();
		CodeApplyStatus applyStatus = new CodeApplyStatus();
		applyStatus.setApplyStatusCode( code );
		applyStatus.setName( name );
		applyStatusList.add( applyStatus );

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		assertThat( resBeans.get( 0 ).getCode(), equalTo( code ) );
		assertThat( resBeans.get( 0 ).getName(), equalTo( name ) );
	}

	@Test
	public void getCodeList_CodeLandingAction_Return_ResBean()
	{
		String codeType = CodeTypeEnum.LANDING_ACTION.getContext();
		String code = "create";
		String name = "建立";

		List<CodeLandingAction> landingActionList = new ArrayList<>();
		CodeLandingAction landingAction = new CodeLandingAction();
		landingAction.setLandingActionCode( code );
		landingAction.setName( name );
		landingActionList.add( landingAction );

		new Expectations()
		{
			{
				codeLandingActionDAO.getAllPojos();
				result = landingActionList;
			}
		};

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		assertThat( resBeans.get( 0 ).getCode(), equalTo( code ) );
		assertThat( resBeans.get( 0 ).getName(), equalTo( name ) );
	}

	@Test
	public void getCodeList_CodeLandingRequestType_Return_ResBean()
	{
		String codeType = CodeTypeEnum.LANDING_REQUEST_TYPE.getContext();
		String code = "add";
		String name = "新增需求單";

		List<CodeLandingRequestType> requestTypeList = new ArrayList<>();
		CodeLandingRequestType requestType = new CodeLandingRequestType();
		requestType.setLandingRequestType( code );
		requestType.setName( name );
		requestTypeList.add( requestType );

		new Expectations()
		{
			{
				codeLandingRequestTypeDAO.getAllPojos();
				result = requestTypeList;
			}
		};

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		assertThat( resBeans.get( 0 ).getCode(), equalTo( code ) );
		assertThat( resBeans.get( 0 ).getName(), equalTo( name ) );
	}

	@Test
	public void getCodeList_CodeTransmissionStatus_Return_ResBean()
	{
		String codeType = CodeTypeEnum.TRANSMISSION_STATUS.getContext();
		String code = "exception";
		String name = "進件異常";

		List<CodeTransmissionStatus> transmissionStatusList = new ArrayList<>();
		CodeTransmissionStatus transmissionStatus = new CodeTransmissionStatus();
		transmissionStatus.setTransmissionStatusCode( code );
		transmissionStatus.setName( name );
		transmissionStatusList.add( transmissionStatus );

		new Expectations()
		{
			{
				codeTransmissionStatusDAO.getAllPojos();
				result = transmissionStatusList;
			}
		};

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		assertThat( resBeans.get( 0 ).getCode(), equalTo( code ) );
		assertThat( resBeans.get( 0 ).getName(), equalTo( name ) );
	}

	@Test
	public void getCodeList_CodeUploadStatus_Return_ResBean()
	{
		String codeType = CodeTypeEnum.UPLOAD_STATUS.getContext();
		String code = UploadStatusEnum.NO.getContext();
		String name = UploadStatusEnum.NO.getName();

		List<CodeResBean> resBeans = service.getCodeList( codeType );

		assertThat( resBeans.get( 0 ).getCode(), equalTo( code ) );
		assertThat( resBeans.get( 0 ).getName(), equalTo( name ) );
	}

	@Test
	public void getCodeList_NotExistCodeType_Return_COMM01003()
	{
		String statusType = "none";

		expectedException.expect( MyRuntimeException.class );
		expectedException.expectMessage( "COMM01003" );
		service.getCodeList( statusType );
	}
}
