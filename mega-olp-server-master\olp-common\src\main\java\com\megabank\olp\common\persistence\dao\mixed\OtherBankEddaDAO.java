package com.megabank.olp.common.persistence.dao.mixed;

import java.util.List;

import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BaseDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeList;

@Repository
public class OtherBankEddaDAO extends BaseDAO
{

	private static final String CODE_VALUE_CONSTANT = "codeValue";

	public List<CodeList> getOtherBankEdda()
	{

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "otherbankedda.getIntersectionList" );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeList.class );

		return nativeQuery.getResultList();
	}

	public List<CodeList> getOtherBankEddaBranchcode( String bankId )
	{

		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "otherbankedda.getOtherbankEddaBranchList" );

		nativeQuery.setParameter( CODE_VALUE_CONSTANT, bankId + "%", String.class );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeList.class );

		return nativeQuery.getResultList();
	}

}
