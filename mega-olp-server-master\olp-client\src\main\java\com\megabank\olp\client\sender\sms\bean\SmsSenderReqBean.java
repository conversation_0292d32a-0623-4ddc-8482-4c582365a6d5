package com.megabank.olp.client.sender.sms.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlRootElement( name = "soap:Envelope" )
@XmlAccessorType( XmlAccessType.FIELD )
public class SmsSenderReqBean extends BaseBean
{
	@XmlAttribute( name = "xmlns:xsi" )
	private String xmlnsXsi = "http://www.w3.org/2001/XMLSchema-instance";

	@XmlAttribute( name = "xmlns:xsd" )
	private String xmlnsXsd = "http://www.w3.org/2001/XMLSchema";

	@XmlAttribute( name = "xmlns:soap" )
	private String xmlnsSoap = "http://schemas.xmlsoap.org/soap/envelope/";

	@XmlElement( name = "soap:Body" )
	private RequestBodyBean requestBodyBean;

	public SmsSenderReqBean()
	{}

	public RequestBodyBean getRequestBodyBean()
	{
		return requestBodyBean;
	}

	public String getXmlnsSoap()
	{
		return xmlnsSoap;
	}

	public String getXmlnsXsd()
	{
		return xmlnsXsd;
	}

	public String getXmlnsXsi()
	{
		return xmlnsXsi;
	}

	public void setRequestBodyBean( RequestBodyBean requestBodyBean )
	{
		this.requestBodyBean = requestBodyBean;
	}

	public void setXmlnsSoap( String xmlnsSoap )
	{
		this.xmlnsSoap = xmlnsSoap;
	}

	public void setXmlnsXsd( String xmlnsXsd )
	{
		this.xmlnsXsd = xmlnsXsd;
	}

	public void setXmlnsXsi( String xmlnsXsi )
	{
		this.xmlnsXsi = xmlnsXsi;
	}
}
