package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeJobType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_job_type" )
public class CodeJobType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_job_type";

	public static final String JOB_TYPE_CONSTANT = "jobType";

	public static final String NAME_CONSTANT = "name";

	public static final String CODE_JOB_SUB_TYPES_CONSTANT = "codeJobSubTypes";

	private String jobType;

	private String name;

	private transient Set<CodeJobSubType> codeJobSubTypes = new HashSet<>( 0 );

	public CodeJobType()
	{}

	public CodeJobType( String jobType )
	{
		this.jobType = jobType;
	}

	public CodeJobType( String jobType, String name )
	{
		this.jobType = jobType;
		this.name = name;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeJobType" )
	public Set<CodeJobSubType> getCodeJobSubTypes()
	{
		return codeJobSubTypes;
	}

	@Id
	@Column( name = "job_type", unique = true, nullable = false, length = 20 )
	public String getJobType()
	{
		return jobType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setCodeJobSubTypes( Set<CodeJobSubType> codeJobSubTypes )
	{
		this.codeJobSubTypes = codeJobSubTypes;
	}

	public void setJobType( String jobType )
	{
		this.jobType = jobType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}