package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeApplyStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_apply_status" )
public class CodeApplyStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_apply_status";

	public static final String APPLY_STATUS_CODE_CONSTANT = "applyStatusCode";

	public static final String NAME_CONSTANT = "name";

	private String applyStatusCode;

	private String name;

	public CodeApplyStatus()
	{}

	public CodeApplyStatus( String applyStatusCode )
	{
		this.applyStatusCode = applyStatusCode;
	}

	@Id
	@Column( name = "apply_status_code", unique = true, nullable = false, length = 20 )
	public String getApplyStatusCode()
	{
		return applyStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setApplyStatusCode( String applyStatusCode )
	{
		this.applyStatusCode = applyStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}