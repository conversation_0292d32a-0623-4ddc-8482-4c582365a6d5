package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeRelationBorrowerType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_relation_borrower_type" )
public class CodeRelationBorrowerType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_relation_borrower_type";

	public static final String RELATION_BORROWER_TYPE_CONSTANT = "relationBorrowerType";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	private String relationBorrowerType;

	private String name;

	private int displayOrder;

	public CodeRelationBorrowerType()
	{}

	public CodeRelationBorrowerType( String relationBorrowerType )
	{
		this.relationBorrowerType = relationBorrowerType;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "relation_borrower_type", unique = true, nullable = false, length = 20 )
	public String getRelationBorrowerType()
	{
		return relationBorrowerType;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setRelationBorrowerType( String relationBorrowerType )
	{
		this.relationBorrowerType = relationBorrowerType;
	}
}