/**
 *
 */
package com.megabank.olp.client.sender.sso.login;

import java.util.Date;

import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.sso.BaseSsoClient;
import com.megabank.olp.client.sender.sso.login.bean.SsoLoginArgBean;
import com.megabank.olp.client.sender.sso.login.bean.SsoLoginReqBean;
import com.megabank.olp.client.sender.sso.login.bean.SsoLoginReqDataBean;
import com.megabank.olp.client.sender.sso.login.bean.SsoLoginResBean;
import com.megabank.olp.client.sender.sso.login.bean.SsoLoginResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
@Component
public class SsoLoginClient extends BaseSsoClient<SsoLoginArg<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>so<PERSON><PERSON>in<PERSON><PERSON><PERSON><PERSON>, SsoLoginResultBean>
{
	@Override
	protected String getSimulatorCode( SsoLoginArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/openAPI/sso";
	}

	@Override
	protected SsoLoginReqBean transArg2Req( SsoLoginArgBean argBean )
	{
		String timestamp = Long.toString( new Date().getTime() );

		SsoLoginReqBean reqBean = new SsoLoginReqBean();
		reqBean.setClientIp( argBean.getClientIp() );
		reqBean.setTrackingIxd( argBean.getTrackingIxd() );
		reqBean.setClientTime( timestamp );
		reqBean.setClientIxd( "irobot" );
		reqBean.setFromSys( "3" );

		SsoLoginReqDataBean reqDataBean = new SsoLoginReqDataBean();
		reqDataBean.setLoginType( argBean.getLoginType() );
		reqDataBean.setSessionIxd( argBean.getXAuthToken() );

		reqBean.setReqDataBean( reqDataBean );

		return reqBean;
	}

	@Override
	protected SsoLoginResultBean transRes2Result( SsoLoginResBean resBean, HttpHeaders httpHeaders )
	{
		SsoLoginResultBean resultBean = new SsoLoginResultBean();
		resultBean.setSys( resBean.getSys() );
		resultBean.setResource( resBean.getResource() );
		resultBean.setClientTime( resBean.getResponseDate() );
		resultBean.setCode( resBean.getReturnCode() );
		resultBean.setDesc( resBean.getReturnDesc() );

		if( resBean.getRespDataBean() != null )
		{
			resultBean.setAccessToken( resBean.getRespDataBean().getAccessToken() );
			resultBean.setSessionIxd( resBean.getRespDataBean().getSessionIxd() );
		}

		return resultBean;
	}

}
