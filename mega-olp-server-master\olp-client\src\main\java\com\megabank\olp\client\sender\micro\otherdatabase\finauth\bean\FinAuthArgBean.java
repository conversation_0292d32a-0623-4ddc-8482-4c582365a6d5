/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase.finauth.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class FinAuthArgBean extends BaseBean
{
	private String idNo;

	private Date birthDate;

	private String mobileNumber;

	private String bankCode;

	private String bankAccount;

	public FinAuthArgBean()
	{}

	public String getBankAccount()
	{
		return bankAccount;
	}

	public String getBankCode()
	{
		return bankCode;
	}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public void setBankAccount( String bankAccount )
	{
		this.bankAccount = bankAccount;
	}

	public void setBankCode( String bankCode )
	{
		this.bankCode = bankCode;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

}
