/**
 *
 */
package com.megabank.olp.client.sender.pib.token.bean;

import com.megabank.olp.client.sender.pib.BasePibArgBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public class PibTokenArgBean extends BasePibArgBean
{
	private String trackingIxd;

	private String clientIp;

	private String authCode;

	public PibTokenArgBean()
	{}

	public String getAuthCode()
	{
		return authCode;
	}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public void setAuthCode( String authCode )
	{
		this.authCode = authCode;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}

}
