package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeJobSubType;

/**
 * The CodeJobSubTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeJobSubTypeDAO extends BasePojoDAO<CodeJobSubType, Long>
{
	@Autowired
	private CodeJobTypeDAO codeJobTypeDAO;

	public List<CodeJobSubType> getPojosByJobType( String jobType )
	{
		Validate.notBlank( jobType );

		NameValueBean condition = new NameValueBean( "codeJobType", codeJobTypeDAO.read( jobType ) );

		return getPojosByProperty( condition );
	}

	public CodeJobSubType read( Long jobSubTypeId )
	{
		Validate.notNull( jobSubTypeId );

		return getPojoByPK( jobSubTypeId, CodeJobSubType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeJobSubType> getPojoClass()
	{
		return CodeJobSubType.class;
	}
}
