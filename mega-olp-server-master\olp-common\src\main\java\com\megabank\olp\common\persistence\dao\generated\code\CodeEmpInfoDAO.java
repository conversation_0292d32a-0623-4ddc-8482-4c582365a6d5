package com.megabank.olp.common.persistence.dao.generated.code;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeEmpInfo;

@Repository
public class CodeEmpInfoDAO extends BasePojoDAO<CodeEmpInfo, Long>
{
	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	public CodeEmpInfo getPojoByEmpId( String empId )
	{
		return getUniquePojoByProperty( new NameValueBean( CodeEmpInfo.EMP_ID, empId ) );
	}

	@Override
	protected Class<CodeEmpInfo> getPojoClass()
	{
		return CodeEmpInfo.class;
	}

}
