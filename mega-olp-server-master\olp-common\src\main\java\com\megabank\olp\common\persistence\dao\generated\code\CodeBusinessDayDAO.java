package com.megabank.olp.common.persistence.dao.generated.code;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import jakarta.persistence.TemporalType;
import org.apache.commons.lang3.Validate;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.type.descriptor.java.DateJavaType;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeBusinessDay;

/**
 * The CodeBusinessDayDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeBusinessDayDAO extends BasePojoDAO<CodeBusinessDay, Date>
{
	private static final String BUSINESS_DAY_CONSTANT = "businessDay";

	public void create( Date code )
	{
		Validate.notNull( code );

		CodeBusinessDay pojo = new CodeBusinessDay();
		pojo.setBusinessDayCode( code );

		super.createPojo( pojo );
	}

	public List<CodeBusinessDay> get5PojosAfterDate( Date date )
	{
		Validate.notNull( date );
		NativeQuery nativeQuery = ( NativeQuery )getNamedQuery( "businessday.get5PojosAfterDate" );
		nativeQuery.setParameter( BUSINESS_DAY_CONSTANT, date, TemporalType.DATE );

		nativeQuery.unwrap( NativeQueryImpl.class ).addEntity( CodeBusinessDay.class );

		return nativeQuery.getResultList();
	}

	public List<CodeBusinessDay> get5PojosAfterToday()
	{
		return get5PojosAfterDate( new Date() );
	}

	@Override
	protected Class<CodeBusinessDay> getPojoClass()
	{
		return CodeBusinessDay.class;
	}
}
