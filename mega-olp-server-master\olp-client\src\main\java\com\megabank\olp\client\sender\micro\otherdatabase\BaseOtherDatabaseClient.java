/**
 *
 */
package com.megabank.olp.client.sender.micro.otherdatabase;

import java.io.UnsupportedEncodingException;
import java.net.URI;

import com.megabank.olp.client.sender.micro.BaseMicroServicesClient;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */

public abstract class BaseOtherDatabaseClient<TArg, TResult> extends BaseMicroServicesClient<TArg, TResult>
{
	@Override
	protected URI getApiURI( TArg argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlMicroOtherDatabase() + getSuffixUrl() );
	}

	@Override
	protected String getSystemName()
	{
		return "other-database";
	}
}