package com.megabank.olp.client.sender.monitor.exception.bean;

import com.megabank.olp.client.sender.monitor.bean.BaseMonitorArgBean;

public class MonitorExceptionArgBean extends BaseMonitorArgBean
{
	private String occurService;

	private String errorMsg;

	private Long errorLogId;

	public MonitorExceptionArgBean()
	{}

	public MonitorExceptionArgBean( String occurService, String errorMsg )
	{
		this.occurService = occurService;
		this.errorMsg = errorMsg;
	}

	public Long getErrorLogId()
	{
		return errorLogId;
	}

	public String getErrorMsg()
	{
		return errorMsg;
	}

	public String getOccurService()
	{
		return occurService;
	}

	public void setErrorLogId( Long errorLogId )
	{
		this.errorLogId = errorLogId;
	}

	public void setErrorMsg( String errorMsg )
	{
		this.errorMsg = errorMsg;
	}

	public void setOccurService( String occurService )
	{
		this.occurService = occurService;
	}

}
