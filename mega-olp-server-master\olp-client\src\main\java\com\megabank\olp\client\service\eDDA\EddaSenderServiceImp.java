package com.megabank.olp.client.service.eDDA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.eDDA.EddaSenderClient;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderArgBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "sit", "stress", "uat", "prod" } )
public class EddaSenderServiceImp implements EddaSenderService
{
	@Autowired
	private EddaSenderClient eDDASenderclient;

	@Override
	public EddaSenderResultBean send( EddaSenderArgBean argBean )
	{
		EddaSenderResultBean result = eDDASenderclient.send( argBean );

		return result;
	}
}
