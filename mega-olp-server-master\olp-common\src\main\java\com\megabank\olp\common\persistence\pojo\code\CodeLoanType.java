package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeLoanType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_loan_type" )
public class CodeLoanType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_loan_type";

	public static final String LOAN_TYPE_CONSTANT = "loanType";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String CODE_LOAN_PURPOSES_CONSTANT = "codeLoanPurposes";

	private String loanType;

	private String name;

	private int displayOrder;

	private transient Set<CodeLoanPurpose> codeLoanPurposes = new HashSet<>( 0 );

	public CodeLoanType()
	{}

	public CodeLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public CodeLoanType( String loanType, String name, int displayOrder )
	{
		this.loanType = loanType;
		this.name = name;
		this.displayOrder = displayOrder;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeLoanType" )
	public Set<CodeLoanPurpose> getCodeLoanPurposes()
	{
		return codeLoanPurposes;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Id
	@Column( name = "loan_type", unique = true, nullable = false, length = 20 )
	public String getLoanType()
	{
		return loanType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setCodeLoanPurposes( Set<CodeLoanPurpose> codeLoanPurposes )
	{
		this.codeLoanPurposes = codeLoanPurposes;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}