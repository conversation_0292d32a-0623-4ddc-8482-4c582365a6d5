package com.megabank.olp.common.controller;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.base.layer.BaseController;
import com.megabank.olp.base.service.jwt.decrypt.JwtService;
import com.megabank.olp.common.controller.bean.code.BackendCodeListGetterArgBean;
import com.megabank.olp.common.controller.bean.code.BranchBankListGetterArgBean;
import com.megabank.olp.common.controller.bean.code.BranchListGetterArgBean;
import com.megabank.olp.common.controller.bean.code.CodeListGetterArgBean;
import com.megabank.olp.common.controller.bean.code.IntroduceBranchBankListGetterArgBean;
import com.megabank.olp.common.controller.bean.code.ServiceAssociateBranchGetterArgBean;
import com.megabank.olp.common.controller.bean.code.SubCodeListGetterArgBean;
import com.megabank.olp.common.service.CodeService;

@RestController
@RequestMapping( "open/code" )
public class CodeController extends BaseController
{
	@Autowired
	private CodeService codeService;
	
	@Autowired
	private JwtService jwtService;

	/**
	 * 取得對保撥款日
	 *
	 * @return
	 */
	@PostMapping( "getAppropirationDateList" )
	public Map<String, Object> getAppropirationDateList()
	{
		return getResponseMap( codeService.getAppropirationDateList() );
	}

	/**
	 * 取得管理後台下拉式選單內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getBackendCodeList" )
	public Map<String, Object> getBackendCodeList( @RequestBody @Validated BackendCodeListGetterArgBean argBean )
	{
		String codeType = argBean.getCodeType();
		Long branchBankId = argBean.getBranchBankId();
		Long subBranchBankId = argBean.getSubBranchBankId();
		String originalBranchBankId = argBean.getOriginalBranchBankCode();
		String loanType = argBean.getLoanType();
		String recipient = argBean.getRecipient();

		return getResponseMap( codeService.getBackendCodeList( codeType, branchBankId, subBranchBankId, originalBranchBankId, loanType, recipient ) );
	}

	/**
	 * 取得分行列表
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getBranchBankList" )
	public Map<String, Object> getBranchBankList( @RequestBody @Validated BranchBankListGetterArgBean argBean )
	{
		String bankType = argBean.getBankType();
		String townCode = argBean.getTownCode();

		return getResponseMap( codeService.getBranchBankList( townCode, bankType ) );
	}

	@PostMapping( "getBranchList" )
	public Map<String, Object> getBranchList( @RequestBody @Validated BranchListGetterArgBean argBean )
	{
		String bno = StringUtils.trimToEmpty( argBean.getBno() );
		Boolean withDefault = argBean.getWithDefault() == null ? Boolean.TRUE : argBean.getWithDefault();

		/*
		 * curl -X POST -H "Content-type: application/json" -d "{\"bno\":\"229\"}"
		 * http://olp-common-ploan-sit.apps-t.megabank.com.tw/open/code/getBranchList
		 */
		return getResponseMap( codeService.getBranchList( bno, withDefault ) );
	}

	@PostMapping( "getActiveBranchList" )
	public Map<String, Object> getActiveBranchList()
	{
		return getResponseMap( codeService.getActiveBranchList() );
	}

	/**
	 * 取得下拉式選單內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getCodeList" )
	public Map<String, Object> getCodeList( @RequestBody @Validated CodeListGetterArgBean argBean )
	{
		String codeType = argBean.getCodeType();

		/*
		 * curl -X POST -H "Content-type: application/json" -d "{\"codeType\":\"rate-adjustment-notification\"}"
		 * http://olp-common-ploan-sit.apps-t.megabank.com.tw/open/code/getCodeList
		 */
		return getResponseMap( codeService.getCodeList( codeType ) );
	}

	/**
	 * 取得子類別下拉式選單內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getSubCodeList" )
	public Map<String, Object> getSubCodeList( @RequestBody @Validated SubCodeListGetterArgBean argBean )
	{
		String codeType = argBean.getCodeType();
		String codeId = argBean.getCodeId();

		return getResponseMap( codeService.getSubCodeList( codeType, codeId ) );
	}

	/**
	 * 取得引介分行下拉式選單內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getBackendIntroduceBranchList" )
	public Map<String, Object> getBackendIntroduceBranchList( @RequestBody @Validated IntroduceBranchBankListGetterArgBean argBean )
	{
		Long branchBankId = argBean.getBranchBankId();
		Long subBranchBankId = argBean.getSubBranchBankId();
		String originalBranchBankCode = argBean.getOriginalBranchBankCode();

		return getResponseMap( codeService.getIntroduceBranchList( branchBankId, subBranchBankId, originalBranchBankCode, argBean.getLoanType() ) );
	}
	
	/**
	 * 取得行銷人員的行銷分行
	 */
	@PostMapping( "getBranchByEmpId" )
	public Map<String, Object> getBranchByEmpId( @RequestBody ServiceAssociateBranchGetterArgBean argBean )
	{
		
		return getResponseMap( codeService.getEmpBranch( argBean.getEmpId() ) );
	}
}
