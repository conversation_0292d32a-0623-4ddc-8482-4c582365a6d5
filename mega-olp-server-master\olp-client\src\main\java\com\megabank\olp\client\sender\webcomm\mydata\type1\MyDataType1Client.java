package com.megabank.olp.client.sender.webcomm.mydata.type1;

import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ArgBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ReqBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ResBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ResultBean;
import com.megabank.olp.client.sender.webcomm.mydata.WebCommMyDataClient;
import com.megabank.olp.client.utility.enums.ClientMyDataErrorEnum;

@Component
public class MyDataType1Client extends WebCommMyDataClient<MyDataType1Arg<PERSON>ean, MyDataType1ReqBean, MyDataType1Res<PERSON>ean, MyDataType1ResultBean>
{
	@Override
	protected ErrorEnum getMyDataErrorEnum()
	{
		return ClientMyDataErrorEnum.WEBCOMM_MY_DATA_TYPE1;
	}

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000" };
	}

	@Override
	protected MyDataType1ResultBean getResultBean( MyDataType1ResBean resBean )
	{
		MyDataType1ResultBean resultBean = new MyDataType1ResultBean();
		resultBean.setMsg( resBean.getMsg() );
		resultBean.setStatus( resBean.getStatus() );
		resultBean.setResults( resBean.getResultsBean() );

		return resultBean;
	}

	@Override
	protected String getSimulatorCode( MyDataType1ArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/mydataAuth";
	}

	@Override
	protected MyDataType1ReqBean transArg2Req( MyDataType1ArgBean argBean )
	{
		MyDataType1ReqBean reqBean = new MyDataType1ReqBean();
		reqBean.setTransactionId( argBean.getTransactionId() );
		reqBean.setPersonId( argBean.getPersonId() );
		reqBean.setBirthday( argBean.getBirthday() );
		reqBean.setReturnUrl( argBean.getReturnUrl() );
		reqBean.setFailReturnUrl( argBean.getFailReturnUrl() );
		reqBean.setChannelSource( argBean.getChannelSource() );
		reqBean.setBu( argBean.getBu() );
		reqBean.setCaseNumber( argBean.getCaseNumber() );

		return reqBean;
	}

	@Override
	protected MyDataType1ResultBean transRes2Result( MyDataType1ResBean resBean, HttpHeaders httpHeaders )
	{
		return getResultBean( resBean );
	}
	
	@Override
	protected boolean save_to_system_tran_log()
	{
		return true;
	}

}
