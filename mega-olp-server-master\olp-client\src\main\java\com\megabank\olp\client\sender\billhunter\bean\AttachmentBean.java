package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "attachHeaderBean", "attachBody" } )
public class AttachmentBean extends BaseBean
{
	@XmlElement( name = "AttachHeader" )
	private AttachHeaderBean attachHeaderBean;

	/**
	 * 附件檔轉base64內容
	 */
	@XmlElement( name = "AttachBody" )
	private String attachBody = "";

	public AttachmentBean()
	{}

	public String getAttachBody()
	{
		return attachBody;
	}

	public AttachHeaderBean getAttachHeaderBean()
	{
		return attachHeaderBean;
	}

	public void setAttachBody( String attachBody )
	{
		this.attachBody = attachBody;
	}

	public void setAttachHeaderBean( AttachHeaderBean attachHeaderBean )
	{
		this.attachHeaderBean = attachHeaderBean;
	}
}
