package com.megabank.olp.common.persistence.pojo.code;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeLoanPurpose is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_loan_purpose" )
public class CodeLoanPurpose extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_loan_purpose";

	public static final String LOAN_PURPOSE_ID_CONSTANT = "loanPurposeId";

	public static final String CODE_LOAN_TYPE_CONSTANT = "codeLoanType";

	public static final String LOAN_PURPOSE_CODE_CONSTANT = "loanPurposeCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	public static final String DISABLED_CONSTANT = "disabled";

	private Long loanPurposeId;

	private transient CodeLoanType codeLoanType;

	private String loanPurposeCode;

	private String name;

	private int displayOrder;

	private boolean disabled;

	public CodeLoanPurpose()
	{}

	public CodeLoanPurpose( Long loanPurposeId )
	{
		this.loanPurposeId = loanPurposeId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "loan_type", nullable = false )
	public CodeLoanType getCodeLoanType()
	{
		return codeLoanType;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "loan_purpose_code", nullable = false, length = 20 )
	public String getLoanPurposeCode()
	{
		return loanPurposeCode;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "loan_purpose_id", unique = true, nullable = false )
	public Long getLoanPurposeId()
	{
		return loanPurposeId;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Column( name = "disabled", nullable = false, precision = 1, scale = 0 )
	public boolean isDisabled()
	{
		return disabled;
	}

	public void setCodeLoanType( CodeLoanType codeLoanType )
	{
		this.codeLoanType = codeLoanType;
	}

	public void setDisabled( boolean disabled )
	{
		this.disabled = disabled;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setLoanPurposeCode( String loanPurposeCode )
	{
		this.loanPurposeCode = loanPurposeCode;
	}

	public void setLoanPurposeId( Long loanPurposeId )
	{
		this.loanPurposeId = loanPurposeId;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}