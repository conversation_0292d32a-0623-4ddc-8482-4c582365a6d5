package com.megabank.olp.client.sender.pib;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

public class BasePibReqBean extends BaseBean
{
	private String resource;

	private String clientIxd = "eloan";

	private String trackingIxd;

	private String platform = "application";

	private String version = "1.0";

	private String clientNo;

	private String clientTime = Long.toString( new Date().getTime() );

	private String locale = "zh_TW";

	private String fromSys = "4";

	private String clientIp;

	public BasePibReqBean()
	{}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getClientIxd()
	{
		return clientIxd;
	}

	public String getClientNo()
	{
		return clientNo;
	}

	public String getClientTime()
	{
		return clientTime;
	}

	public String getFromSys()
	{
		return fromSys;
	}

	public String getLocale()
	{
		return locale;
	}

	public String getPlatform()
	{
		return platform;
	}

	public String getResource()
	{
		return resource;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public String getVersion()
	{
		return version;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setClientIxd( String clientIxd )
	{
		this.clientIxd = clientIxd;
	}

	public void setClientNo( String clientNo )
	{
		this.clientNo = clientNo;
	}

	public void setClientTime( String clientTime )
	{
		this.clientTime = clientTime;
	}

	public void setFromSys( String fromSys )
	{
		this.fromSys = fromSys;
	}

	public void setLocale( String locale )
	{
		this.locale = locale;
	}

	public void setPlatform( String platform )
	{
		this.platform = platform;
	}

	public void setResource( String resource )
	{
		this.resource = resource;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}

	public void setVersion( String version )
	{
		this.version = version;
	}

}
