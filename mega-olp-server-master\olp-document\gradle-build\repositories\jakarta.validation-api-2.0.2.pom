<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Jakarta Bean Validation API
  ~
  ~ License: Apache License, Version 2.0
  ~ See the license.txt file in the root directory or <http://www.apache.org/licenses/LICENSE-2.0>.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>jakarta.validation</groupId>
    <artifactId>jakarta.validation-api</artifactId>
    <version>2.0.2</version>
    <packaging>jar</packaging>
    <name>Jakarta Bean Validation API</name>
    <url>https://beanvalidation.org</url>

    <description>
        Jakarta Bean Validation API
    </description>

    <developers>
        <developer>
            <id>epbernard</id>
            <name>Emmanuel <PERSON></name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/emmanuel-bernard/</url>
        </developer>
        <developer>
            <id>emmanuelbernard</id>
            <name>Emmanuel Bernard</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/emmanuel-bernard/</url>
        </developer>
        <developer>
            <id>hardy.ferentschik</id>
            <name>Hardy Ferentschik</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/hardy-ferentschik/</url>
        </developer>
        <developer>
            <id>gunnar.morling</id>
            <name>Gunnar Morling</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/gunnar-morling/</url>
        </developer>
        <developer>
            <id>guillaume.smet</id>
            <name>Guillaume Smet</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/guillaume-smet/</url>
        </developer>
    </developers>

    <issueManagement>
        <system>JIRA</system>
        <url>https://hibernate.atlassian.net/projects/BVAL/</url>
    </issueManagement>

    <inceptionYear>2007</inceptionYear>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/eclipse-ee4j/beanvalidation-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/beanvalidation-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/beanvalidation-api</url>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.11</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <!--
                        Creates a report by running "mvn japicmp:cmp"
                        Note: you need to install the jars before running the japicmp command.
                    -->
                    <groupId>com.github.siom79.japicmp</groupId>
                    <artifactId>japicmp-maven-plugin</artifactId>
                    <version>0.9.3</version>
                    <configuration>
                        <oldVersion>
                            <dependency>
                                <groupId>javax.validation</groupId>
                                <artifactId>validation-api</artifactId>
                                <version>1.1.0.Final</version>
                            </dependency>
                        </oldVersion>
                        <newVersion>
                            <file>
                                <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
                            </file>
                        </newVersion>
                        <parameter>
                            <onlyModified>true</onlyModified>
                        </parameter>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <instructions>
                        <Export-Package>
                            javax.validation.*;version="${project.version}",
                        </Export-Package>
                        <Automatic-Module-Name>java.validation</Automatic-Module-Name>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <forkMode>once</forkMode>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <archive>
                        <manifestFile>${basedir}/target/classes/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <links>
                        <link>http://docs.oracle.com/javase/8/docs/api/</link>
                    </links>
                    <packagesheader>Jakarta Bean Validation API Packages</packagesheader>
                    <doctitle>Jakarta Bean Validation API ${project.version}</doctitle>
                    <windowtitle>Jakarta Bean Validation API ${project.version}</windowtitle>
                    <bottom><![CDATA[
Comments to: <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
Copyright &#169; 2019 Eclipse Foundation.<br>
Use is subject to <a href="{@docRoot}/doc-files/speclicense.html" target="_top">EFSL</a>; this spec is based on material that is licensed under the Apache License, version 2.0.]]>
                    </bottom>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>2.5.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <allowTimestampedSnapshots>true</allowTimestampedSnapshots>
                    <remoteTagging>true</remoteTagging>
                    <goals>deploy</goals>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>3.0</version>
                <configuration>
                    <header>${project.basedir}/license/license.header</header>
                    <strictCheck>true</strictCheck>
                    <headerDefinitions>
                        <headerDefinition>${project.basedir}/license/java-header-style.xml</headerDefinition>
                        <headerDefinition>${project.basedir}/license/xml-header-style.xml</headerDefinition>
                    </headerDefinitions>
                    <mapping>
                        <java>JAVA_CLASS_STYLE</java>
                        <xml>XML_FILE_STYLE</xml>
                        <xsd>XML_FILE_STYLE</xsd>
                    </mapping>
                    <includes>
                        <include>**/*.java</include>
                        <include>**/*.xml</include>
                        <include>**/*.xsd</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <id>license-headers</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <properties>
                <createChecksum>true</createChecksum>
            </properties>
        </profile>
    </profiles>

</project>
