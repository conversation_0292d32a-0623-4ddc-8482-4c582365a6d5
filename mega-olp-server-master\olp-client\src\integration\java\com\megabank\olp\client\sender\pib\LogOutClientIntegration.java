package com.megabank.olp.client.sender.pib;

import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.client.sender.pib.logout.LogOutClient;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutArgBean;
import com.megabank.olp.client.sender.pib.logout.bean.PibLogOutResultBean;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ClientServiceConfig.class, BaseServiceConfig.class, SystemConfig.class } )
public class LogOutClientIntegration
{

	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private LogOutClient client;

	@Test
	public void send()
	{
		String uuid = UUID.randomUUID().toString();

		PibLogOutArgBean argBean = new PibLogOutArgBean();
		argBean.setClientIp( "0.0.0.0" );
		argBean.setTrackingIxd( uuid );

		PibLogOutResultBean resultBean = client.send( argBean );

		logger.info( "resultBean:{}", resultBean );
	}

}
