/**
 *
 */
package com.megabank.olp.client.sender.sso.custinfo.bean;

import com.megabank.olp.client.sender.sso.BaseSsoArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoCustInfoArgBean extends BaseSsoArgBean
{
	private String trackingIxd;

	private String xAuthToken;

	private String accessToken;

	public SsoCustInfoArgBean()
	{}

	public String getAccessToken()
	{
		return accessToken;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public String getXAuthToken()
	{
		return xAuthToken;
	}

	public void setAccessToken( String accessToken )
	{
		this.accessToken = accessToken;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}

	public void setXAuthToken( String xAuthToken )
	{
		this.xAuthToken = xAuthToken;
	}

}
