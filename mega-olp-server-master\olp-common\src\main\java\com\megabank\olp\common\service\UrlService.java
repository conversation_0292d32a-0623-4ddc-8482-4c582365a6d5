package com.megabank.olp.common.service;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.megabank.olp.base.layer.BaseService;

@Service
public class UrlService extends BaseService
{
	@Autowired
	private Environment env;

	/**
	 * 查詢url是否顯示
	 *
	 * @param key
	 * @return
	 */
	public boolean isUrlDisplayed( String key )
	{
		Validate.notBlank( key );

		String property = env.getProperty( key );

		return BooleanUtils.toBoolean( property );
	}
}
