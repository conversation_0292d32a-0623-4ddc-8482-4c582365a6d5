package com.megabank.olp.client.sender.eloan.getted.accountAuth.bean;

import com.megabank.olp.base.bean.BaseBean;

public class EloanAccountAuthInfoArgBean extends BaseBean
{
	/**
	 *
	 */
	private static final long serialVersionUID = 5569341233880952243L;

	private String actionCode;// 執行功能代碼 (R:取得uuid, C:儲存交易結果)

	private String oid;// C122S01C oid

	public String getActionCode()
	{
		return actionCode;
	}

	public String getOid()
	{
		return oid;
	}

	public void setActionCode( String actionCode )
	{
		this.actionCode = actionCode;
	}

	public void setOid( String oid )
	{
		this.oid = oid;
	}

}
