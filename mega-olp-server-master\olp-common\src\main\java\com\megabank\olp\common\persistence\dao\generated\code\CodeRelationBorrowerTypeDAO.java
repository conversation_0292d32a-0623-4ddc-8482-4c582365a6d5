package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeRelationBorrowerType;

import org.springframework.stereotype.Repository;

/**
 * The CodeRelationBorrowerTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeRelationBorrowerTypeDAO extends BasePojoDAO<CodeRelationBorrowerType, String>
{
	@Override
	protected Class<CodeRelationBorrowerType> getPojoClass()
	{
		return CodeRelationBorrowerType.class;
	}
}
