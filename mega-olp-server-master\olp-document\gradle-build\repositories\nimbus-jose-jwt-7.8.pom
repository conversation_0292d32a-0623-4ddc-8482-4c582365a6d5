<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.nimbusds</groupId>
    <artifactId>nimbus-jose-jwt</artifactId>
    <version>7.8</version>
    <packaging>jar</packaging>

    <name>Nimbus JOSE+JWT</name>
    <description>
        Java library for Javascript Object Signing and Encryption (JOSE) and
        JSON Web Tokens (JWT)
    </description>
    <url>https://bitbucket.org/connect2id/nimbus-jose-jwt</url>

    <organization>
        <name>Connect2id Ltd.</name>
        <url>http://connect2id.com</url>
    </organization>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:https://bitbucket.org/connect2id/nimbus-jose-jwt.git</connection>
        <developerConnection> scm:git:*****************:connect2id/nimbus-jose-jwt.git</developerConnection>
        <url>https://bitbucket.org/connect2id/nimbus-jose-jwt</url>
        <tag>7.8</tag>
    </scm>

    <developers>
        <developer>
            <id>vdzhuvinov</id>
            <name>Vladimir Dzhuvinov</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.stephenc.jcip</groupId>
            <artifactId>jcip-annotations</artifactId>
            <version>1.0-1</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>[1.3.1,2.3]</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>[1.52,)</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>[1.52,)</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.bitbucket.b_c</groupId>
            <artifactId>jose4j</artifactId>
            <version>0.4.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.jadler</groupId>
            <artifactId>jadler-all</artifactId>
            <version>1.1.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.crypto.tink</groupId>
            <artifactId>tink</artifactId>
            <version>1.2.2</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                    <fork>true</fork>
                    <compilerArgument>-Xlint</compilerArgument>
                </configuration>
                <dependencies>
                    <!-- Using the eclipse compiler allows for different source and target,
                            which is a good thing (outweighing that this is a rarely used combination,
                            and most people use javac) This should also allow us to run maven builds
                            on a JRE and not a JDK. -->
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-compiler-eclipse</artifactId>
                        <version>2.5</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>default-compile</id>
                    </execution>
                    <execution>
                        <id>jdk6-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <source>1.7</source>
                            <target>1.6</target>
                            <compilerId>eclipse</compilerId>
                            <outputDirectory>${project.build.outputDirectory}_jdk6</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Build-Date>${timestamp}</Build-Date>
                            <Build-Number>${buildNumber}</Build-Number>
                            <Build-Tag>${project.scm.tag}</Build-Tag>
                        </manifestEntries>
                        <!-- Make sure MANIFEST.MF generated by maven-bundle-plugin is used for final JAR -->
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>default-jar</id>
                    </execution>
                    <execution>
                        <id>jdk6-jar</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classesDirectory>${project.build.outputDirectory}_jdk6</classesDirectory>
                            <classifier>jdk6</classifier>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.3</version>
                <configuration>
                    <author>true</author>
                    <version>true</version>
                    <use>true</use>
                    <linksource>true</linksource>
                    <windowtitle>Nimbus JOSE + JWT v${project.version}
                    </windowtitle>
                    <doctitle>Nimbus JOSE + JWT v${project.version}</doctitle>
                    <overview>${basedir}/src/main/javadoc/overview.html
                    </overview>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <useReleaseProfile>false</useReleaseProfile>
                    <goals>deploy</goals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.6</version>
                <!-- Pass password with
                    mvn release:prepare|perform -Darguments=-Dgpg.passphrase=thephrase
                -->
                <executions>
                    <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.3</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <doCheck>false</doCheck>
                    <doUpdate>false</doUpdate>
                    <timestampFormat>{0,date,yyyyMMdd.HHmmss.SSS}
                    </timestampFormat>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <!-- Remove when fixed: https://issues.apache.org/jira/browse/SUREFIRE-1588 -->
                    <useSystemClassLoader>false</useSystemClassLoader>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.5.0</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>com.nimbusds.jose.*,com.nimbusds.jwt.*</Export-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.7</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

</project>
