package com.megabank.olp.common.controller.bean.code;

import javax.validation.constraints.NotBlank;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */
public class CodeListGetterArgBean extends BaseBean
{
	@NotBlank
	private String codeType;

	public CodeListGetterArgBean()
	{
		// default constructor
	}

	/**
	 *
	 * @return codeType
	 */
	public String getCodeType()
	{
		return codeType;
	}

	/**
	 *
	 * @param codeType
	 */
	public void setCodeType( String codeType )
	{
		this.codeType = codeType;
	}
}
