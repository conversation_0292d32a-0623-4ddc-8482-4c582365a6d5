package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "name", "id", "mailBrn", "fromEmpId" } )
public class FromBean extends BaseBean
{
	@XmlElement( name = "FromEmpId" )
	private String fromEmpId;

	/**
	 * 寄件人戶名
	 */
	@XmlElement( name = "Name" )
	private String name;

	/**
	 * 寄件人ID
	 */
	@XmlElement( name = "ID" )
	private String id;

	/**
	 * 分行別BranchNo
	 */
	@XmlElement( name = "MailBrn" )
	private String mailBrn;

	public FromBean()
	{}

	public String getFromEmpId()
	{
		return fromEmpId;
	}

	public String getId()
	{
		return id;
	}

	public String getMailBrn()
	{
		return mailBrn;
	}

	public String getName()
	{
		return name;
	}

	public void setFromEmpId( String fromEmpId )
	{
		this.fromEmpId = fromEmpId;
	}

	public void setId( String id )
	{
		this.id = id;
	}

	public void setMailBrn( String mailBrn )
	{
		this.mailBrn = mailBrn;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}
