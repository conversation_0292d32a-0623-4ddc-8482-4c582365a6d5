package com.megabank.olp.client.utility;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.transaction.CannotCreateTransactionException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.NoHandlerFoundException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.base.enums.ResponseEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.utility.web.CommonAppUtils;
import com.megabank.olp.client.service.common.monitor.CommonMonitorService;
import com.megabank.olp.system.controller.bean.error.ErrorBean;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.service.TranLogService;
import com.megabank.olp.system.service.bean.ErrorMsgBean;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

@RestController
@RequestMapping( "error" )
public class ErrorController
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired( required = false )
	private HttpServletRequest request;

	@Autowired
	private SystemService systemService;

	@Autowired
	private TranLogService tranLogService;

	@Autowired
	private CommonMonitorService commonMonitorService;

	/**
	 *
	 * @return
	 */
	@RequestMapping( "responseBody" )
	@ResponseBody
	public Map<String, Object> doResponseBody()
	{
		logger.traceEntry();

		Map<String, Object> map = new HashMap<>();

		ErrorBean errorBean = processError();

		map.put( ResponseEnum.KEY_ERROR_CODE.getContext(), errorBean.getErrorCode() );
		map.put( ResponseEnum.KEY_ERROR_MSG.getContext(), errorBean.getErrorMsgBean() );
		map.put( ResponseEnum.KEY_ERROR_LOG_ID.getContext(), errorBean.getErrorLogId() );
		map.put( ResponseEnum.KEY_STAT.getContext(), ResponseEnum.STAT_ERROR.getContext() );

		return map;
	}

	/**
	 * @param exceptionLogPk
	 */
	private void doUpdateTranLogToError( Long tranLogId, Long exceptionLogId )
	{
		logger.traceEntry( "tranLogId:{}, exceptionLogId:{}", tranLogId, exceptionLogId );

		if( tranLogId == null )
			return;

		try
		{
			tranLogService.updateTranLogToError( tranLogId, exceptionLogId );
		}
		catch( Exception exception )
		{
			logger.error( exception );
		}
	}

	private String[] getArgsByInvalidFormatException( InvalidFormatException exception )
	{
		try
		{
			JsonParser jsonParser = ( JsonParser )exception.getProcessor();

			return new String[]{ jsonParser.getCurrentName(), ( String )exception.getValue() };
		}
		catch( IOException exception1 )
		{
			return new String[]{ "unknow", "unknow" };
		}
	}

	/**
	 * key is errorCode and errorMsg
	 *
	 * @param exception
	 * @return
	 */
	private Map<String, Object> getError( Exception exception )
	{
		String errorCode = null;
		String errorContent = null;

		ErrorMsgBean errorMsgBean = null;
		Long exceptionLogId = null;

		if( exception instanceof AccessDeniedException )
		{
			errorCode = SystemErrorEnum.ACCESS_DENIED.getCode();

			errorMsgBean = systemService.getErrorMsg( errorCode, null );
		}

		if( exception instanceof CannotCreateTransactionException )
		{
			errorCode = SystemErrorEnum.DATABASE.getCode();
			errorContent = "can not connect the database";
		}

		if( exception instanceof HttpRequestMethodNotSupportedException )
			errorCode = SystemErrorEnum.REQUEST_METHOD.getCode();

		if( exception instanceof HttpMessageNotReadableException )
		{
			errorCode = getErrorEnuByHttpMessageNoReadableException( exception ).getCode();
			errorMsgBean = getErrorMsgByHttpMessageNotReadableException( exception, errorCode );
		}

		if( exception instanceof MethodArgumentNotValidException )
		{
			errorCode = SystemErrorEnum.REQUEST_BODY_PROPERTY.getCode();

			errorMsgBean = processErrorMsg( ( MethodArgumentNotValidException )exception, errorCode );
		}

		if( exception instanceof MyRuntimeException )
		{
			MyRuntimeException myRuntimeException = ( MyRuntimeException )exception;

			errorCode = myRuntimeException.getErrorCode();
			errorContent = myRuntimeException.getErrorMsg();
			exceptionLogId = myRuntimeException.getExceptionLogId();

			if( errorContent == null )
				errorMsgBean = systemService.getErrorMsg( errorCode, ( ( MyRuntimeException )exception ).getArgs() );
		}

		if( exception instanceof NoHandlerFoundException )
		{
			errorCode = SystemErrorEnum.NOT_FOUND.getCode();
			errorMsgBean = systemService.getErrorMsg( errorCode, new String[]{ ( ( NoHandlerFoundException )exception ).getRequestURL() } );
		}

		if( errorCode == null )
		{
			errorCode = SystemErrorEnum.INTERNAL.getCode();
			errorMsgBean = systemService.getErrorMsg( errorCode, null );
		}

		if( errorMsgBean == null )
			errorMsgBean = new ErrorMsgBean( "系統錯誤", errorContent );

		Map<String, Object> map = new HashMap<>();
		map.put( "errorCode", errorCode );
		map.put( "errorMsg", errorMsgBean );
		map.put( "exceptionLogId", exceptionLogId );

		return map;
	}

	private ErrorEnum getErrorEnuByHttpMessageNoReadableException( Exception exception )
	{
		Throwable cause = exception.getCause();

		if( cause instanceof InvalidFormatException )
			return SystemErrorEnum.REQUEST_PROPERTY_VALUE;

		if( StringUtils.contains( exception.getMessage(), "Required request body is missing" ) )
			return SystemErrorEnum.REQUEST_BODY_NO_DATA;

		return SystemErrorEnum.REQUEST_BODY_FORMAT;
	}

	private ErrorMsgBean getErrorMsgByHttpMessageNotReadableException( Exception exception, String errorCode )
	{
		Throwable cause = exception.getCause();

		if( cause instanceof InvalidFormatException )
			return systemService.getErrorMsg( errorCode, getArgsByInvalidFormatException( ( InvalidFormatException )cause ) );

		return systemService.getErrorMsg( errorCode, null );
	}

	private ResponseEntity<String> getResponseEntity( String body )
	{
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType( new MediaType( "text", "html", StandardCharsets.UTF_8 ) );

		return new ResponseEntity<>( body, httpHeaders, HttpStatus.OK );
	}

	private boolean isDoByException( Exception exception, Class... clazzs )
	{
		for( Class clazz : clazzs )
			if( exception.getClass().getName().equals( clazz.getName() ) )
				return false;

		return true;
	}

	private boolean isNoCauseByMyRuntimeException( Exception exception )
	{
		return exception instanceof MyRuntimeException && exception.getCause() == null;
	}

	private boolean isPrintException( Exception exception )
	{
		if( isNoCauseByMyRuntimeException( exception ) )
			return false;

		return isDoByException( exception, NoHandlerFoundException.class );
	}

	private boolean isSaveException( Exception exception )
	{
		if( isNoCauseByMyRuntimeException( exception ) )
			return false;

		return isDoByException( exception, CannotCreateTransactionException.class, NoHandlerFoundException.class );
	}

	private ErrorBean processError()
	{
		Exception exception = null;
		Long tranLogId = null;
		Long exceptionLogId = null;

		String errorCode = null;
		String errorContent = null;

		ErrorMsgBean errorMsgBean = null;

		boolean need = false;

		try
		{
			exception = ( Exception )request.getAttribute( CommonAppUtils.HANDLED_EXCEPTION );
			tranLogId = ( Long )request.getAttribute( "tranLogId" );

			Validate.notNull( exception );

			Map<String, Object> errorMap = getError( exception );

			errorCode = ( String )errorMap.get( "errorCode" );
			errorMsgBean = ( ErrorMsgBean )errorMap.get( "errorMsg" );
			exceptionLogId = ( Long )errorMap.get( "exceptionLogId" );

			errorContent = errorMsgBean.getContent();
		}
		catch( MyRuntimeException ex )
		{
			errorCode = ex.getErrorCode();
			MessageFormat messageFormatter = new MessageFormat( "can not find {1} in {0} table" );
			errorContent = messageFormatter.format( ex.getArgs() );

			need = true;
			exception = ex;
		}
		catch( Exception ex )
		{
			errorCode = SystemErrorEnum.INTERNAL.getCode();
			errorContent = ex.getMessage();

			need = true;
			exception = ex;
		}

		if( need || isPrintException( exception ) )
			logger.error( exception.getMessage(), exception );

		if( need || isSaveException( exception ) )
		{
			// 因應簽署日期未成功寫表，一但發生錯誤，會特別標注 errorMsg
			if (Arrays.toString(exception.getStackTrace()).contains("com.megabank.olp.apply.controller.loan.SigningContractController.submitContract"))
				errorContent += "-submitContract";
			exceptionLogId = saveException( errorCode, errorContent, exception, tranLogId );
			commonMonitorService.sendByException( errorCode, errorContent, exception, tranLogId, exceptionLogId );
		}

		ErrorBean errorBean = new ErrorBean();
		errorBean.setErrorCode( errorCode );
		errorBean.setErrorLogId( exceptionLogId );
		errorBean.setErrorMsgBean( errorMsgBean != null ? errorMsgBean : new ErrorMsgBean( "系統錯誤", errorContent ) );

		logger.info( "errorBean:{}", errorBean );

		return errorBean;

	}

	private ErrorMsgBean processErrorMsg( MethodArgumentNotValidException ex, String errorCode )
	{
		String errorField;
		String errorFieldMsg;

		BindingResult bindingResult = ex.getBindingResult();

		if( bindingResult.hasFieldErrors() )
		{
			FieldError fieldError = bindingResult.getFieldError();

			errorField = fieldError.getField();
			errorFieldMsg = fieldError.getDefaultMessage();
		}
		else
		{
			ObjectError objectError = bindingResult.getAllErrors().get( 0 );

			errorField = String.format( "use %s annotation", objectError.getCode() );
			errorFieldMsg = objectError.getDefaultMessage();
		}

		return systemService.getErrorMsg( errorCode, new String[]{ errorField, errorFieldMsg } );
	}

	private Long saveException( String errorCode, String errorMsg, Exception ex, Long tranLogId )
	{
		try
		{
			Long exceptionLogId = systemService.saveExceptionLog( errorCode, ex, null, "mbt-restful", errorMsg );

			doUpdateTranLogToError( tranLogId, exceptionLogId );

			return exceptionLogId;
		}
		catch( Exception exception )
		{
			logger.catching( exception );

			return -99L;
		}
	}
}
