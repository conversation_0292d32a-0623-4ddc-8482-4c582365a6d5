package com.megabank.olp.common.service.bean.landing;

import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TextGroupColumnResBean extends BaseColumnResBean
{
	@JsonProperty( "columnValue" )
	private List<TextGroupBean> textGroupBeans = Collections.emptyList();

	public TextGroupColumnResBean()
	{
		// default constructor
	}

	public List<TextGroupBean> getTextGroupBeans()
	{
		return textGroupBeans;
	}

	public void setTextGroupBeans( List<TextGroupBean> textGroupBeans )
	{
		this.textGroupBeans = textGroupBeans;
	}

}
