package com.megabank.olp.client.service.eDDA;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.eDDA.EddaSenderClient;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderArgBean;
import com.megabank.olp.client.sender.eDDA.bean.EddaSenderResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev" } )
public class EddaSenderServiceMockImp implements EddaSenderService
{

	@Autowired
	private EddaSenderClient eDDASenderclient;

	@Override
	public EddaSenderResultBean send( EddaSenderArgBean argBean )
	{
		EddaSenderArgBean argBeanMock = new EddaSenderArgBean();
		String msg_uuid = "TWI900PCD1333-" + new SimpleDateFormat( "yyyyMMddHHmmssSSS" ).format( new Date() );
		// argBeanMock.setContractNo( "PA123456" );
		if( argBean.getAdMark() == null || argBean.getAdMark().equals( "A" ) )
			argBeanMock.setAdMark( "A" ); // 交易類型 {A:新增, M:變更, D:取消(取消授權免傳卡片資料), I:確認}
		else
			argBeanMock.setAdMark( argBean.getAdMark() ); // 交易類型 {A:新增, M:變更, D:取消(取消授權免傳卡片資料), I:確認}
		argBeanMock.setaID( "B123456780" ); // 用戶身分證/統編
		argBeanMock.setMegaMessageId( msg_uuid );
		// argBeanMock.setNote( "" );// 發動者專區 ,CHAR(40) 之前只有 CHAR(20)
		argBeanMock.setpBank( "0170000" ); // 發動行代號
		// argBeanMock.setpBankNote( "" ); // 發動行專區 AN20
		argBeanMock.setrBank( "8120000" ); // 扣款行代號
		argBeanMock.setRclNo( "**************" ); // 授權扣款帳號 => 在銀行系統與訊息建置指引的26/170提到{採Socket 傳送若欄位不足位數時，右靠左補零。},
												  // WebService版可以比照？
		argBeanMock.setrID( "B123456780" );
		argBeanMock.setUserNo( "************" ); // 用戶號碼
		argBeanMock.setContractNo( "************-006" );

		EddaSenderResultBean result = new EddaSenderResultBean();
		result = eDDASenderclient.send( argBeanMock );
		return result;
	}
}
