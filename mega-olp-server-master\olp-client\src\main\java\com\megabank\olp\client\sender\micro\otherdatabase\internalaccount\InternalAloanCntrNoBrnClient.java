package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalAloanCntrNoBrnArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalAloanCntrNoBrnResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */
@Component
public class InternalAloanCntrNoBrnClient extends BaseOtherDatabaseClient<InternalAloanCntrNoBrnArgBean, InternalAloanCntrNoBrnResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/ods/account/getAloanCntrNoBrn";
	}

}
