package com.megabank.olp.client.sender.micro.apply.management.house;

import java.util.List;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialReassignBranchBankResultBean;

@Component
public class HouseLoanTrialReassignBranchBankClient extends BaseApplyClient<HouseLoanTrialArgBean, List<HouseLoanTrialReassignBranchBankResultBean>>
{
	@Override
	protected Class<?> getBeanClassByCollection()
	{
		return HouseLoanTrialReassignBranchBankResultBean.class;
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/management/houseloantrial/getReassignBranchBank";
	}
}
