package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.common.persistence.pojo.code.CodeTemplateType;

/**
 * The LandingTemplate is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_template" )
public class LandingTemplate extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_template";

	public static final String TEMPLATE_ID_CONSTANT = "templateId";

	public static final String CODE_TEMPLATE_TYPE_CONSTANT = "codeTemplateType";

	public static final String NAME_CONSTANT = "name";

	public static final String TEMPLATE_DESC_CONSTANT = "templateDesc";

	public static final String LANDING_SOLUTIONS_CONSTANT = "landingSolutions";

	public static final String LANDING_TEMPLATE_COLUMNS_CONSTANT = "landingTemplateColumns";

	private Long templateId;

	private transient CodeTemplateType codeTemplateType;

	private String name;

	private String templateDesc;

	private transient Set<LandingSolution> landingSolutions = new HashSet<>( 0 );

	private transient Set<LandingTemplateColumn> landingTemplateColumns = new HashSet<>( 0 );

	public LandingTemplate()
	{}

	public LandingTemplate( CodeTemplateType codeTemplateType, String name, String templateDesc )
	{
		this.codeTemplateType = codeTemplateType;
		this.name = name;
		this.templateDesc = templateDesc;
	}

	public LandingTemplate( Long templateId )
	{
		this.templateId = templateId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "template_type", nullable = false )
	public CodeTemplateType getCodeTemplateType()
	{
		return codeTemplateType;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingTemplate" )
	public Set<LandingSolution> getLandingSolutions()
	{
		return landingSolutions;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingTemplate" )
	public Set<LandingTemplateColumn> getLandingTemplateColumns()
	{
		return landingTemplateColumns;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Column( name = "template_desc", nullable = false )
	public String getTemplateDesc()
	{
		return templateDesc;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "template_id", unique = true, nullable = false )
	public Long getTemplateId()
	{
		return templateId;
	}

	public void setCodeTemplateType( CodeTemplateType codeTemplateType )
	{
		this.codeTemplateType = codeTemplateType;
	}

	public void setLandingSolutions( Set<LandingSolution> landingSolutions )
	{
		this.landingSolutions = landingSolutions;
	}

	public void setLandingTemplateColumns( Set<LandingTemplateColumn> landingTemplateColumns )
	{
		this.landingTemplateColumns = landingTemplateColumns;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setTemplateDesc( String templateDesc )
	{
		this.templateDesc = templateDesc;
	}

	public void setTemplateId( Long templateId )
	{
		this.templateId = templateId;
	}
}