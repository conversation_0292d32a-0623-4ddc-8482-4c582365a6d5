package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeGuarantyReason is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_guaranty_reason" )
public class CodeGuarantyReason extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_guaranty_reason";

	public static final String GUARANTY_REASON_CODE_CONSTANT = "guarantyReasonCode";

	public static final String NAME_CONSTANT = "name";

	private String guarantyReasonCode;

	private String name;

	public CodeGuarantyReason()
	{}

	public CodeGuarantyReason( String guarantyReasonCode )
	{
		this.guarantyReasonCode = guarantyReasonCode;
	}

	@Id
	@Column( name = "guaranty_reason_code", unique = true, nullable = false, length = 20 )
	public String getGuarantyReasonCode()
	{
		return guarantyReasonCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setGuarantyReasonCode( String guarantyReasonCode )
	{
		this.guarantyReasonCode = guarantyReasonCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}