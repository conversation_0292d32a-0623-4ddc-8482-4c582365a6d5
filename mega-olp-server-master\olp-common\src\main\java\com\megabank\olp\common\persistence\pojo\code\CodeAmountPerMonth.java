package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeAmountPerMonth is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_amount_per_month" )
public class CodeAmountPerMonth extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_amount_per_month";

	public static final String AMOUNT_PER_MONTH_CODE_CONSTANT = "amountPerMonthCode";

	public static final String NAME_CONSTANT = "name";

	private String amountPerMonthCode;

	private String name;

	public CodeAmountPerMonth()
	{}

	public CodeAmountPerMonth( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	@Id
	@Column( name = "amount_per_month_code", unique = true, nullable = false, length = 20 )
	public String getAmountPerMonthCode()
	{
		return amountPerMonthCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setAmountPerMonthCode( String amountPerMonthCode )
	{
		this.amountPerMonthCode = amountPerMonthCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}