<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <properties>
        <maven.compiler.source>1.6</maven.compiler.source>
        <maven.compiler.target>1.6</maven.compiler.target>
        <jdk.min.version>1.6</jdk.min.version>
    </properties>

    <parent>
        <groupId>org.jboss</groupId>
        <artifactId>jboss-parent</artifactId>
        <version>12</version>
    </parent>
    <groupId>org.jboss</groupId>
    <artifactId>jandex</artifactId>
    <version>2.2.3.Final</version>
    <name>Java Annotation Indexer</name>
    <packaging>bundle</packaging>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <debug>true</debug>
                    <source>1.6</source>
                    <target>1.6</target>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <doclet>org.jboss.apiviz.APIviz</doclet>
                    <docletArtifact>
                        <groupId>org.jboss.apiviz</groupId>
                        <artifactId>apiviz</artifactId>
                        <version>1.3.2.GA</version>
                    </docletArtifact>
                    <useStandardDocletOptions>true</useStandardDocletOptions>
                    <charset>UTF-8</charset>
                    <encoding>UTF-8</encoding>
                    <docencoding>UTF-8</docencoding>
                    <breakiterator>true</breakiterator>
                    <version>true</version>
                    <author>true</author>
                    <keywords>true</keywords>
                    <additionalparam>
                        -sourceclasspath ${project.build.outputDirectory}
                    </additionalparam>
                    <docfilessubdirs>true</docfilessubdirs>
                    <top><![CDATA[
                         <script src="doc-files/addSyntax.js" type="text/javascript"></script>
                         <script src="doc-files/shCore.js" type="text/javascript"></script>
                         <script src="doc-files/shBrushJava.js" type="text/javascript"></script>
                    ]]></top>
                    <footer><![CDATA[
                         <script type="text/javascript">
                           SyntaxHighlighter.defaults["auto-links"] = false;
                           SyntaxHighlighter.defaults["tab-size"] = 2;
                           SyntaxHighlighter.defaults["toolbar"] = false;
                           SyntaxHighlighter.all();
                         </script>
                    ]]></footer>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.3.7</version>
                <extensions>true</extensions>
                <configuration>
                    <archive>
                        <index>true</index>
                        <manifest>
                            <mainClass>org.jboss.jandex.Main</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Automatic-Module-Name>org.jboss.jandex</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                    <instructions>
                        <Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
                        <Bundle-Version>${project.version}</Bundle-Version>
                        <Export-Package>
                            org.jboss.jandex;version="${project.version}"
                        </Export-Package>
                        <Import-Package>
                            org.apache.tools.ant;resolution:=optional,
                            org.apache.tools.ant.types;resolution:=optional,
                            *
                        </Import-Package>
                        <!-- Without the -nouses BND directive, the org.jboss.jandex export will get the optional
                        ant packages in a "uses" clause. -->
                        <_nouses>true</_nouses>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <type>jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.jandex</groupId>
            <artifactId>typeannotation-test</artifactId>
            <version>1.6</version>
            <type>jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.9.15</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.10.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>jboss-public-repository</id>
            <name>JBoss Repository</name>
            <url>https://repository.jboss.org/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
