package com.megabank.olp.common.persistence.pojo.code;

import com.megabank.olp.base.bean.BaseBean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table( name = "code_case_source" )
public class CodeCaseSource extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_case_source";

	public static final String CASE_SOURCE_CODE_CONSTANT = "caseSourceCode";

	public static final String NAME_CONSTANT = "name";

	public static final String DISPLAY_ORDER_CONSTANT = "displayOrder";

	private String caseSourceCode;

	private String name;

	private int displayOrder;

	public CodeCaseSource()
	{}

	public CodeCaseSource(String caseSourceCode )
	{
		this.caseSourceCode = caseSourceCode;
	}

	@Column( name = "display_order", nullable = false, precision = 5, scale = 0 )
	public int getDisplayOrder()
	{
		return displayOrder;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "case_source_code", unique = true, nullable = false, length = 20 )
	public String getCaseSourceCode()
	{
		return caseSourceCode;
	}

	public void setDisplayOrder( int displayOrder )
	{
		this.displayOrder = displayOrder;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setCaseSourceCode( String caseSourceCode )
	{
		this.caseSourceCode = caseSourceCode;
	}
}