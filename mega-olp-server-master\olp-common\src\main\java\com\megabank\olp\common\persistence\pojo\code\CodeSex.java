package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeSex is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_sex" )
public class CodeSex extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_sex";

	public static final String SEX_CODE_CONSTANT = "sexCode";

	public static final String NAME_CONSTANT = "name";

	private String sexCode;

	private String name;

	public CodeSex()
	{}

	public CodeSex( String sexCode )
	{
		this.sexCode = sexCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "sex_code", unique = true, nullable = false, length = 20 )
	public String getSexCode()
	{
		return sexCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setSexCode( String sexCode )
	{
		this.sexCode = sexCode;
	}
}