package com.megabank.olp.common.persistence.dao.generated.code;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeNonPrivateUsageSubType;

/**
 * The CodeNonPrivateUsageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNonPrivateUsageSubTypeDAO extends BasePojoDAO<CodeNonPrivateUsageSubType, String>
{
	@Override
	protected Class<CodeNonPrivateUsageSubType> getPojoClass()
	{
		return CodeNonPrivateUsageSubType.class;
	}
}
