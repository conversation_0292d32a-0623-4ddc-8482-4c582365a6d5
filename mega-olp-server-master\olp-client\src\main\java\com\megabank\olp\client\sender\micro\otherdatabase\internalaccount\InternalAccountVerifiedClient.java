package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalAccountVerifiedArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalAccountVerifiedResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class InternalAccountVerifiedClient extends BaseOtherDatabaseClient<InternalAccountVerifiedArgBean, InternalAccountVerifiedResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/ods/account/verify";
	}

}
