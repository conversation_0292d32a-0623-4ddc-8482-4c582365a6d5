/**
 *
 */
package com.megabank.olp.client.sender.credit.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@XmlAccessorType( XmlAccessType.FIELD )
public class AuthInfoBean extends BaseBean
{
	@XmlElement( name = "DATAA" )
	private String dataA;

	@XmlElement( name = "DATAB" )
	private String dataB;

	@XmlElement( name = "DATAC" )
	private String dataC;

	public String getDataA()
	{
		return dataA;
	}

	public String getDataB()
	{
		return dataB;
	}

	public String getDataC()
	{
		return dataC;
	}

	public void setDataA( String dataA )
	{
		this.dataA = dataA;
	}

	public void setDataB( String dataB )
	{
		this.dataB = dataB;
	}

	public void setDataC( String dataC )
	{
		this.dataC = dataC;
	}

}
