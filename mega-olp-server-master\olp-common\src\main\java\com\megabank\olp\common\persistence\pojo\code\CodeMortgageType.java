package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeMortgageType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_mortgage_type" )
public class CodeMortgageType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_mortgage_type";

	public static final String MORTGAGE_TYPE_CONSTANT = "mortgageType";

	public static final String NAME_CONSTANT = "name";

	private String mortgageType;

	private String name;

	public CodeMortgageType()
	{}

	public CodeMortgageType( String mortgageType )
	{
		this.mortgageType = mortgageType;
	}

	@Id
	@Column( name = "mortgage_type", unique = true, nullable = false, length = 20 )
	public String getMortgageType()
	{
		return mortgageType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setMortgageType( String mortgageType )
	{
		this.mortgageType = mortgageType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}