package com.megabank.olp.client.utility.enums;

import com.megabank.olp.base.enums.ErrorEnum;

public enum ClientOtpErrorEnum implements ErrorEnum
{
	/**
	 * 產生簡訊
	 */
	GETTED_SMS_COMMON( "01001" ),

	/**
	 * 驗證簡訊
	 */
	VARIFIED_SMS_COMMON( "02001" ),

	/**
	 * 驗證過期
	 */
	VARIFIED_SMS_EXPIRED( "02002" ),

	/**
	 * 已驗證
	 */
	VARIFIED_SMS_HAS_BEEN( "02003" );

	private String errorCode;

	private ClientOtpErrorEnum( String errorCode )
	{
		this.errorCode = errorCode;
	}

	@Override
	public String getCode()
	{
		return "CLIENT-OTP" + errorCode;
	}
}
