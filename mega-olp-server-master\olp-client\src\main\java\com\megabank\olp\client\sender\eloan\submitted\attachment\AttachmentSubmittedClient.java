package com.megabank.olp.client.sender.eloan.submitted.attachment;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedClient;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;

@Component
public class AttachmentSubmittedClient extends EloanSubmittedClient<AttachmentSubmittedArgBean>
{
	@Override
	protected String getLogForRequestBody( String requestBody )
	{
		return StringUtils.replaceAll( requestBody, "\"fileContent\":\".*\",\"serviceType\"", "\"fileContent\":\"...\",\"serviceType\"" );
	}

	@Override
	protected String getSimulatorCode( AttachmentSubmittedArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/gw-web/pLoan/func/applyAttch";
	}

}
