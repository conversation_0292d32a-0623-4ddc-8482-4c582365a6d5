package com.megabank.olp.common.persistence.pojo.code;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

@Entity
@Table( name = "code_emp_info" )
public class CodeEmpInfo extends BaseBean
{
	public static final String EMP_INFO_ID = "empInfoId";

	public static final String EMP_ID = "empId";

	public static final String BRANCH_BANK_CODE = "branchBankCode";

	public static final String SUB_BRANCH_BANK_CODE = "subBranchBankCode";

	public static final String UPDATED_DATE_CONSTANT = "updatedDate";

	public static final String CREATED_DATE_CONSTANT = "createdDate";

	private Long empInfoId;

	private String empId;

	private String branchBankCode;

	private String subBranchBankCode;

	private Date updatedDate;

	private Date createdDate;

	public CodeEmpInfo()
	{}

	@Column( name = "branch_bank_code", nullable = false )
	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "emp_id", unique = true, nullable = false )
	public String getEmpId()
	{
		return empId;
	}

	@Column( name = "emp_info_id", nullable = false )
	public Long getEmpInfoId()
	{
		return empInfoId;
	}

	@Column( name = "sub_branch_bank_code", nullable = true )
	public String getSubBranchBankCode()
	{
		return subBranchBankCode;
	}

	@Column( name = "updated_date" )
	public Date getUpdatedDate()
	{
		return updatedDate;
	}

	@Column( name = "created_date" )
	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setEmpId( String empId )
	{
		this.empId = empId;
	}

	public void setEmpInfoId( Long empInfoId )
	{
		this.empInfoId = empInfoId;
	}

	public void setSubBranchBankCode( String subBranchBankCode )
	{
		this.subBranchBankCode = subBranchBankCode;
	}

	public void setUpdatedDate( Date updatedDate )
	{
		this.updatedDate = updatedDate;
	}

}
