package com.megabank.olp.client.sender;

import java.io.IOException;
import java.util.Collection;

import jakarta.xml.bind.JAXBException;

import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.MediaType;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public abstract class BaseJsonClient<TArg, TReq, TRes, TResult> extends BaseClient<TArg, TReq, TRes, TResult>
{
	private final Class resClass;

	@Autowired
	@Qualifier( "clientObjectMapper" )
	protected ObjectMapper mapper;

	public BaseJsonClient()
	{
		resClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseJsonClient.class )[ 2 ];
	}

	protected Class<?> getBeanClassByCollection()
	{
		throw new NotImplementedException( "you need to implement the getBeanClassByCollection() when response is collection" );
	}

	@Override
	protected String getContentType()
	{
		return MediaType.APPLICATION_JSON_VALUE;
	}

	@Override
	protected String transReq2ReqBody( TReq reqBean ) throws IOException, JAXBException
	{
		return mapper.writeValueAsString( reqBean );
	}

	@Override
	protected TRes transResBody2Res( String resBody ) throws IOException
	{
		if( Collection.class.isAssignableFrom( resClass ) )
			return mapper.readValue( resBody, mapper.getTypeFactory().constructCollectionType( resClass, getBeanClassByCollection() ) );

		return ( TRes )mapper.readValue( resBody, resClass );
	}

}
