package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlRootElement( name = "NotificationXML" )
@XmlAccessorType( XmlAccessType.FIELD )
public class BillhunterSenderResBean extends BaseBean
{
	@XmlElement( name = "NotificationInfo" )
	private ResInfoBean resInfoBean;

	public BillhunterSenderResBean()
	{}

	public ResInfoBean getResInfoBean()
	{
		return resInfoBean;
	}

	public void setResInfoBean( ResInfoBean resInfoBean )
	{
		this.resInfoBean = resInfoBean;
	}

}
