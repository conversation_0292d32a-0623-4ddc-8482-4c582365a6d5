package com.megabank.olp.common.persistence.dao.generated.code;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeJobType;

/**
 * The CodeJobTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeJobTypeDAO extends BasePojoDAO<CodeJobType, String>
{
	public CodeJobType read( String jobType )
	{
		Validate.notNull( jobType );

		return getPojoByPK( jobType, CodeJobType.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeJobType> getPojoClass()
	{
		return CodeJobType.class;
	}
}
