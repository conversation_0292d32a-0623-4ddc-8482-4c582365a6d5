<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind.mvn</groupId>
        <artifactId>jaxb-runtime-parent</artifactId>
        <version>2.3.2</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>jaxb-runtime</artifactId>

    <packaging>jar</packaging>
    <name>JAXB Runtime</name>
    <description>JAXB (JSR 222) Reference Implementation</description>

    <properties>
        <findbugs.exclude>${project.basedir}/exclude-runtime.xml</findbugs.exclude>
    </properties>

    <dependencies>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>txw2</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.istack</groupId>
            <artifactId>istack-commons-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jvnet.staxex</groupId>
            <artifactId>stax-ex</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.fastinfoset</groupId>
            <artifactId>FastInfoset</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <workingDirectory>target/test-out</workingDirectory>
                    <forkCount>1</forkCount>
                    <reuseForks>true</reuseForks>
                    <argLine>
                        --add-opens com.sun.xml.bind/com.sun.xml.bind.v2=java.xml.bind
                        --add-opens com.sun.xml.bind/com.sun.xml.bind.v2.schemagen=java.xml.bind
                    </argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>prepare-module-path</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${module.path}</outputDirectory>
                            <silent>false</silent>
                            <includeArtifactIds>txw2,istack-commons-runtime,stax-ex,FastInfoset,jakarta.xml.bind-api</includeArtifactIds>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <configuration>
                            <source>${upper.java.level}</source>
                            <target>${upper.java.level}</target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>${base.java.level}</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <additionalJOptions>
                        <additionalJOption>--add-modules</additionalJOption>
                        <additionalJOption>jakarta.activation</additionalJOption>
                        <additionalJOption>--module-path</additionalJOption>
                        <additionalJOption>${module.path}</additionalJOption>
                    </additionalJOptions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
