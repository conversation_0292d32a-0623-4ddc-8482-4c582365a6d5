package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;
import com.megabank.olp.common.persistence.pojo.landing.LandingTemplate;

/**
 * The CodeTemplateType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_template_type" )
public class CodeTemplateType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_template_type";

	public static final String TEMPLATE_TYPE_CONSTANT = "templateType";

	public static final String NAME_CONSTANT = "name";

	public static final String LANDING_TEMPLATES_CONSTANT = "landingTemplates";

	private String templateType;

	private String name;

	private transient Set<LandingTemplate> landingTemplates = new HashSet<>( 0 );

	public CodeTemplateType()
	{}

	public CodeTemplateType( String templateType )
	{
		this.templateType = templateType;
	}

	public CodeTemplateType( String templateType, String name )
	{
		this.templateType = templateType;
		this.name = name;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeTemplateType" )
	public Set<LandingTemplate> getLandingTemplates()
	{
		return landingTemplates;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "template_type", unique = true, nullable = false, length = 20 )
	public String getTemplateType()
	{
		return templateType;
	}

	public void setLandingTemplates( Set<LandingTemplate> landingTemplates )
	{
		this.landingTemplates = landingTemplates;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setTemplateType( String templateType )
	{
		this.templateType = templateType;
	}
}