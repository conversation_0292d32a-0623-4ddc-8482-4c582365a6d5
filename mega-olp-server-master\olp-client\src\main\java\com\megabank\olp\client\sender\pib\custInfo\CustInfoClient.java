/**
 *
 */
package com.megabank.olp.client.sender.pib.custInfo;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.pib.BasePibClient;
import com.megabank.olp.client.sender.pib.custInfo.bean.PibCustInfoArgBean;
import com.megabank.olp.client.sender.pib.custInfo.bean.PibCustInfoReqBean;
import com.megabank.olp.client.sender.pib.custInfo.bean.PibCustInfoResBean;
import com.megabank.olp.client.sender.pib.custInfo.bean.PibCustInfoResultBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class CustInfoClient extends BasePibClient<PibCustInfoArgBean, PibCustInfoReqBean, PibCustInfoResBean, PibCustInfoResultBean>
{
	private Date transRocString2Date( String value )
	{
		Date rocDate = CommonDateStringUtils.transString2Date( value, "TTTMMdd" );
		Calendar rocCalendar = DateUtils.toCalendar( rocDate );

		return rocCalendar.getTime();
	}

	@Override
	protected void doReqHeaders( HttpHeaders httpHeaders, PibCustInfoArgBean argBean )
	{
		httpHeaders.add( HttpHeaders.AUTHORIZATION, "Bearer " + argBean.getAccessToken() );
		httpHeaders.add( "x-auth-token", argBean.getSessionId() );
	}

	@Override
	protected String getSimulatorCode( PibCustInfoArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/openAPI/v1.0.0/CustInfo";
	}

	@Override
	protected PibCustInfoReqBean transArg2Req( PibCustInfoArgBean argBean )
	{
		PibCustInfoReqBean reqBean = new PibCustInfoReqBean();
		reqBean.setClientIp( argBean.getClientIp() );
		reqBean.setTrackingIxd( argBean.getTrackingIxd() );

		return reqBean;
	}

	@Override
	protected PibCustInfoResultBean transRes2Result( PibCustInfoResBean resBean, HttpHeaders httpHeaders )
	{
		PibCustInfoResultBean resultBean = new PibCustInfoResultBean();
		resultBean.setSys( resBean.getSys() );
		resultBean.setResource( resBean.getResource() );
		resultBean.setCode( resBean.getReturnCode() );
		resultBean.setDesc( resBean.getReturnDesc() );
		resultBean.setClientTime( resBean.getResponseDate() );

		if( resBean.getPibCustInfoBean() != null )
		{
			resultBean.setIdNo( resBean.getPibCustInfoBean().getIdNo() );
			resultBean.setBirthDate( transRocString2Date( resBean.getPibCustInfoBean().getBirthDate() ) );
			resultBean.setMobileNumber( resBean.getPibCustInfoBean().getMobileNumber() );
		}

		return resultBean;
	}
}
