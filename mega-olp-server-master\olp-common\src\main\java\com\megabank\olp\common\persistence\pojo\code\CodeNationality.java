package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeNationality is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_nationality" )
public class CodeNationality extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_nationality";

	public static final String NATIONALITY_CODE_CONSTANT = "nationalityCode";

	public static final String NAME_CONSTANT = "name";

	private String nationalityCode;

	private String name;

	public CodeNationality()
	{}

	public CodeNationality( String nationalityCode )
	{
		this.nationalityCode = nationalityCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "nationality_code", unique = true, nullable = false, length = 20 )
	public String getNationalityCode()
	{
		return nationalityCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNationalityCode( String nationalityCode )
	{
		this.nationalityCode = nationalityCode;
	}
}