package com.megabank.olp.client.service.mydata;

import java.util.UUID;

import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileResultBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ArgBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ResultBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.ResultsBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "dev" } )
public class MyDataSenderServiceMockImp implements MyDataSenderService
{

	@Override
	public String getTxId( String serviceId )
	{
		return UUID.randomUUID().toString();
	}

	@Override
	public MyDataFileResultBean getUserData( String serviceId, String txId )
	{
		MyDataFileResultBean resultBean = new MyDataFileResultBean();
		resultBean.setErrorCode( "0000" );
		resultBean.setWaitSec( 0 );

		return resultBean;
	}

	@Override
	public boolean reportMyData( String serviceId, String txId )
	{
		return true;
	}
	
	@Override
	public MyDataType1ResultBean sendMyDataType1( MyDataType1ArgBean argBean )
	{
		MyDataType1ResultBean myDataType1ResultBean = new MyDataType1ResultBean();
		myDataType1ResultBean.setStatus( HttpStatus.OK.value() );
		myDataType1ResultBean.setMsg( "Success" );
		ResultsBean resultBean = new ResultsBean();
		resultBean.setTxId(argBean.getTransactionId());
		resultBean.setRedirectUrl( "http://localhost:3000/#/myDataFinished?checkMyDataAuth=1&transactionId="
				+ argBean.getTransactionId());
		myDataType1ResultBean.setResults(resultBean);
		return myDataType1ResultBean;
	}


}
