package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.otherdatabase.BaseOtherDatabaseClient;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalIxmlVerifiedArgBean;
import com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean.InternalIxmlVerifiedResultBean;

@Component
public class InternalIxmlVerifiedClient extends BaseOtherDatabaseClient<InternalIxmlVerifiedArgBean, InternalIxmlVerifiedResultBean>
{

	@Override
	protected String getSuffixUrl()
	{
		return "/ods/account/isUserHaveIxmlPermission";
	}

}