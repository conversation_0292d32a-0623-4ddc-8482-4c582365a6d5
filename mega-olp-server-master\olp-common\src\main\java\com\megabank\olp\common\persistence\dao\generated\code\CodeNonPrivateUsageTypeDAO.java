package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeNonPrivateUsageType;

import org.springframework.stereotype.Repository;

/**
 * The CodeNonPrivateUsageTypeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeNonPrivateUsageTypeDAO extends BasePojoDAO<CodeNonPrivateUsageType, String>
{
	@Override
	protected Class<CodeNonPrivateUsageType> getPojoClass()
	{
		return CodeNonPrivateUsageType.class;
	}
}
