package com.megabank.olp.client.sender.sms;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

import org.apache.commons.io.IOUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.BaseXmlClient;
import com.megabank.olp.client.sender.sms.bean.ItemBean;
import com.megabank.olp.client.sender.sms.bean.RequestBean;
import com.megabank.olp.client.sender.sms.bean.RequestBodyBean;
import com.megabank.olp.client.sender.sms.bean.SmsSenderArgBean;
import com.megabank.olp.client.sender.sms.bean.SmsSenderReqBean;
import com.megabank.olp.client.sender.sms.bean.SmsSenderResBean;
import com.megabank.olp.client.sender.sms.bean.SmsSenderResultBean;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;

@Component
public class SmsSenderClient extends BaseXmlClient<SmsSenderArgBean, SmsSenderReqBean, SmsSenderResBean, SmsSenderResultBean>
{

	@Override
	protected void doReqHeaders( HttpHeaders httpHeaders, SmsSenderArgBean argBean )
	{
		httpHeaders.set( "SOAPAction", "\"http://HiB2B/webservices/aMsgToHiB2B\"" );
	}

	@Override
	protected URI getApiURI( SmsSenderArgBean argBean ) throws UnsupportedEncodingException
	{
		return URI.create( propertyBean.getApiUrlThirdpartySms() );
	}

	@Override
	protected String getContentType()
	{
		return "text/xml; charset=utf-8";
	}

	@Override
	protected String getSimulatorCode( SmsSenderArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSystemName()
	{
		return "sms";
	}

	@Override
	protected SmsSenderReqBean transArg2Req( SmsSenderArgBean argBean )
	{
		String msgNo = new SimpleDateFormat( "yyyyMMddHHmmss" ).format( new Date() ) + argBean.getContractNo();

		ItemBean itemBean = new ItemBean();
		itemBean.setPhoneNo( argBean.getMobileNumber() );
		itemBean.setMsgContent( argBean.getMessage() );
		itemBean.setMsgNo( msgNo );
		itemBean.setCustomerId( argBean.getIdNo() );
		itemBean.setBranchNo( argBean.getBranchBankCode() );

		RequestBean requestBean = new RequestBean();
		requestBean.setItemBean( itemBean );

		RequestBodyBean requestBodyBean = new RequestBodyBean();
		requestBodyBean.setRequestBean( requestBean );

		SmsSenderReqBean reqBean = new SmsSenderReqBean();
		reqBean.setRequestBodyBean( requestBodyBean );

		return reqBean;
	}

	@Override
	protected SmsSenderResultBean transRes2Result( SmsSenderResBean resBean, HttpHeaders httpHeaders )
	{
		SmsSenderResultBean resultBean = new SmsSenderResultBean();
		resultBean.setSuccess( "1".equals( resBean.getResultBean().getErrCode() ) );

		return resultBean;
	}

	@Override
	protected SmsSenderResBean transResBody2Res( String resBody ) throws IOException, JAXBException, XMLStreamException
	{
		XMLInputFactory factory = XMLInputFactory.newFactory();
		factory.setProperty( XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, false );
		factory.setProperty( XMLInputFactory.SUPPORT_DTD, false );
		factory.setProperty( XMLInputFactory.IS_NAMESPACE_AWARE, false );

		// factory.setProperty( XMLConstants.ACCESS_EXTERNAL_DTD, "" );
		// factory.setProperty( XMLConstants.ACCESS_EXTERNAL_SCHEMA, "" );

		XMLStreamReader reader = factory
					.createXMLStreamReader( IOUtils.toInputStream( StringEscapeUtils.unescapeHtml4( resBody ), StandardCharsets.UTF_8 ) );

		int tagCount = 0;
		reader.nextTag();

		if( reader != null )
			while( !reader.getLocalName().equals( "aMsgToHiB2BResponse" ) )
		{
			reader.nextTag();
			tagCount++;

			if( tagCount > Integer.MAX_VALUE )
				break;
		}

		JAXBContext context = JAXBContext.newInstance( SmsSenderResBean.class );

		Unmarshaller unmarshaller = context.createUnmarshaller();

		JAXBElement<SmsSenderResBean> element = unmarshaller.unmarshal( reader, SmsSenderResBean.class );

		return element.getValue();
	}

}
