package com.megabank.olp.client.sender.mydata.webcomm.type1.bean;

import com.megabank.olp.client.sender.webcomm.mydata.WebCommMyDataType1ArgBean;

public class MyDataType1ArgBean extends WebCommMyDataType1ArgBean
{
	private String transactionId;
	
	private String personId;
	
	private String birthday;
	
	private String returnUrl;
	
	private String failReturnUrl;
	
	private String channelSource;
	
	private String bu;
	
	private String caseNumber;

	public MyDataType1ArgBean()
	{

	}

	public String getTransactionId() 
	{
		return transactionId;
	}

	public void setTransactionId(String transactionId) 
	{
		this.transactionId = transactionId;
	}

	public String getPersonId() 
	{
		return personId;
	}

	public void setPersonId(String personId) 
	{
		this.personId = personId;
	}

	public String getBirthday() 
	{
		return birthday;
	}

	public void setBirthday(String birthday) 
	{
		this.birthday = birthday;
	}

	public String getReturnUrl() 
	{
		return returnUrl;
	}

	public void setReturnUrl(String returnUrl) 
	{
		this.returnUrl = returnUrl;
	}

	public String getFailReturnUrl() 
	{
		return failReturnUrl;
	}

	public void setFailReturnUrl(String failReturnUrl) 
	{
		this.failReturnUrl = failReturnUrl;
	}

	public String getChannelSource() 
	{
		return channelSource;
	}

	public void setChannelSource(String channelSource) 
	{
		this.channelSource = channelSource;
	}

	public String getBu() 
	{
		return bu;
	}

	public void setBu(String bu) 
	{
		this.bu = bu;
	}
	
	public String getCaseNumber() 
	{
		return caseNumber;
	}

	public void setCaseNumber(String caseNumber) 
	{
		this.caseNumber = caseNumber;
	}

}
