package com.megabank.olp.common.utility.enums;

public enum ErrorMessageEnum
{

	/**
	 * 7日內重複申請
	 */
	APPLY_EXISTS_IN_7_DAYS( "/loan/apply/checkApplyCount", "您已完成申請", "親愛的兆豐客戶您好，您已於{apply-completed-date}送出申請，目前專人正在處理你的案件，請您耐心等候" ),

	/**
	 * 保證人申請查無案件
	 */
	NO_APPLY_RESULT( "/open/loan/apply/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，系統查無您的案件資料" ),

	/**
	 * 擔保品提供人查無案件
	 */
	NO_COLLATERAL_RESULT( "/open/loan/apply/collateral/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，系統查無您的案件資料" ),

	/**
	 * 線上補件查無案件
	 */
	NO_RESULT_TO_UPLOAD( "/open/loan/upload/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，您目前沒有需要補件的申請案件" ),

	/**
	 * 線上調閱查無案件
	 */
	NO_RESULT_TO_DOWNLOAD( "/open/loan/download/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，系統查無您的案件資料" ),

	/**
	 * 線上對保查無案件
	 */
	NO_SIGNING_CONTRACT_RESULT( "/open/loan/signingcontract/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，系統查無您的案件資料" ),

	NO_HOUSE_LOAN_SIGNING_CONTRACT_RESULT( "/open/loan/housesigningcontract/checkCaseExisted", "查無案件資料", "親愛的兆豐客戶您好，系統查無您的案件資料" ),

	/**
	 * 無效的行銷方案
	 */
	NOT_ACTIVE_LOAN_PLAN( "/open/loan/apply/checkLoanPlan", "無效的行銷方案", "行銷方案無法進行申請" ),
	/**
	 * 無認證選單
	 */
	NO_AVAILABLE_IDENTITY( "/open/auth/getIdentityType", "無法進行身份認證", "親愛的兆豐客戶您好，由於您需要使用自然人憑證進行申請，請你使用桌電再次進行申請" ),

	/**
	 * 無法提供保證人申請
	 */
	NO_AVAILABLE_GUARANTOR_APPLICATION( "/open/loan/apply/checkGuarantorAvailable", "無法提供保證人申請", "親愛的顧客您好，本案件無法提供保證人申請，謝謝。" ),

	/**
	 * 30日內有案件在途
	 */
	APPLY_EXISTS_IN_30_DAYS( "/loan/apply/create", "您已完成申請", "親愛的兆豐客戶您好，您已於{apply-completed-date}送出申請，目前專人正在處理你的案件，請您耐心等候" );

	private String context;

	private String title;

	private String content;

	/*
	 * curl -X POST -H "Content-type: application/json" -d "{\"url\":\"/open/loan/apply/checkLoanPlan\"}"
	 * http://127.0.0.1:9030/open/message/getErrorMessage
	 */
	private ErrorMessageEnum( String context, String title, String content )
	{
		this.context = context;
		this.title = title;
		this.content = content;
	}

	public String getContent()
	{
		return content;
	}

	public String getContext()
	{
		return context;
	}

	public String getTitle()
	{
		return title;
	}
}
