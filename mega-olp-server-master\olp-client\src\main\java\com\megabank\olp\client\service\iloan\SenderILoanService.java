package com.megabank.olp.client.service.iloan;

import java.util.Date;

import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusResultBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.mydata.bean.MyDataSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;
import com.megabank.olp.client.sender.iloan.getted.caseDetailStatus.bean.ILoanCaseDetailStatusResultBean;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.bean.PersonalLoanApplySubmittedIloanArgBean;

public interface SenderILoanService
{

	public EloanCaseStatusResultBean getCaseStatusList( String idNo, Date birthDate );

	public EloanCustInfoResultBean getILoanCustInfo( String idNo, Date birthDate );

	public EloanSubmittedResultBean submitAttachment( AttachmentSubmittedArgBean argBean );

	public EloanSubmittedResultBean submitPersonalLoanApply( PersonalLoanApplySubmittedIloanArgBean argBean );

	public EloanSubmittedResultBean submitMyData( MyDataSubmittedArgBean argBean );

	public EloanSubmittedResultBean submitSigningContract( SigningContractCompletedArgBean argBean );

	public ILoanCaseDetailStatusResultBean getCaseDetailStatus( String caseNo );

}
