package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.enums.OrderEnum;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeSex;

/**
 * The CodeSexDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeSexDAO extends BasePojoDAO<CodeSex, String>
{

	public List<CodeSex> getAllPojosOrderByDesc()
	{
		OrderBean orderBean = new OrderBean( CodeSex.SEX_CODE_CONSTANT, OrderEnum.DESCEND );

		return this.getAllPojosOrderBy( orderBean );
	}

	@Override
	protected Class<CodeSex> getPojoClass()
	{
		return CodeSex.class;
	}
}
