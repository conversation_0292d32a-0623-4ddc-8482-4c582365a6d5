/**
 *
 */
package com.megabank.olp.client.sender.micro.apply.mydata.bean;

import com.megabank.olp.base.bean.BaseBean;

public class MyDataTransferArgBean extends BaseBean
{
	private String transactionId;

	private String tx_id;

	private String file;

	private String status;

	public MyDataTransferArgBean()
	{
		// default constructor
	}

	public String getTransactionId()
	{
		return transactionId;
	}

	public void setTransactionId(String transactionId)
	{
		this.transactionId = transactionId;
	}

	public String getTx_id() 
	{
		return tx_id;
	}

	public void setTx_id(String tx_id) 
	{
		this.tx_id = tx_id;
	}

	public String getFile()
	{
		return file;
	}

	public void setFile(String file)
	{
		this.file = file;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

}
