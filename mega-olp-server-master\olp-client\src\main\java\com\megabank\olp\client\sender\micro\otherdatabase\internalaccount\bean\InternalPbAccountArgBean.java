package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company megabank
 * @copyright Copyright (c) 2020
 */
public class InternalPbAccountArgBean extends BaseBean
{
	private String idNo;

	private Date birthDate;

	private String include_currTWD;

	private String include_currNotTWD;

	private String include_digitalAccount_promoted;

	private String include_digitalAccount_unpromoted;

	public InternalPbAccountArgBean()
	{}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getInclude_currNotTWD()
	{
		return include_currNotTWD;
	}

	public String getInclude_currTWD()
	{
		return include_currTWD;
	}

	public String getInclude_digitalAccount_promoted()
	{
		return include_digitalAccount_promoted;
	}

	public String getInclude_digitalAccount_unpromoted()
	{
		return include_digitalAccount_unpromoted;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setInclude_currNotTWD( String include_currNotTWD )
	{
		this.include_currNotTWD = include_currNotTWD;
	}

	public void setInclude_currTWD( String include_currTWD )
	{
		this.include_currTWD = include_currTWD;
	}

	public void setInclude_digitalAccount_promoted( String include_digitalAccount_promoted )
	{
		this.include_digitalAccount_promoted = include_digitalAccount_promoted;
	}

	public void setInclude_digitalAccount_unpromoted( String include_digitalAccount_unpromoted )
	{
		this.include_digitalAccount_unpromoted = include_digitalAccount_unpromoted;
	}

}
