package com.megabank.olp.client.sender.mydata;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.client.sender.mydata.txid.MyDataTxIdClient;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdArgBean;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdResultBean;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ClientServiceConfig.class, BaseServiceConfig.class, SystemConfig.class } )
public class MyDataTxIdClientIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private MyDataTxIdClient client;

	@Test
	public void send()
	{
		String serviceId = "01";

		MyDataTxIdArgBean argBean = new MyDataTxIdArgBean();
		argBean.setServiceId( serviceId );

		MyDataTxIdResultBean resultBean = client.send( argBean );

		logger.info( "resultBean:{}", resultBean );
	}
}
