package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeLoanPeriod is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_loan_period" )
public class CodeLoanPeriod extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_loan_period";

	public static final String LOAD_PERIOD_CODE_CONSTANT = "loadPeriodCode";

	public static final String NAME_CONSTANT = "name";

	private String loadPeriodCode;

	private String name;

	public CodeLoanPeriod()
	{}

	public CodeLoanPeriod( String loadPeriodCode )
	{
		this.loadPeriodCode = loadPeriodCode;
	}

	@Id
	@Column( name = "load_period_code", unique = true, nullable = false, length = 20 )
	public String getLoadPeriodCode()
	{
		return loadPeriodCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setLoadPeriodCode( String loadPeriodCode )
	{
		this.loadPeriodCode = loadPeriodCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}