package com.megabank.olp.common.utility.enums;

public enum CodeTypeEnum
{

	/**
	 * 本行帳戶預期月平均交易金額
	 */
	AMOUNT_PER_MONTH( "amount-per-month" ),

	/**
	 * 申請狀態
	 */
	APPLY_STATUS( "apply-status" ),

	/**
	 * 銀行
	 */
	BANK( "bank" ),

	/**
	 * 縣市
	 */
	CITY( "city" ),

	/**
	 * 房貸e把兆方便聯絡時間
	 */
	CALL_BACK_TIME( "call-back-time" ),

	/**
	 * 方便聯絡時間
	 */
	CONTACT_TIME( "contact-time" ),

	/**
	 * 契約通知方式
	 */
	CONTRACT_NOTIFICATION( "contract-notification" ),

	/**
	 * 學歷
	 */
	EDUCATION_LEVEL( "education-level" ),

	/**
	 * 行員申請信貸承辦分行
	 */
	EMP_PL_BRANCH_BANK( "emp-pl-branch-bank" ),

	/**
	 * 行員申請信貸承辦分行縣市
	 */
	EMP_PL_BRANCH_BANK_CITY( "emp-pl-branch-bank-city" ),

	/**
	 * 寬限期
	 */
	GRACE_PERIOD( "grace-period" ),

	/**
	 * 本案提徵保證人原因
	 */
	GUARANTY_REASON( "guaranty-reason" ),

	/**
	 * 房貸承辦分行
	 */
	HL_BRANCH_BANK( "hl-branch-bank" ),

	/**
	 * 房貸承辦分行縣市
	 */
	HL_BRANCH_BANK_CITY( "hl-branch-bank-city" ),

	/**
	 * 房貸借款期限
	 */
	HL_LOAN_PERIOD( "hl-loan-period" ),

	/**
	 * 房貸借款用途
	 */
	HL_LOAN_PURPOSE( "hl-loan-purpose" ),

	/**
	 * 房貸身分別
	 */
	HL_USER_TYPE( "hl-user-type" ),

	/**
	 * 不動產狀態
	 */
	HOUSE_STATUS( "house-status" ),

	/**
	 * 職業別
	 */
	JOB_TYPE( "job-type" ),

	/**
	 * 行銷管理頁操作類型
	 */
	LANDING_ACTION( "landing-action" ),

	/**
	 * 申請單類型
	 */
	LANDING_REQUEST_TYPE( "landing-request-type" ),

	/**
	 * 貸款類型
	 */
	LOAN_TYPE( "loan-type" ),

	/**
	 * 婚姻
	 */
	MARRIAGE_STATUS( "marriage-status" ),

	/**
	 * 本次房地產屬於
	 */
	MORTGAGE_TYPE( "mortgage-type" ),

	/**
	 * 國籍
	 */
	NATIONALITY( "nationality" ),

	/**
	 * 非自用住宅用途
	 */
	NON_PRIVATE_USAGE_TYPE( "non-private-usage-type" ),

	/**
	 * 非自用住宅、自住用途
	 */
	NON_PRIVATE_USAGE_SUBTYPE( "non-private-usage-subtype" ),

	/**
	 * 通知狀態
	 */
	NOTIFICATION_STATUS( "notification-status" ),

	/**
	 * 他行認證銀行
	 */
	OTHER_BANK( "other-bank" ),

	/**
	 * 信貸承辦分行
	 */
	PL_BRANCH_BANK( "pl-branch-bank" ),

	/**
	 * 信貸承辦分行縣市
	 */
	PL_BRANCH_BANK_CITY( "pl-branch-bank-city" ),

	/**
	 * 信貸借款期限
	 */
	PL_LOAN_PERIOD( "pl-loan-period" ),

	/**
	 * 信貸借款用途
	 */
	PL_LOAN_PURPOSE( "pl-loan-purpose" ),

	/**
	 * 信貸身分別
	 */
	PL_USER_TYPE( "pl-user-type" ),

	/**
	 * 自用住宅證明方式
	 */
	PRIVATE_USAGE_TYPE( "private-usage-type" ),

	/**
	 * 處理狀態
	 */
	PROCESS_STATUS( "process-status" ),

	/**
	 * 利息更動通知方式
	 */
	RATE_ADJUSTMENT_NOTIFICATION( "rate-adjustment-notification" ),

	/**
	 * 貸款利息收據通知方式
	 */
	RECEIPT_NOTIFICATION( "receipt-notification" ),

	/**
	 * 與借款人關係
	 */
	RELATION_BORROWER_TYPE( "relation-borrower-type" ),

	/**
	 * 與申貸人關係類型
	 */
	RELATION_TYPE( "relation-type" ),

	/**
	 * 負責人類別
	 */
	REPRESENTATIVE_TYPE( "representative-type" ),

	/**
	 * 現住房屋持有狀態
	 */
	RESIDENCE_STATUS( "residence-status" ),

	/**
	 * 行銷單位
	 */
	SERVICE_ASSOCIATE_DEPT( "service-associate-dept" ),

	/**
	 * 性別
	 */
	SEX( "sex" ),

	/**
	 * 職稱
	 */
	TITLE_TYPE( "title-type" ),

	/**
	 * 進件狀態
	 */
	TRANSMISSION_STATUS( "transmission-status" ),

	/**
	 * 上傳狀態
	 */
	UPLOAD_STATUS( "upload-status" ),

	/**
	 * eDDA他行認證銀行
	 */
	OTHER_BANK_EDDA( "other-bank-edda" ),

	/**
	 * eDDA他行認證銀行分行
	 */
	OTHER_BANK_EDDA_BRANCHCODE( "other-bank-edda-branchCode" ),

	/**
	 * 得知本行信貸服務的管道
	 */
	CASE_SOURCE( "case-source" ),

	/**
	 * 房貸e把兆貸款用途
	 */
	HOUSE_CONTACT_LOAN_PURPOSE( "house-contact-loan-purpose" );

	private String context;

	private CodeTypeEnum( String context )
	{
		this.context = context;
	}

	public String getContext()
	{
		return context;
	}
}
