package com.megabank.olp.client.sender.eloan.getted.collateral.bean;

import com.megabank.olp.base.bean.BaseBean;

public class EloanCollateralProviderResultBean extends BaseBean
{
	private String providerContractNo;

	private String providerName;

	private String borrowerName;

	private String branchCode;

	public EloanCollateralProviderResultBean()
	{}

	public String getBorrowerName()
	{
		return borrowerName;
	}

	public String getBranchCode()
	{
		return branchCode;
	}

	public String getProviderContractNo()
	{
		return providerContractNo;
	}

	public String getProviderName()
	{
		return providerName;
	}

	public void setBorrowerName( String borrowerName )
	{
		this.borrowerName = borrowerName;
	}

	public void setBranchCode( String branchCode )
	{
		this.branchCode = branchCode;
	}

	public void setProviderContractNo( String providerContractNo )
	{
		this.providerContractNo = providerContractNo;
	}

	public void setProviderName( String providerName )
	{
		this.providerName = providerName;
	}

}
