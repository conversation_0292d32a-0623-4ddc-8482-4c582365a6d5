package com.megabank.olp.client.sender;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

import org.apache.commons.io.IOUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public abstract class BaseXmlClient<TArg, TReq, TRes, TResult> extends BaseClient<TArg, TReq, TRes, TResult>
{
	private final Class reqClass;

	private final Class resClass;

	public BaseXmlClient()
	{
		reqClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseXmlClient.class )[ 1 ];
		resClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseXmlClient.class )[ 2 ];
	}

	/**
	 * 假如字串有大於或小於符號, 要加入 CDATA 的格式
	 *
	 * @param str
	 * @return
	 */
	protected String addCDATAIfNeed( String str )
	{
		if( str.contains( "<" ) || str.contains( ">" ) )
			return "@@@<![CDATA[" + str + "]]>@@@";

		return str;
	}

	@Override
	protected String getContentType()
	{
		return MediaType.TEXT_XML_VALUE;
	}

	@Override
	protected HttpMethod getHttpMethod()
	{
		return HttpMethod.POST;
	}

	@Override
	protected String transReq2ReqBody( TReq reqBean ) throws IOException, JAXBException
	{
		StringWriter writer = new StringWriter();

		JAXBContext context = JAXBContext.newInstance( reqClass );

		Marshaller marshaller = context.createMarshaller();

		marshaller.setProperty( Marshaller.JAXB_FORMATTED_OUTPUT, true );

		marshaller.marshal( reqBean, writer );

		String reqBody = writer.toString();

		Pattern pattern = Pattern.compile( "@@@(.*)@@@" );
		Matcher matcher = pattern.matcher( reqBody );

		while( matcher.find() )
		{
			reqBody = reqBody.replace( "&lt;", "<" );
			reqBody = reqBody.replace( "&gt;", ">" );
			reqBody = reqBody.replace( "@@@", "" );
		}

		return reqBody;

	}

	@Override
	protected TRes transResBody2Res( String resBody ) throws IOException, JAXBException, XMLStreamException
	{
		XMLInputFactory factory = XMLInputFactory.newFactory();
		factory.setProperty( XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, false );
		factory.setProperty( XMLInputFactory.SUPPORT_DTD, false );
		factory.setProperty( XMLInputFactory.IS_NAMESPACE_AWARE, false );

		// factory.setProperty( XMLConstants.ACCESS_EXTERNAL_DTD, "" );
		// factory.setProperty( XMLConstants.ACCESS_EXTERNAL_SCHEMA, "" );

		XMLStreamReader reader = factory
					.createXMLStreamReader( IOUtils.toInputStream( StringEscapeUtils.unescapeHtml4( resBody ), StandardCharsets.UTF_8 ) );

		JAXBContext context = JAXBContext.newInstance( resClass );
		Unmarshaller unmarshaller = context.createUnmarshaller();

		return ( TRes )unmarshaller.unmarshal( reader, resClass ).getValue();
	}
}
