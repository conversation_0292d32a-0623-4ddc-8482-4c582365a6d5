package com.megabank.olp.backend.controller.landing;

import java.util.Date;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.backend.controller.BaseBackendController;
import com.megabank.olp.backend.controller.landing.bean.landing.ApplyDateCheckedArgBean;
import com.megabank.olp.backend.controller.landing.bean.landing.TemplateColumnListGetterArgBean;
import com.megabank.olp.backend.controller.landing.bean.landing.TemplateListGetterArgBean;
import com.megabank.olp.backend.service.landing.LandingService;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@RestController
@RequestMapping( "landing" )
public class LandingController extends BaseBackendController
{
	@Autowired
	private LandingService landingService;

	/**
	 * 檢查上架日期是否可用
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "checkApplyDate" )
	public Map<String, Object> checkApplyDate( @RequestBody @Validated ApplyDateCheckedArgBean argBean )
	{
		Long templateId = argBean.getTemplateId();
		Date applyDate = argBean.getApplyDate();
		Long solutionId = argBean.getSolutionId();
		Long requestId = argBean.getRequestId();

		return getResponseMap( landingService.checkApplyDate( templateId, applyDate, solutionId, requestId ) );
	}

	/**
	 * 取得頁面模版內容
	 *
	 * @param argBean
	 * @return
	 */
	@PostMapping( "getTemplateColumnList" )
	public Map<String, Object> getTemplateColumnList( @RequestBody @Validated TemplateColumnListGetterArgBean argBean )
	{
		return getResponseMap( landingService.getTemplateColumnList( argBean.getTemplateId() ) );
	}

	/**
	 * 取得頁面模版列表
	 *
	 * @return
	 */
	@PostMapping( "getTemplateList" )
	public Map<String, Object> getTemplateList( @RequestBody @Validated TemplateListGetterArgBean argBean )
	{
		return getResponseMap( landingService.getTemplateList( argBean.getTemplateType() ) );
	}

}
