package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementRef;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "isConvertToPDFBean", "attachmentFileName", "contentType", "contentDisposition", "contentTransferEncoding" } )
public class AttachHeaderBean extends BaseBean
{

	/**
	 * 是否由BH協助轉為PDF,PDFVertical請填寫是否直橫印
	 */
	@XmlElementRef( name = "isConvertToPDF" )
	private IsConvertToPDFBean isConvertToPDFBean;

	/**
	 * 待傳客戶檔名,由BH轉PDF仍請填原始檔名
	 */
	@XmlElement( name = "AttachmentFileName" )
	private String attachmentFileName;

	@XmlElement( name = "ContentType" )
	private String contentType = "application/pdf; name=";

	@XmlElement( name = "ContentDisposition" )
	private String contentDisposition = "attachment; filename=";

	@XmlElement( name = "ContentTransferEncoding" )
	private String contentTransferEncoding = "base64";

	public AttachHeaderBean()
	{}

	public String getAttachmentFileName()
	{
		return attachmentFileName;
	}

	public String getContentDisposition()
	{
		return contentDisposition;
	}

	public String getContentTransferEncoding()
	{
		return contentTransferEncoding;
	}

	public String getContentType()
	{
		return contentType;
	}

	public IsConvertToPDFBean getIsConvertToPDFBean()
	{
		return isConvertToPDFBean;
	}

	public void setAttachmentFileName( String attachmentFileName )
	{
		this.attachmentFileName = attachmentFileName;
	}

	public void setContentDisposition( String contentDisposition )
	{
		this.contentDisposition = contentDisposition;
	}

	public void setContentTransferEncoding( String contentTransferEncoding )
	{
		this.contentTransferEncoding = contentTransferEncoding;
	}

	public void setContentType( String contentType )
	{
		this.contentType = contentType;
	}

	public void setIsConvertToPDFBean( IsConvertToPDFBean isConvertToPDFBean )
	{
		this.isConvertToPDFBean = isConvertToPDFBean;
	}

}
