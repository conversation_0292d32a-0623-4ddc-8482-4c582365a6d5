<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2018, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0 which is available at
    http://www.eclipse.org/legal/epl-2.0,
    or the Eclipse Distribution License v. 1.0 which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>jakarta.persistence</groupId>
    <artifactId>jakarta.persistence-api</artifactId>
    <version>2.2.3</version>

    <name>Jakarta Persistence API</name>
    <url>https://github.com/eclipse-ee4j/jpa-api</url>

    <scm>
        <connection>scm:git:git://github.com/eclipse-ee4j/jpa-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/jpa-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jpa-api.git</url>
        <tag>HEAD</tag>
    </scm>

    <licenses>
        <license>
            <name>Eclipse Public License v. 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
            <comments>Standard Eclipse Licence</comments>
        </license>
        <license>
            <name>Eclipse Distribution License v. 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
            <comments>Standard Eclipse Distribution License</comments>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>lukasj</id>
            <name>Lukas Jungmann</name>
            <organization>Oracle, Inc.</organization>
            <roles>
                <role>lead</role>
            </roles>
        </developer>
    </developers>

    <issueManagement>
        <system>IssueTracker</system>
        <url>https://github.com/eclipse-ee4j/jpa-api/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Community discussions</name>
            <subscribe>https://accounts.eclipse.org/mailing-list/jpa-dev</subscribe>
            <unsubscribe>https://accounts.eclipse.org/mailing-list/jpa-dev</unsubscribe>
            <post><EMAIL></post>
            <archive>https://dev.eclipse.org/mhonarc/lists/jpa-dev/</archive>
            <otherArchives>
                <otherArchive>http://dev.eclipse.org/mhonarc/lists/jpa-dev/maillist.rss</otherArchive>
            </otherArchives>
        </mailingList>
    </mailingLists>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <spec.non.final>false</spec.non.final>
        <spec.version>2.2</spec.version>
        <spec.build>01</spec.build>
        <spec.impl.version>2.2.3</spec.impl.version>
        <spec.api.package>jakarta.persistence</spec.api.package>
        <spec.new.spec.version>2.3</spec.new.spec.version>
        <legal.doc.source>${project.basedir}</legal.doc.source>
        <vendor.name>Eclipse Foundation</vendor.name>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>spec-version-maven-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-legal-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${legal.doc.source}</directory>
                                    <includes>
                                        <include>NOTICE.md</include>
                                        <include>LICENSE.md</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.8,)</version>
                        </requireJavaVersion>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>display-info</goal>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>spec-version-maven-plugin</artifactId>
                <configuration>
                    <specMode>jakarta</specMode>
                    <spec>
                        <nonFinal>${spec.non.final}</nonFinal>
                        <jarType>api</jarType>
                        <specVersion>${spec.version}</specVersion>
                        <specBuild>${spec.build}</specBuild>
                        <specImplVersion>${spec.impl.version}</specImplVersion>
                        <apiPackage>${spec.api.package}</apiPackage>
                        <newSpecVersion>${spec.new.spec.version}</newSpecVersion>
                    </spec>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>set-spec-properties</goal>
                            <!-- TODO:
                            glassfish-spec-version-maven-plugin needs to be updated
                            in order to check 'jakarta.' prefixed values in manifest entries
                            -->
                            <!--<goal>check-module</goal>-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgs>
                        <compilerArg>-Xlint:all</compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Automatic-Module-Name>java.persistence</Automatic-Module-Name>
                        <Bundle-Description>Jakarta Persistence ${spec.specification.version} API jar</Bundle-Description>
                        <Bundle-Name>Jakarta Persistence API jar</Bundle-Name>
                        <Bundle-SymbolicName>${spec.bundle.symbolic-name}</Bundle-SymbolicName>
                        <Bundle-Version>${spec.bundle.version}</Bundle-Version>
                        <Extension-Name>${spec.extension.name}</Extension-Name>
                        <Implementation-Version>${spec.implementation.version}</Implementation-Version>
                        <Specification-Vendor>${vendor.name}</Specification-Vendor>
                        <Specification-Version>${spec.specification.version}</Specification-Version>
                        <_noee>true</_noee>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>osgi-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <doclint>all</doclint>
                    <detectJavaApiLink>false</detectJavaApiLink>
                    <docfilessubdirs>true</docfilessubdirs>
                    <description>Jakarta Persistence API documentation</description>
                    <doctitle>Jakarta Persistence API documentation</doctitle>
                    <windowtitle>Jakarta Persistence API documentation</windowtitle>
                    <header><![CDATA[<br>Jakarta Persistence API v${project.version}]]>
                    </header>
                    <bottom><![CDATA[<br>
Comments to: <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
Copyright &#169; 2019 Eclipse Foundation. All rights reserved.<br>
Use is subject to <a href="{@docRoot}/doc-files/speclicense.html" target="_top">license terms</a>.]]>
                    </bottom>
                    <groups>
                        <group>
                            <title>Jakarta Persistence API Packages</title>
                            <packages>javax.persistence*</packages>
                        </group>
                    </groups>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>