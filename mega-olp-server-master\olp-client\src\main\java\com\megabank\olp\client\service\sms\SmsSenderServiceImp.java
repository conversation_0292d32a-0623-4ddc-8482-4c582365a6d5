package com.megabank.olp.client.service.sms;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.sender.sms.SmsSenderClient;
import com.megabank.olp.client.sender.sms.bean.SmsSenderArgBean;
import com.megabank.olp.client.utility.enums.ClientSmsErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Service
@Profile( { "uat", "prod" } )
public class SmsSenderServiceImp implements SmsSenderService
{
	@Autowired
	private SmsSenderClient smsSenderClient;

	@Override
	public void send( String contractNo, String branchBankCode, String mobileNumber, String idNo, String message ) throws MyRuntimeException
	{
		SmsSenderArgBean argBean = new SmsSenderArgBean();
		argBean.setContractNo( contractNo );
		argBean.setBranchBankCode( branchBankCode );
		argBean.setMobileNumber( mobileNumber );
		argBean.setIdNo( idNo );
		argBean.setMessage( message );

		boolean isSuccess = smsSenderClient.send( argBean ).isSuccess();

		if( !isSuccess )
			throw new MyRuntimeException( ClientSmsErrorEnum.SEND_SMS_FAILED );
	}

}
