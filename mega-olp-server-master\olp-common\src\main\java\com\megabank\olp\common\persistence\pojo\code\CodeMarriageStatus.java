package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeMarriageStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_marriage_status" )
public class CodeMarriageStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_marriage_status";

	public static final String MARRIAGE_STATUS_CODE_CONSTANT = "marriageStatusCode";

	public static final String NAME_CONSTANT = "name";

	private String marriageStatusCode;

	private String name;

	public CodeMarriageStatus()
	{}

	public CodeMarriageStatus( String marriageStatusCode )
	{
		this.marriageStatusCode = marriageStatusCode;
	}

	@Id
	@Column( name = "marriage_status_code", unique = true, nullable = false, length = 20 )
	public String getMarriageStatusCode()
	{
		return marriageStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setMarriageStatusCode( String marriageStatusCode )
	{
		this.marriageStatusCode = marriageStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}