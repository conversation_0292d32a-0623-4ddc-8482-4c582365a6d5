<!--

    Copyright (c) 2005, 2020 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind.mvn</groupId>
        <artifactId>jaxb-txw-parent</artifactId>
        <version>2.3.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>txw2</artifactId>
    <packaging>jar</packaging>
    <name>TXW2 Runtime</name>

    <description>
        TXW is a library that allows you to write XML documents.
    </description>

    <properties>
        <spotbugs.exclude>${project.basedir}/exclude-txw-runtime.xml</spotbugs.exclude>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Implementation-Title>TXW Runtime</Implementation-Title>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

