package com.megabank.olp.client.sender.micro.apply.management.survey;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyBranchBankUpdatedArgBean;

@Component
public class LoanSurveyBranchBankUpdatedClient extends BaseApplyClient<LoanSurveyBranchBankUpdatedArgBean, Long>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/survey/updateBranchBank";
	}
}
