package com.megabank.olp.client.service.mydata;

import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileResultBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ArgBean;
import com.megabank.olp.client.sender.mydata.webcomm.type1.bean.MyDataType1ResultBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public interface MyDataSenderService
{
	public String getTxId( String serviceId );

	public MyDataFileResultBean getUserData( String serviceId, String txId );

	public boolean reportMyData( String serviceId, String txId );

	public MyDataType1ResultBean sendMyDataType1( MyDataType1ArgBean argBean );
}
