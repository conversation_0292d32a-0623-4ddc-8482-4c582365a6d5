<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping >
	
	<sql-query name="otherbankedda.getIntersectionList">
		SELECT eddabank.*
	  	FROM code_list eddabank
	  	JOIN code_other_bank otherbank ON eddabank.code_value = otherbank.other_bank_code
		WHERE eddabank.code_type = 'edda_bank_code'
		AND eddabank.code_disabled = 0
		AND otherbank.disabled = 0
		ORDER BY eddabank.code_display_order
	</sql-query>
	
	<sql-query name="otherbankedda.getOtherbankEddaBranchList">
		SELECT eddabank.*
	  	FROM code_list eddabank
		WHERE eddabank.code_type = 'edda_bank_branch_code'
		AND eddabank.code_disabled = 0
		AND eddabank.code_value LIKE coalesce( :codeValue, eddabank.code_value )
		ORDER BY eddabank.code_display_order
	</sql-query>
	
</hibernate-mapping>
