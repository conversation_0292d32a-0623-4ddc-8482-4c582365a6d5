<?xml version="1.0"?>
<!--

    Copyright (c) 1997, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>jakarta.xml.bind</groupId>
    <artifactId>jakarta.xml.bind-api-parent</artifactId>
    <version>2.3.2</version>
    <packaging>pom</packaging>
    <name>Java Architecture for XML Binding</name>
    <description>JAXB (JSR 222) API</description>
    <url>https://github.com/eclipse-ee4j/jaxb-api</url>

    <scm>
        <connection>scm:git:git://github.com/eclipse-ee4j/jaxb-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/jaxb-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaxb-api.git</url>
        <tag>HEAD</tag>
    </scm>

    <licenses>
        <license>
            <name>Eclipse Distribution License - v 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Roman Grigoriadi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaxb-api/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Eclipse Project for JAXB mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://accounts.eclipse.org/mailing-list/jaxb-dev</subscribe>
            <unsubscribe>https://accounts.eclipse.org/mailing-list/jaxb-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jaxb-dev</archive>
        </mailingList>
    </mailingLists>

    <properties>
        <release.spec.feedback><EMAIL></release.spec.feedback>
        <release.spec.date>Jul 2017</release.spec.date>
        <findbugs.exclude>${project.basedir}/exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
        <api.package>javax.xml.bind</api.package>
        <extension.name>jakarta.xml.bind</extension.name>
        <spec.version>2.3</spec.version>
        <impl.version>2</impl.version>
        <activation.version>1.2.1</activation.version>
        <config.dir>${project.basedir}/etc/config</config.dir>
        <vendor.name>Oracle Corporation</vendor.name>
    </properties>

    <modules>
        <module>jaxb-api</module>
        <module>jaxb-api-test</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${activation.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.50</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.20</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <templateFile>${config.dir}/edl-copyright.txt</templateFile>
                    <excludeFile>${config.dir}/copyright-exclude</excludeFile>
                    <scm>git</scm>
                    <!-- skip files not under SCM-->
                    <scmOnly>true</scmOnly>
                    <!-- turn off warnings -->
                    <warn>true</warn>
                    <!-- for use with repair -->
                    <update>false</update>
                    <!-- check that year is correct -->
                    <ignoreYear>false</ignoreYear>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>copyright</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

   <profiles>
      <profile>
            <id>oss-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <additionalJOptions>
                                <additionalJOption>-Xdoclint:none</additionalJOption>
                            </additionalJOptions>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
