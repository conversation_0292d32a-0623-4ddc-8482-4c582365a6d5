/**
 *
 */
package com.megabank.olp.client.sender.pib.token;

import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.pib.BasePibClient;
import com.megabank.olp.client.sender.pib.token.bean.PibTokenArgBean;
import com.megabank.olp.client.sender.pib.token.bean.PibTokenReqBean;
import com.megabank.olp.client.sender.pib.token.bean.PibTokenResBean;
import com.megabank.olp.client.sender.pib.token.bean.PibTokenResultBean;
import com.megabank.olp.client.sender.pib.token.bean.RquestDataBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
@Component
public class TokenClient extends BasePibClient<PibTokenArgBean, PibTokenReqBean, <PERSON>bTokenR<PERSON><PERSON>ean, PibTokenResultBean>
{
	@Override
	protected String getSimulatorCode( PibTokenArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/openAPI/getToken";
	}

	@Override
	protected PibTokenReqBean transArg2Req( PibTokenArgBean argBean )
	{
		PibTokenReqBean reqBean = new PibTokenReqBean();
		reqBean.setClientIp( argBean.getClientIp() );
		reqBean.setTrackingIxd( argBean.getTrackingIxd() );

		RquestDataBean reqDataBean = new RquestDataBean();
		reqDataBean.setAuthCode( argBean.getAuthCode() );
		reqBean.setRquestDataBean( reqDataBean );

		return reqBean;
	}

	@Override
	protected PibTokenResultBean transRes2Result( PibTokenResBean resBean, HttpHeaders httpHeaders )
	{
		PibTokenResultBean resultBean = new PibTokenResultBean();
		resultBean.setSys( resBean.getSys() );
		resultBean.setResource( resBean.getResource() );
		resultBean.setClientTime( resBean.getResponseDate() );
		resultBean.setCode( resBean.getReturnCode() );
		resultBean.setDesc( resBean.getReturnDesc() );

		if( resBean.getAppRepBody() != null )
		{
			resultBean.setAccessToken( resBean.getAppRepBody().getAccessToken() );
			resultBean.setSessionIxd( resBean.getAppRepBody().getSessionIxd() );
		}

		return resultBean;
	}

}
