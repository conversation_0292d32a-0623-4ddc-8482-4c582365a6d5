package com.megabank.olp.client.sender.monitor.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class SendBean extends BaseBean
{
	@XmlAttribute( name = "xmlns" )
	private String xmlns = "http://IT.MEGABANK.COM/";

	@XmlElement( name = "msgObj" )
	private MsgObjBean msgObjBean;

	public SendBean()
	{}

	public MsgObjBean getMsgObjBean()
	{
		return msgObjBean;
	}

	public String getXmlns()
	{
		return xmlns;
	}

	public void setMsgObjBean( MsgObjBean msgObjBean )
	{
		this.msgObjBean = msgObjBean;
	}

	public void setXmlns( String xmlns )
	{
		this.xmlns = xmlns;
	}
}
