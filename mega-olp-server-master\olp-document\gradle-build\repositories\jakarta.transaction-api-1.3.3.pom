<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2012, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>jakarta.transaction</groupId>
    <artifactId>jakarta.transaction-api</artifactId>
    <version>1.3.3</version>
    
    <properties>
        <non.final>false</non.final>
        <extension.name>javax.transaction</extension.name>
        <spec.version>1.3</spec.version>
        <findbugs.version>2.3.1</findbugs.version>
        <findbugs.exclude>exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
    </properties>
    <name>${extension.name} API</name>
    <description>Jakarta Transactions</description>
    <url>https://projects.eclipse.org/projects/ee4j.jta</url>

    <developers>
        <developer>
            <id>stephen_felts</id>
            <name>Stephen Felts</name>
            <organization>Oracle, Inc.</organization>
            <roles>
                <role>lead</role>
            </roles>
        </developer>
    </developers>

    <organization>
        <name>EE4J Community</name>
        <url>https://github.com/eclipse-ee4j</url>
    </organization>
    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jta-api/issues</url>
    </issueManagement>
    <mailingLists>
      <mailingList>
            <name>Jakarta Transactions mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/jta-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jta-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jta-dev/</archive>
        </mailingList>
    </mailingLists>
    <scm>
        <connection>scm:git:https://github.com/eclipse-ee4j/jta-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/jta-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jta-api</url>
      <tag>HEAD</tag>
  </scm>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.html</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>META-INF/README</exclude>
                </excludes>
            </resource>
        </resources>        
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                    <compilerArgument>-Xlint:unchecked</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>spec-version-maven-plugin</artifactId>
                <version>2.0</version>
                <configuration>
                    <specMode>jakarta</specMode>
                    <spec>
                        <nonFinal>${non.final}</nonFinal>
                        <jarType>api</jarType>
                        <specVersion>${spec.version}</specVersion>
                        <specImplVersion>${project.version}</specImplVersion>
                        <apiPackage>${extension.name}</apiPackage>
                    </spec>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>set-spec-properties</goal>
                            <goal>check-module</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>            
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>1.4.3</version>
                <configuration>
                    <supportedProjectTypes>
                        <supportedProjectType>jar</supportedProjectType>
                    </supportedProjectTypes>
                    <instructions>
                        <Bundle-Version>${spec.bundle.version}</Bundle-Version>
                        <Bundle-SymbolicName>${spec.bundle.symbolic-name}</Bundle-SymbolicName>
                        <Extension-Name>${spec.extension.name}</Extension-Name>
                        <Implementation-Version>${spec.implementation.version}</Implementation-Version>
                        <Specification-Version>${spec.specification.version}</Specification-Version>                               
                        <Bundle-Description>
                            Jakarta(TM) Transactions ${spec.version} API Design Specification
                        </Bundle-Description>
                        <Specification-Vendor>Oracle Corporation</Specification-Vendor>
                        <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                        <Implementation-Vendor-Id>org.glassfish</Implementation-Vendor-Id>
                        <Import-Package>javax.interceptor.*;version="[1.2,1.2.98)",*</Import-Package>
                        <_include>-${basedir}/osgi.bundle</_include>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                      <manifestEntries>
                          <!-- for JDK 9 -->
                          <Automatic-Module-Name>
                            java.transaction
                          </Automatic-Module-Name>
                      </manifestEntries>
                    </archive>
                    <excludes>
                        <exclude>**/*.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>build-helper-maven-plugin</artifactId>
              <version>3.0.0</version>
              <executions>
                <execution>
                  <id>add-resource</id>
                  <phase>generate-resources</phase>
                  <goals>
                    <goal>add-resource</goal>
                  </goals>
                  <configuration>
                    <resources>
                      <resource>
                        <directory>.</directory>
                        <targetPath>META-INF</targetPath>
                        <includes>
                          <include>LICENSE.md</include>
                          <include>NOTICE.md</include>
                        </includes>
                      </resource>
                    </resources>
                  </configuration>
                </execution>
              </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <includePom>true</includePom>
                </configuration>
                <executions>
                    <execution>
                       <id>attach-sources</id>
                       <goals>
                           <goal>jar-no-fork</goal> 
                       </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <author>false</author>
                    <description>
                        Jakarta Transactions documentation
                    </description>
                    <doctitle>
                        Jakarta Transactions documentation
                    </doctitle>
                    <windowtitle>
                        Jakarta Transactions documentation
                    </windowtitle>
                    <splitindex>true</splitindex>
                    <use>true</use>
                    <bottom>
        <![CDATA[Copyright &#169; 2019 Eclipse Foundation. All Rights Reserved.
            <br/>Use is subject to
    <a href="{@docRoot}/doc-files/EFSL.html" target="_top">license terms</a>.
        ]]>
                    </bottom>
                </configuration>                
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>javadoc</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>     
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>${findbugs.version}</version>
                <configuration>
                    <threshold>${findbugs.threshold}</threshold>
                    <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <useReleaseProfile>false</useReleaseProfile>
                    <arguments>${release.arguments}</arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <reporting>
                        <plugins>
                            <plugin>
                                <groupId>org.codehaus.mojo</groupId>
                                <artifactId>findbugs-maven-plugin</artifactId>
                                <version>${findbugs.version}</version>
                                <configuration>
                                    <threshold>${findbugs.threshold}</threshold>
                                    <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                                </configuration>
                            </plugin>
                        </plugins>                        
                    </reporting>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>jakarta.enterprise</groupId>
            <artifactId>jakarta.enterprise.cdi-api</artifactId>
            <version>2.0.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
