package com.megabank.olp.common.persistence.pojo.code;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeUserType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_user_type" )
public class CodeUserType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_user_type";

	public static final String USER_TYPE_CONSTANT = "userType";

	public static final String NAME_CONSTANT = "name";

	public static final String HL_DESC_CONSTANT = "hlDesc";

	public static final String CODE_USER_SUB_TYPES_CONSTANT = "codeUserSubTypes";

	private String userType;

	private String name;

	private String hlDesc;

	private transient Set<CodeUserSubType> codeUserSubTypes = new HashSet<>( 0 );

	public CodeUserType()
	{}

	public CodeUserType( String userType )
	{
		this.userType = userType;
	}

	public CodeUserType( String userType, String name )
	{
		this.userType = userType;
		this.name = name;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "codeUserType" )
	public Set<CodeUserSubType> getCodeUserSubTypes()
	{
		return codeUserSubTypes;
	}

	@Column( name = "hl_desc" )
	public String getHlDesc()
	{
		return hlDesc;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "user_type", unique = true, nullable = false, length = 20 )
	public String getUserType()
	{
		return userType;
	}

	public void setCodeUserSubTypes( Set<CodeUserSubType> codeUserSubTypes )
	{
		this.codeUserSubTypes = codeUserSubTypes;
	}

	public void setHlDesc( String hlDesc )
	{
		this.hlDesc = hlDesc;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setUserType( String userType )
	{
		this.userType = userType;
	}

}