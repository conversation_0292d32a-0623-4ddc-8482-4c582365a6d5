package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The LandingTemplateColumn is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_template_column" )
public class LandingTemplateColumn extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_template_column";

	public static final String TEMPLATE_COLUMN_ID_CONSTANT = "templateColumnId";

	public static final String LANDING_TEMPLATE_CONSTANT = "landingTemplate";

	public static final String COLUMN_NAME_CONSTANT = "columnName";

	public static final String COLUMN_TITLE_CONSTANT = "columnTitle";

	public static final String COLUMN_TYPE_CONSTANT = "columnType";

	public static final String COLUMN_ORDER_CONSTANT = "columnOrder";

	public static final String LANDING_SOLUTION_VALUES_CONSTANT = "landingSolutionValues";

	public static final String LANDING_SOLUTION_IMAGES_CONSTANT = "landingSolutionImages";

	private Long templateColumnId;

	private transient LandingTemplate landingTemplate;

	private String columnName;

	private String columnTitle;

	private String columnType;

	private int columnOrder;

	private transient Set<LandingSolutionValue> landingSolutionValues = new HashSet<>( 0 );

	private transient Set<LandingSolutionImage> landingSolutionImages = new HashSet<>( 0 );

	public LandingTemplateColumn()
	{}

	public LandingTemplateColumn( LandingTemplate landingTemplate, String columnName, String columnTitle, String columnType, int columnOrder )
	{
		this.landingTemplate = landingTemplate;
		this.columnName = columnName;
		this.columnTitle = columnTitle;
		this.columnType = columnType;
		this.columnOrder = columnOrder;
	}

	public LandingTemplateColumn( Long templateColumnId )
	{
		this.templateColumnId = templateColumnId;
	}

	@Column( name = "column_name", nullable = false, length = 100 )
	public String getColumnName()
	{
		return columnName;
	}

	@Column( name = "column_order", nullable = false, precision = 5, scale = 0 )
	public int getColumnOrder()
	{
		return columnOrder;
	}

	@Column( name = "column_title", nullable = false )
	public String getColumnTitle()
	{
		return columnTitle;
	}

	@Column( name = "column_type", nullable = false, length = 20 )
	public String getColumnType()
	{
		return columnType;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingTemplateColumn" )
	public Set<LandingSolutionImage> getLandingSolutionImages()
	{
		return landingSolutionImages;
	}

	@OneToMany( fetch = FetchType.LAZY, mappedBy = "landingTemplateColumn" )
	public Set<LandingSolutionValue> getLandingSolutionValues()
	{
		return landingSolutionValues;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "template_id", nullable = false )
	public LandingTemplate getLandingTemplate()
	{
		return landingTemplate;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "template_column_id", unique = true, nullable = false )
	public Long getTemplateColumnId()
	{
		return templateColumnId;
	}

	public void setColumnName( String columnName )
	{
		this.columnName = columnName;
	}

	public void setColumnOrder( int columnOrder )
	{
		this.columnOrder = columnOrder;
	}

	public void setColumnTitle( String columnTitle )
	{
		this.columnTitle = columnTitle;
	}

	public void setColumnType( String columnType )
	{
		this.columnType = columnType;
	}

	public void setLandingSolutionImages( Set<LandingSolutionImage> landingSolutionImages )
	{
		this.landingSolutionImages = landingSolutionImages;
	}

	public void setLandingSolutionValues( Set<LandingSolutionValue> landingSolutionValues )
	{
		this.landingSolutionValues = landingSolutionValues;
	}

	public void setLandingTemplate( LandingTemplate landingTemplate )
	{
		this.landingTemplate = landingTemplate;
	}

	public void setTemplateColumnId( Long templateColumnId )
	{
		this.templateColumnId = templateColumnId;
	}
}