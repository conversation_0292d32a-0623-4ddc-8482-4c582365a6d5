package com.megabank.olp.common.persistence.dao.mixed;

import java.util.Arrays;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.common.config.CommonConfig;
import com.megabank.olp.common.persistence.pojo.code.CodeCity;
import com.megabank.olp.common.persistence.pojo.code.CodeTown;

@SpringBootTest
@ContextConfiguration( classes = CommonConfig.class )
public class BranchBankDAOIntegration
{
	private final Logger logger = LogManager.getLogger( getClass() );

	@Autowired
	private BranchBankDAO dao;

	@Test
	public void getBranchBankCity()
	{
		String loanType = "personalloan";
		List<Integer> allowedUser = Arrays.asList( 3, 2 );

		List<CodeCity> resBeans = dao.getBranchBankCity( loanType, allowedUser );

		logger.info( "resBeans:{}", resBeans );
	}

	@Test
	public void getBranchBankTownByCity()
	{
		String loanType = "personalloan";
		String cityCode = "A";
		List<Integer> allowedUser = Arrays.asList( 1 );

		List<CodeTown> resBeans = dao.getBranchBankTownByCity( loanType, cityCode, allowedUser );

		logger.info( "resBeans:{}", resBeans );
	}

}
