package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeCity;

/**
 * The CodeCityDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeCityDAO extends BasePojoDAO<CodeCity, String>
{

	public List<CodeCity> getAllPojosOrderByDisplay()
	{
		OrderBean orderBean = new OrderBean( CodeCity.DISPLAY_ORDER_CONSTANT );

		return this.getAllPojosOrderBy( orderBean );
	}

	public CodeCity read( String cityCode )
	{
		Validate.notBlank( cityCode );

		return getPojoByPK( cityCode, CodeCity.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeCity> getPojoClass()
	{
		return CodeCity.class;
	}
}
