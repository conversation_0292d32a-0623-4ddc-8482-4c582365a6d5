package com.megabank.olp.common.persistence.dao.generated.code;

import java.util.List;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.megabank.olp.base.bean.NameValueBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeBranchBank;

/**
 * The CodeBranchBankDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeBranchBankDAO extends BasePojoDAO<CodeBranchBank, Long>
{
	@Autowired
	private CodeTownDAO codeTownDAO;

	public List<CodeBranchBank> getPojosByBankCodeActiveBusinessUnit( String bankCode )
	{
		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeBranchBank.BANK_CODE_CONSTANT, bankCode ),
														  new NameValueBean( CodeBranchBank.HEAD_OFFICE_CONSTANT, false ),
														  new NameValueBean( CodeBranchBank.DISABLED_CONSTANT, false ) };

		return getPojosByProperties( conditions );
	}

	public List<CodeBranchBank> getPojosByTownCode( String townCode )
	{
		NameValueBean[] conditions = new NameValueBean[]{ new NameValueBean( CodeBranchBank.CODE_TOWN_CONSTANT, codeTownDAO.read( townCode ) ),
														  new NameValueBean( CodeBranchBank.HEAD_OFFICE_CONSTANT, 0 ),
														  new NameValueBean( CodeBranchBank.DISABLED_CONSTANT, false ) };

		return getPojosByProperties( conditions );
	}

	public List<CodeBranchBank> getPojosByDisabled( Boolean isActive )
	{
		NameValueBean condition = new NameValueBean( CodeBranchBank.DISABLED_CONSTANT, isActive );

		return getPojosByProperty( condition );
	}

	public CodeBranchBank read( Long branchBankId )
	{
		Validate.notNull( branchBankId );

		return getPojoByPK( branchBankId, CodeBranchBank.TABLENAME_CONSTANT );
	}

	@Override
	protected Class<CodeBranchBank> getPojoClass()
	{
		return CodeBranchBank.class;
	}
}
