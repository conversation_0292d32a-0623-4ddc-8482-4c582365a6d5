package com.megabank.olp.client.sender.micro.apply.management.house;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialArgBean;
import com.megabank.olp.client.sender.micro.apply.management.house.bean.loantrial.HouseLoanTrialDetailResultBean;

@Component
public class HouseLoanTrialDetailClient extends BaseApplyClient<HouseLoanTrialArgBean, HouseLoanTrialDetailResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/houseloantrial/getDetail";
	}
}
