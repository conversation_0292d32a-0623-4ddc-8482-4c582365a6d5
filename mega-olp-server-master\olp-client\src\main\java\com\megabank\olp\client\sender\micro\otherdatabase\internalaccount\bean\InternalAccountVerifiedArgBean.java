package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import java.util.Date;

import com.megabank.olp.base.bean.BaseBean;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class InternalAccountVerifiedArgBean extends BaseBean
{
	private String idNo;

	private Date birthDate;

	public InternalAccountVerifiedArgBean()
	{}

	public Date getBirthDate()
	{
		return birthDate;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public void setBirthDate( Date birthDate )
	{
		this.birthDate = birthDate;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

}
