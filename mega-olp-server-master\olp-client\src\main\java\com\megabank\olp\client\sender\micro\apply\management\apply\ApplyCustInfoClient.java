package com.megabank.olp.client.sender.micro.apply.management.apply;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyCustInfoArgBean;
import com.megabank.olp.client.sender.micro.apply.management.apply.bean.ApplyCustInfoResultBean;

@Component
public class ApplyCustInfoClient extends BaseApplyClient<ApplyCustInfoArgBean, ApplyCustInfoResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/apply/getApplyCustInfo";
	}
}
