package com.megabank.olp.common.persistence.dao.generated.code;

import org.springframework.stereotype.Repository;

import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeContactTime;

/**
 * The CodeContactTimeDAO is generated by Hibernate Tools 4.3.4.Final
 */
@Repository
public class CodeContactTimeDAO extends BasePojoDAO<CodeContactTime, String>
{
	@Override
	protected Class<CodeContactTime> getPojoClass()
	{
		return CodeContactTime.class;
	}
}
