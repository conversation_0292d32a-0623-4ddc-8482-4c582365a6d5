package com.megabank.olp.client.sender.micro.apply.management.survey;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyExportedArgBean;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyExportedResultBean;

@Component
public class LoanSurveyExportedClient extends BaseApplyClient<LoanSurveyExportedArgBean, LoanSurveyExportedResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/survey/exportList";
	}
}
