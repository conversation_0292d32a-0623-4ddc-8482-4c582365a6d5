package com.megabank.olp.client.sender.micro.user;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoArgBean;
import com.megabank.olp.client.sender.micro.user.bean.IdentityInfoResultBean;

@Component
public class IdentityInfoClient extends BaseUserClient<IdentityInfoArgBean, IdentityInfoResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/user/getIdentityInfo";
	}
}
