package com.megabank.olp.client.service.ixml;

import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginArgBean;
import com.megabank.olp.client.sender.ixml.login.bean.IxmlLoginResBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertArgBean;
import com.megabank.olp.client.sender.ixml.querycert.bean.IxmlQueryCertResBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultArgBean;
import com.megabank.olp.client.sender.ixml.queryverifyresult.bean.IxmlQueryVerifyResultResBean;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertArgBean;
import com.megabank.olp.client.sender.ixml.revokecert.bean.IxmlRevokeCertResBean;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertArgBean;
import com.megabank.olp.client.sender.ixml.savecert.bean.IxmlSaveCertResBean;

public interface IxmlSenderService
{
	public IxmlLoginResBean login( IxmlLoginArgBean argBean );

	public IxmlQueryCertResBean queryCert( IxmlQueryCertArgBean argBean );

	public IxmlQueryVerifyResultResBean queryVerifyResult( IxmlQueryVerifyResultArgBean argBean );

	public IxmlRevokeCertResBean revokeCert( IxmlRevokeCertArgBean argBean );

	public IxmlSaveCertResBean saveCert( IxmlSaveCertArgBean argBean );
}