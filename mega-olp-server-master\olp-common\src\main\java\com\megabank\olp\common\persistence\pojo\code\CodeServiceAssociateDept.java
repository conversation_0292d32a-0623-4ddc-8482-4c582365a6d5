package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeServiceAssociateDept is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_service_associate_dept" )
public class CodeServiceAssociateDept extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_service_associate_dept";

	public static final String SERVICE_ASSOCIATE_DEPT_CODE_CONSTANT = "serviceAssociateDeptCode";

	public static final String NAME_CONSTANT = "name";

	private String serviceAssociateDeptCode;

	private String name;

	public CodeServiceAssociateDept()
	{}

	public CodeServiceAssociateDept( String serviceAssociateDeptCode )
	{
		this.serviceAssociateDeptCode = serviceAssociateDeptCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "service_associate_dept_code", unique = true, nullable = false, length = 20 )
	public String getServiceAssociateDeptCode()
	{
		return serviceAssociateDeptCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setServiceAssociateDeptCode( String serviceAssociateDeptCode )
	{
		this.serviceAssociateDeptCode = serviceAssociateDeptCode;
	}
}