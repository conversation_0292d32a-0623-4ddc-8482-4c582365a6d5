package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyBean extends BaseBean
{
	@JsonProperty( "id" )
	private Long surveyId;

	private String caseNo;

	private String name;

	private String mobileNumber;

	private Date createdDate;

	private String notificationStatusName;

	private String processStatusName;

	private String branchBankName;

	private String contactTime;

	public LoanSurveyBean()
	{
		// default constructor
	}

	public String getBranchBankName()
	{
		return branchBankName;
	}

	public String getCaseNo()
	{
		return caseNo;
	}

	public String getContactTime()
	{
		return contactTime;
	}

	public Date getCreatedDate()
	{
		return createdDate;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getNotificationStatusName()
	{
		return notificationStatusName;
	}

	public String getProcessStatusName()
	{
		return processStatusName;
	}

	public Long getSurveyId()
	{
		return surveyId;
	}

	public void setBranchBankName( String branchBankName )
	{
		this.branchBankName = branchBankName;
	}

	public void setCaseNo( String caseNo )
	{
		this.caseNo = caseNo;
	}

	public void setContactTime( String contactTime )
	{
		this.contactTime = contactTime;
	}

	public void setCreatedDate( Date createdDate )
	{
		this.createdDate = createdDate;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setNotificationStatusName( String notificationStatusName )
	{
		this.notificationStatusName = notificationStatusName;
	}

	public void setProcessStatusName( String processStatusName )
	{
		this.processStatusName = processStatusName;
	}

	public void setSurveyId( Long surveyId )
	{
		this.surveyId = surveyId;
	}

}