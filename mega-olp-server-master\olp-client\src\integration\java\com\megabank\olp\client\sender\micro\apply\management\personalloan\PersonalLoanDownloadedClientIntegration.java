package com.megabank.olp.client.sender.micro.apply.management.personalloan;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import com.megabank.olp.base.config.BaseServiceConfig;
import com.megabank.olp.client.config.ClientServiceConfig;
import com.megabank.olp.client.sender.micro.JwtArgBean;
import com.megabank.olp.client.sender.micro.apply.management.personalloan.PersonalLoanDownloadedClient;
import com.megabank.olp.client.sender.micro.apply.management.personalloan.bean.PersonalLoanArgBean;
import com.megabank.olp.client.sender.micro.apply.management.personalloan.bean.PersonalLoanDownloadedResultBean;
import com.megabank.olp.system.config.SystemConfig;

@SpringBootTest
@ContextConfiguration( classes = { ClientServiceConfig.class, BaseServiceConfig.class, SystemConfig.class } )
public class PersonalLoanDownloadedClientIntegration
{
	@Autowired
	private PersonalLoanDownloadedClient client;

	private final Logger logger = LogManager.getLogger( getClass() );

	@Test
	public void send()
	{
		Long loanId = 1L;

		PersonalLoanArgBean argBean = new PersonalLoanArgBean();
		argBean.setLoanId( loanId );

		PersonalLoanDownloadedResultBean resultBean = client.send( argBean, new JwtArgBean() );

		logger.info( "resultBean:{}", resultBean );

	}
}
