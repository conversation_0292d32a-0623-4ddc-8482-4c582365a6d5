package com.megabank.olp.client.sender.micro.apply.management.survey.bean;

import com.megabank.olp.base.bean.BaseBean;

public class LoanSurveyContactBean extends BaseBean
{
	private String name;

	private String branchBankName;

	private String phoneCode;

	private String phoneNumber;

	private String phoneExt;

	private String email;

	private String mobileNumber;

	private String contactTime;

	private String otherMsg;

	public LoanSurveyContactBean()
	{}

	public String getBranchBankName()
	{
		return branchBankName;
	}

	public String getContactTime()
	{
		return contactTime;
	}

	public String getEmail()
	{
		return email;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public String getName()
	{
		return name;
	}

	public String getOtherMsg()
	{
		return otherMsg;
	}

	public String getPhoneCode()
	{
		return phoneCode;
	}

	public String getPhoneExt()
	{
		return phoneExt;
	}

	public String getPhoneNumber()
	{
		return phoneNumber;
	}

	public void setBranchBankName( String branchBankName )
	{
		this.branchBankName = branchBankName;
	}

	public void setContactTime( String contactTime )
	{
		this.contactTime = contactTime;
	}

	public void setEmail( String email )
	{
		this.email = email;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setOtherMsg( String otherMsg )
	{
		this.otherMsg = otherMsg;
	}

	public void setPhoneCode( String phoneCode )
	{
		this.phoneCode = phoneCode;
	}

	public void setPhoneExt( String phoneExt )
	{
		this.phoneExt = phoneExt;
	}

	public void setPhoneNumber( String phoneNumber )
	{
		this.phoneNumber = phoneNumber;
	}

}
