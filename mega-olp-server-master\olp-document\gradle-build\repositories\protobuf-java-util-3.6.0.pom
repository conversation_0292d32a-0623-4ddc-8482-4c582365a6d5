<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-parent</artifactId>
    <version>3.6.0</version>
  </parent>

  <artifactId>protobuf-java-util</artifactId>
  <packaging>bundle</packaging>

  <name>Protocol Buffers [Util]</name>
  <description>Utilities for Protocol Buffers</description>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.7</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymockclassextension</artifactId>
    </dependency>
  </dependencies>

  <properties>
    <!-- Use the core proto dir so that we can call the core generation script -->
    <test.proto.dir>../core/src/test/proto</test.proto.dir>
  </properties>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <!-- Generate the test protos -->
          <execution>
            <id>generate-test-sources</id>
            <phase>generate-test-sources</phase>
            <configuration>
              <target>
                <!-- Generate all of the test protos from the core module -->
                <ant antfile="../core/generate-test-sources-build.xml"/>

                <!-- Generate additional test protos for this module -->
                <exec executable="${protoc}">
                  <arg value="--java_out=${generated.testsources.dir}" />
                  <arg value="--proto_path=${protobuf.source.dir}" />
                  <arg value="--proto_path=src/test/proto" />
                  <arg value="src/test/proto/com/google/protobuf/util/json_test.proto" />
                </exec>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- Add the generated test sources to the build -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-generated-test-sources</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>add-test-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${generated.testsources.dir}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Configure the OSGI bundle -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Bundle-DocURL>https://developers.google.com/protocol-buffers/</Bundle-DocURL>
            <Bundle-SymbolicName>com.google.protobuf.util</Bundle-SymbolicName>
            <Export-Package>com.google.protobuf.util;version=${project.version}</Export-Package>
          </instructions>
        </configuration>
      </plugin>

      <!-- Configure the fat jar to include all dependencies -->
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
