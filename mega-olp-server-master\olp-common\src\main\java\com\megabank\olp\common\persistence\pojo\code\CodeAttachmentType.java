package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeAttachmentType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_attachment_type" )
public class CodeAttachmentType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_attachment_type";

	public static final String ATTACHMENT_TYPE_CONSTANT = "attachmentType";

	public static final String NAME_CONSTANT = "name";

	private String attachmentType;

	private String name;

	public CodeAttachmentType()
	{}

	public CodeAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	@Id
	@Column( name = "attachment_type", unique = true, nullable = false, length = 20 )
	public String getAttachmentType()
	{
		return attachmentType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setAttachmentType( String attachmentType )
	{
		this.attachmentType = attachmentType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}