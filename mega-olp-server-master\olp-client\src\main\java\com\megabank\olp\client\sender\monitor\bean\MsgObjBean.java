package com.megabank.olp.client.sender.monitor.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
public class MsgObjBean extends BaseBean
{
	@XmlElement( name = "ITMSG_EVENT_MSG" )
	private EventMsgBean eventMsgBean;

	public MsgObjBean()
	{}

	public EventMsgBean getEventMsgBean()
	{
		return eventMsgBean;
	}

	public void setEventMsgBean( EventMsgBean eventMsgBean )
	{
		this.eventMsgBean = eventMsgBean;
	}

}
