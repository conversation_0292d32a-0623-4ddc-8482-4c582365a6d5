package com.megabank.olp.client.sender.micro.apply.management.survey;

import org.springframework.stereotype.Component;

import com.megabank.olp.client.sender.micro.apply.BaseApplyClient;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyArgBean;
import com.megabank.olp.client.sender.micro.apply.management.survey.bean.LoanSurveyDetailResultBean;

@Component
public class LoanSurveyDetailClient extends BaseApplyClient<LoanSurveyArgBean, LoanSurveyDetailResultBean>
{
	@Override
	protected String getSuffixUrl()
	{
		return "/management/survey/getDetail";
	}
}
