package com.megabank.olp.client.sender;

import java.io.IOException;
import java.util.Collection;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.GenericTypeResolver;
import org.springframework.http.HttpHeaders;

import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.megabank.olp.base.bean.StatResBean;
import com.megabank.olp.base.enums.ResponseEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.client.utility.exception.MyClientException;

/**
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2019
 */

public abstract class BaseJsonWithStatClient<TArg, TReq, TRes, TResult> extends BaseJsonClient<TArg, TReq, StatResBean<TRes>, TResult>
{
	private final Class resClass;

	public BaseJsonWithStatClient()
	{
		resClass = GenericTypeResolver.resolveTypeArguments( getClass(), BaseJsonWithStatClient.class )[ 2 ];
	}

	protected boolean throwExceptionWhenStatError()
	{
		return true;
	}

	@Override
	protected StatResBean<TRes> transResBody2Res( String resBody ) throws IOException
	{
		TypeFactory factory = mapper.getTypeFactory();

		if( Collection.class.isAssignableFrom( resClass ) )
		{
			CollectionType collectionType = factory.constructCollectionType( resClass, getBeanClassByCollection() );

			return mapper.readValue( resBody, factory.constructParametricType( StatResBean.class, collectionType ) );

		}

		return mapper.readValue( resBody, factory.constructParametricType( StatResBean.class, resClass ) );
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.my.archetype.client.sender.BaseClient#transRes2Result(java.lang.Object, org.springframework.http.HttpHeaders)
	 */
	@Override
	protected TResult transRes2Result( StatResBean<TRes> statResBean, HttpHeaders httpHeaders )
	{

		String stat = statResBean.getStat();

		if( StringUtils.isBlank( stat ) )
			throw new MyClientException( "the value in the stat field is blank" );

		boolean success = ResponseEnum.STAT_OK.getContext().equals( stat );

		if( throwExceptionWhenStatError() && !success )
			throw new MyRuntimeException( statResBean.getErrorCode(), statResBean.getErrorMsg() );

		return transStatResult2Result( statResBean.getResult(), success );
	}

	/**
	 *
	 * @param result
	 * @return
	 */
	protected abstract TResult transStatResult2Result( TRes statResultBean, boolean sucess );
}
