package com.megabank.olp.common.service.bean.landing;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

public class NumberColumnResBean extends BaseColumnResBean
{
	@JsonProperty( "columnValue" )
	private BigDecimal value;

	public NumberColumnResBean()
	{
		// default constructor
	}

	public BigDecimal getValue()
	{
		return value;
	}

	public void setValue( BigDecimal value )
	{
		this.value = value;
	}

}
