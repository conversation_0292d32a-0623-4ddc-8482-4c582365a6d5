package com.megabank.olp.client.sender.mydata.webcomm.type1.bean;

import com.megabank.olp.base.bean.BaseBean;

public class ResultsBean extends BaseBean
{
	private String txId;

	private String redirectUrl;

	public ResultsBean()
	{
		// default constructor
	}

	public String getTxId() 
	{
		return txId;
	}

	public void setTxId(String txId) 
	{
		this.txId = txId;
	}

	public String getRedirectUrl() 
	{
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) 
	{
		this.redirectUrl = redirectUrl;
	}
}
