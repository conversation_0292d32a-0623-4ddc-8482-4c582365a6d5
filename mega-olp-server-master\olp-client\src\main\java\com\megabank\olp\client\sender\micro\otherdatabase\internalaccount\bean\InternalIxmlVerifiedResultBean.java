package com.megabank.olp.client.sender.micro.otherdatabase.internalaccount.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class InternalIxmlVerifiedResultBean extends BaseBean
{
	@JsonProperty( "userHaveIxmlPermission" )
	private Boolean isUserHaveIxmlPermission;

	public InternalIxmlVerifiedResultBean()
	{}

	public Boolean getIsUserHaveIxmlPermission()
	{
		return isUserHaveIxmlPermission;
	}

	public void setIsUserHaveIxmlPermission( Boolean isUserHaveIxmlPermission )
	{
		this.isUserHaveIxmlPermission = isUserHaveIxmlPermission;
	}
}
