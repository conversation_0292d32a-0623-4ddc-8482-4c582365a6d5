package com.megabank.olp.client.sender.otp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class BaseOtpResBean extends BaseBean
{
	@JsonProperty( "ResponseDate" )
	private String responseDate;

	@JsonProperty( "ReturnCode" )
	private String returnCode;

	@JsonProperty( "ReturnDesc" )
	private String returnDesc;

	public BaseOtpResBean()
	{}

	public String getResponseDate()
	{
		return responseDate;
	}

	public String getReturnCode()
	{
		return returnCode;
	}

	public String getReturnDesc()
	{
		return returnDesc;
	}

	public void setResponseDate( String responseDate )
	{
		this.responseDate = responseDate;
	}

	public void setReturnCode( String returnCode )
	{
		this.returnCode = returnCode;
	}

	public void setReturnDesc( String returnDesc )
	{
		this.returnDesc = returnDesc;
	}
}
