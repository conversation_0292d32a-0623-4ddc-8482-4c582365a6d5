<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-commons</artifactId>
	<version>2.7.10</version>

	<name>Spring Data Core</name>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>2.7.10</version>
	</parent>

	<properties>

		<scala>2.11.7</scala>
		<xmlbeam>1.4.24</xmlbeam>
		<java-module-name>spring.data.commons</java-module-name>

	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webflux</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>javax.annotation-api</artifactId>
			<version>${javax-annotation-api}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<version>3.0.2</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${jodatime}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.threeten</groupId>
			<artifactId>threetenbp</artifactId>
			<version>${threetenbp}</version>
			<optional>true</optional>
		</dependency>

		<!--  Project Reactor -->

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--  RxJava -->

		<dependency>
			<groupId>io.reactivex</groupId>
			<artifactId>rxjava</artifactId>
			<version>${rxjava}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.reactivex</groupId>
			<artifactId>rxjava-reactive-streams</artifactId>
			<version>${rxjava-reactive-streams}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.reactivex.rxjava2</groupId>
			<artifactId>rxjava</artifactId>
			<version>${rxjava2}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.reactivex.rxjava3</groupId>
			<artifactId>rxjava</artifactId>
			<version>${rxjava3}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.smallrye.reactive</groupId>
			<artifactId>mutiny</artifactId>
			<version>${smallrye-mutiny}</version>
			<optional>true</optional>
		</dependency>

		<!--  Querydsl -->

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-core</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<version>${querydsl}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-collections</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<!-- EJB Transactions -->
		<dependency>
			<groupId>javax.ejb</groupId>
			<artifactId>ejb-api</artifactId>
			<version>3.0</version>
			<optional>true</optional>
		</dependency>

		<!-- CDI -->
		<!-- Dependency order required to build against CDI 1.0 and test with CDI 2.0 -->
		<dependency>
			<groupId>org.apache.geronimo.specs</groupId>
			<artifactId>geronimo-jcdi_2.0_spec</artifactId>
			<version>1.0.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.interceptor</groupId>
			<artifactId>javax.interceptor-api</artifactId>
			<version>1.2.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.enterprise</groupId>
			<artifactId>cdi-api</artifactId>
			<version>${cdi}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.vavr</groupId>
			<artifactId>vavr</artifactId>
			<version>${vavr}</version>
			<optional>true</optional>
		</dependency>

		<!-- Eclipse Collections -->

		<dependency>
			<groupId>org.eclipse.collections</groupId>
			<artifactId>eclipse-collections-api</artifactId>
			<version>${eclipse-collections}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.eclipse.collections</groupId>
			<artifactId>eclipse-collections</artifactId>
			<version>${eclipse-collections}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>el-api</artifactId>
			<version>1.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans</groupId>
			<artifactId>openwebbeans-se</artifactId>
			<version>${webbeans}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.xbean</groupId>
			<artifactId>xbean-asm9-shaded</artifactId>
			<version>${webbeans.xbean}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.hateoas</groupId>
			<artifactId>spring-hateoas</artifactId>
			<version>${spring-hateoas}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>2.3.5</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>xmlunit</groupId>
			<artifactId>xmlunit</artifactId>
			<version>1.6</version>
			<scope>test</scope>
		</dependency>

		<!-- Groovy -->
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.4.4</version>
			<scope>test</scope>
		</dependency>

		<!-- Kotlin extension -->
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-reflect</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-core</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-reactive</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-reactor</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-test-junit5</artifactId>
			<version>${kotlin}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.mockk</groupId>
			<artifactId>mockk-jvm</artifactId>
			<version>${mockk}</version>
			<scope>test</scope>
		</dependency>

		<!-- Scala -->
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>${scala}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>javax.transaction-api</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>${jsonpath}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.xmlbeam</groupId>
			<artifactId>xmlprojector</artifactId>
			<version>${xmlbeam}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>de.schauderhaft.degraph</groupId>
			<artifactId>degraph-check</artifactId>
			<version>0.1.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.jmolecules.integrations</groupId>
			<artifactId>jmolecules-spring</artifactId>
			<version>${jmolecules-integration}</version>
			<optional>true</optional>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.asciidoctor</groupId>
				<artifactId>asciidoctor-maven-plugin</artifactId>
			</plugin>

		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-libs-release</id>
			<url>https://repo.spring.io/libs-release</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-plugins-release</id>
			<url>https://repo.spring.io/plugins-release</url>
		</pluginRepository>
	</pluginRepositories>

</project>
