package com.megabank.olp.client.sender.monitor.exception.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import com.megabank.olp.client.sender.monitor.bean.BaseMonitorReqBean;

@XmlRootElement( name = "soap:Envelope" )
@XmlAccessorType( XmlAccessType.FIELD )
public class MonitorExceptionReqBean extends BaseMonitorReqBean
{
	public MonitorExceptionReqBean()
	{}
}
