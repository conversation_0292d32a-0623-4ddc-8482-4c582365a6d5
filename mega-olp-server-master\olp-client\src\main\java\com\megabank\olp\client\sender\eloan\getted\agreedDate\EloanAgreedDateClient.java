/**
 *
 */
package com.megabank.olp.client.sender.eloan.getted.agreedDate;

import com.megabank.olp.client.sender.eloan.getted.EloanGettedClient;
import com.megabank.olp.client.sender.eloan.getted.agreedDate.bean.EloanAgreedDateArgBean;
import com.megabank.olp.client.sender.eloan.getted.agreedDate.bean.EloanAgreedDateResultBean;
import org.springframework.stereotype.Component;

@Component
public class EloanAgreedDateClient extends EloanGettedClient<EloanAgreedDateArgBean, EloanAgreedDateResultBean>
{

	@Override
	protected String getSimulatorCode( EloanAgreedDateArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/gw-web/pLoan/func/getAgreedDate";
	}

}
