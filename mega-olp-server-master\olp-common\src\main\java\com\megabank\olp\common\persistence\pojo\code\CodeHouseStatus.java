package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeHouseStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_house_status" )
public class CodeHouseStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_house_status";

	public static final String HOUSE_STATUS_CODE_CONSTANT = "houseStatusCode";

	public static final String NAME_CONSTANT = "name";

	private String houseStatusCode;

	private String name;

	public CodeHouseStatus()
	{}

	public CodeHouseStatus( String houseStatusCode )
	{
		this.houseStatusCode = houseStatusCode;
	}

	@Id
	@Column( name = "house_status_code", unique = true, nullable = false, length = 20 )
	public String getHouseStatusCode()
	{
		return houseStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setHouseStatusCode( String houseStatusCode )
	{
		this.houseStatusCode = houseStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}