package com.megabank.olp.common.service.bean.landing;

import com.megabank.olp.base.bean.BaseBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class BaseColumnResBean extends BaseBean
{
	private Long columnId;

	private String columnTitle;

	private String columnName;

	private String columnType;

	public BaseColumnResBean()
	{
		// default constructor
	}

	public Long getColumnId()
	{
		return columnId;
	}

	public String getColumnName()
	{
		return columnName;
	}

	public String getColumnTitle()
	{
		return columnTitle;
	}

	public String getColumnType()
	{
		return columnType;
	}

	public void setColumnId( Long columnId )
	{
		this.columnId = columnId;
	}

	public void setColumnName( String columnName )
	{
		this.columnName = columnName;
	}

	public void setColumnTitle( String columnTitle )
	{
		this.columnTitle = columnTitle;
	}

	public void setColumnType( String columnType )
	{
		this.columnType = columnType;
	}

}
