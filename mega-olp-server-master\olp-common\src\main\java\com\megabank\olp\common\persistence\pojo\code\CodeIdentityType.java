package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeIdentityType is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_identity_type" )
public class CodeIdentityType extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_identity_type";

	public static final String IDENTITY_TYPE_CONSTANT = "identityType";

	public static final String NAME_CONSTANT = "name";

	private String identityType;

	private String name;

	public CodeIdentityType()
	{}

	public CodeIdentityType( String identityType )
	{
		this.identityType = identityType;
	}

	@Id
	@Column( name = "identity_type", unique = true, nullable = false, length = 20 )
	public String getIdentityType()
	{
		return identityType;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	public void setIdentityType( String identityType )
	{
		this.identityType = identityType;
	}

	public void setName( String name )
	{
		this.name = name;
	}
}