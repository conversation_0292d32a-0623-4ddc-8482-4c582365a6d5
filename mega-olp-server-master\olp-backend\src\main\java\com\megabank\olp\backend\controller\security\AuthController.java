package com.megabank.olp.backend.controller.security;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.megabank.olp.backend.controller.BaseBackendController;
import com.megabank.olp.base.service.jwt.encrypt.BackendJwtService;

@RestController
@RequestMapping( "auth" )
public class AuthController extends BaseBackendController
{
	@Autowired
	private BackendJwtService jwtUtils;

	@PostMapping( "getEmployee" )
	public Map<String, Object> getEmployee()
	{
		Map<String, Object> map = new HashMap<>();
		map.put( "employeeId", getEmployeeId() );
		map.put( "employeeName", getEmployeeName() );
		map.put( "branchBankId", getBranchBankId() );
		map.put( "branchBankCode", getBranchBankCode() );
		map.put( "permissions", getPermissions( getGrantedAuthorities() ) );
		map.put( "headOffice", headOffice() );

		return getResponseMap( map );
	}

	@PostMapping( "logoutSuccess" )
	public Map<String, Object> logoutSuccess()
	{
		return getResponseMap();
	}

	private List<String> getPermissions( List<GrantedAuthority> grantedAuthorities )
	{
		List<String> roles = new ArrayList<>();

		for( GrantedAuthority grantedAuthority : grantedAuthorities )
			roles.add( grantedAuthority.getAuthority() );

		return roles;
	}
}
