package com.megabank.olp.client.sender.mydata.txid;

import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.client.sender.mydata.BaseMyDataClient;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdArgBean;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdReqBean;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdResBean;
import com.megabank.olp.client.sender.mydata.txid.bean.MyDataTxIdResultBean;
import com.megabank.olp.client.utility.enums.ClientMyDataErrorEnum;

@Component
public class MyDataTxIdClient extends BaseMyDataClient<MyDataTxIdArgBean, MyDataTxIdReqBean, MyDataTxIdResBean, MyDataTxIdResultBean>
{

	@Override
	protected ErrorEnum getMyDataErrorEnum()
	{
		return ClientMyDataErrorEnum.GETTED_TXID_COMMON;
	}

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000" };
	}

	@Override
	protected MyDataTxIdResultBean getResultBean( MyDataTxIdResBean resBean )
	{
		MyDataTxIdResultBean resultBean = new MyDataTxIdResultBean();
		resultBean.setTxId( resBean.getTxId() );

		return resultBean;
	}

	@Override
	protected String getSimulatorCode( MyDataTxIdArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/getTxId";
	}

	@Override
	protected MyDataTxIdReqBean transArg2Req( MyDataTxIdArgBean argBean )
	{
		MyDataTxIdReqBean reqBean = new MyDataTxIdReqBean();
		reqBean.setServiceId( argBean.getServiceId() );

		return reqBean;
	}

}
