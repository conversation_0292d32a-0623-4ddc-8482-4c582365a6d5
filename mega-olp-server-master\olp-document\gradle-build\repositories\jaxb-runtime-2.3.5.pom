<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2021 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind.mvn</groupId>
        <artifactId>jaxb-runtime-parent</artifactId>
        <version>2.3.5</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>jaxb-runtime</artifactId>

    <packaging>jar</packaging>
    <name>JAXB Runtime</name>
    <description>JAXB (JSR 222) Reference Implementation</description>
    <url>https://eclipse-ee4j.github.io/jaxb-ri/</url>

    <properties>
        <spotbugs.exclude>${project.basedir}/exclude-runtime.xml</spotbugs.exclude>
        <argLine>
            --add-opens org.glassfish.jaxb.runtime/com.sun.xml.bind.v2=java.xml.bind
            --add-opens org.glassfish.jaxb.runtime/com.sun.xml.bind.v2.schemagen=java.xml.bind
            --add-opens org.glassfish.jaxb.runtime/com.sun.xml.bind.v2.schemagen.xmlidref=java.xml.bind
            --add-opens java.base/java.lang=org.glassfish.jaxb.runtime
            --add-opens java.base/java.lang.reflect=org.glassfish.jaxb.runtime
            --add-opens org.glassfish.jaxb.runtime/com.sun.xml.bind.v2.runtime.reflect.opt=java.xml.bind
        </argLine>
    </properties>

    <dependencies>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>txw2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.istack</groupId>
            <artifactId>istack-commons-runtime</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jvnet.staxex</groupId>
            <artifactId>stax-ex</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.fastinfoset</groupId>
            <artifactId>FastInfoset</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.sun.activation</groupId>
            <artifactId>jakarta.activation</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-mr-resource</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${mrjar.sourceDirectory}</directory>
                                    <targetPath>META-INF/versions</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile-mr</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <compileSourceRoots>
                                <compileSourceRoot>${mrjar.sourceDirectory}/${upper.java.level}</compileSourceRoot>
                            </compileSourceRoots>
                            <outputDirectory>${project.build.outputDirectory}/META-INF/versions/${upper.java.level}</outputDirectory>
                            <source>${upper.java.level}</source>
                            <target>${upper.java.level}</target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Implementation-Vendor>${vendor.name}</Implementation-Vendor>
                        <Implementation-Vendor-Id>${project.groupId}</Implementation-Vendor-Id>
                        <Implementation-Build-Id>${project.version} - ${buildNumber}</Implementation-Build-Id>
                        <Multi-Release>true</Multi-Release>
                        <Import-Package>
                            javax.activation;version=!,
                            sun.misc;resolution:=optional,
                            jdk.internal.misc;resolution:=optional,
                            *
                        </Import-Package>
                        <Export-Package>
                            !META-INF.*,
                            *
                        </Export-Package>
                        <DynamicImport-Package>*</DynamicImport-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <workingDirectory>target/test-out</workingDirectory>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
