package com.megabank.olp.client.sender.micro.apply.management.houseloan.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.megabank.olp.base.bean.BaseBean;

public class HouseLoanDetailResultBean extends BaseBean
{
	@JsonProperty( "info" )
	private HouseLoanCaseInfoBean houseLoanCaseInfoBean;

	@JsonProperty( "basic" )
	private HouseLoanBasicBean houseLoanBasicBean;

	@JsonProperty( "contact" )
	private HouseLoanContactBean houseLoanContactBean;

	@JsonProperty( "job" )
	private HouseLoanJobBean houseLoanJobBean;

	@JsonProperty( "content" )
	private HouseLoanContentBean houseLoanContentBean;

	@JsonProperty( "relation" )
	private List<HouseLoanRelationBean> houseLoanRelationBeans;

	@JsonProperty( "served" )
	private List<HouseLoanServedBean> houseLoanServedBeans;

	@JsonProperty( "guarantee" )
	private HouseLoanGuaranteeBean houseLoanGuaranteeBean;

	public HouseLoanDetailResultBean()
	{
		// default constructor
	}

	public HouseLoanBasicBean getHouseLoanBasicBean()
	{
		return houseLoanBasicBean;
	}

	public HouseLoanCaseInfoBean getHouseLoanCaseInfoBean()
	{
		return houseLoanCaseInfoBean;
	}

	public HouseLoanContactBean getHouseLoanContactBean()
	{
		return houseLoanContactBean;
	}

	public HouseLoanContentBean getHouseLoanContentBean()
	{
		return houseLoanContentBean;
	}

	public HouseLoanGuaranteeBean getHouseLoanGuaranteeBean()
	{
		return houseLoanGuaranteeBean;
	}

	public HouseLoanJobBean getHouseLoanJobBean()
	{
		return houseLoanJobBean;
	}

	public List<HouseLoanRelationBean> getHouseLoanRelationBeans()
	{
		return houseLoanRelationBeans;
	}

	public List<HouseLoanServedBean> getHouseLoanServedBeans()
	{
		return houseLoanServedBeans;
	}

	public void setHouseLoanBasicBean( HouseLoanBasicBean houseLoanBasicBean )
	{
		this.houseLoanBasicBean = houseLoanBasicBean;
	}

	public void setHouseLoanCaseInfoBean( HouseLoanCaseInfoBean houseLoanCaseInfoBean )
	{
		this.houseLoanCaseInfoBean = houseLoanCaseInfoBean;
	}

	public void setHouseLoanContactBean( HouseLoanContactBean houseLoanContactBean )
	{
		this.houseLoanContactBean = houseLoanContactBean;
	}

	public void setHouseLoanContentBean( HouseLoanContentBean houseLoanContentBean )
	{
		this.houseLoanContentBean = houseLoanContentBean;
	}

	public void setHouseLoanGuaranteeBean( HouseLoanGuaranteeBean houseLoanGuaranteeBean )
	{
		this.houseLoanGuaranteeBean = houseLoanGuaranteeBean;
	}

	public void setHouseLoanJobBean( HouseLoanJobBean houseLoanJobBean )
	{
		this.houseLoanJobBean = houseLoanJobBean;
	}

	public void setHouseLoanRelationBeans( List<HouseLoanRelationBean> houseLoanRelationBeans )
	{
		this.houseLoanRelationBeans = houseLoanRelationBeans;
	}

	public void setHouseLoanServedBeans( List<HouseLoanServedBean> houseLoanServedBeans )
	{
		this.houseLoanServedBeans = houseLoanServedBeans;
	}

}