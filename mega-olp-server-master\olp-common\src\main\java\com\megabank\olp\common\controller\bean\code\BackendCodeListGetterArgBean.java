package com.megabank.olp.common.controller.bean.code;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class BackendCodeListGetterArgBean
{
	@NotBlank
	private String codeType;

	@NotNull
	private Long branchBankId;
	
	private Long subBranchBankId;
	
	private String originalBranchBankCode;
	
	private String recipient;
	
	private String loanType;

	public BackendCodeListGetterArgBean()
	{}

	public Long getBranchBankId()
	{
		return branchBankId;
	}

	public String getCodeType()
	{
		return codeType;
	}
	
	public Long getSubBranchBankId()
	{
		return subBranchBankId;
	}

	public String getRecipient()
	{
		return recipient;
	}

	public String getLoanType()
	{
		return loanType;
	}

	public String getOriginalBranchBankCode()
	{
		return originalBranchBankCode;
	}

	public void setBranchBankId( Long branchBankId )
	{
		this.branchBankId = branchBankId;
	}

	public void setCodeType( String codeType )
	{
		this.codeType = codeType;
	}

	public void setSubBranchBankId( Long subBranchBankId )
	{
		this.subBranchBankId = subBranchBankId;
	}

	public void setRecipient( String recipient )
	{
		this.recipient = recipient;
	}

	public void setLoanType( String loanType )
	{
		this.loanType = loanType;
	}

	public void setOriginalBranchBankCode( String originalBranchBankCode )
	{
		this.originalBranchBankCode = originalBranchBankCode;
	}
}
