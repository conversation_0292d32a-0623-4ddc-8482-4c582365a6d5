/**
 *
 */
package com.megabank.olp.client.sender.sso.login.bean;

import com.megabank.olp.client.sender.sso.BaseSsoArgBean;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2021
 */
public class SsoLoginArgBean extends BaseSsoArgBean
{
	private String trackingIxd;

	private String clientIp;

	private String xAuthToken;

	private String loginType;

	public SsoLoginArgBean()
	{}

	public String getClientIp()
	{
		return clientIp;
	}

	public String getLoginType()
	{
		return loginType;
	}

	public String getTrackingIxd()
	{
		return trackingIxd;
	}

	public String getXAuthToken()
	{
		return xAuthToken;
	}

	public void setClientIp( String clientIp )
	{
		this.clientIp = clientIp;
	}

	public void setLoginType( String loginType )
	{
		this.loginType = loginType;
	}

	public void setTrackingIxd( String trackingIxd )
	{
		this.trackingIxd = trackingIxd;
	}

	public void setXAuthToken( String xAuthToken )
	{
		this.xAuthToken = xAuthToken;
	}

}
