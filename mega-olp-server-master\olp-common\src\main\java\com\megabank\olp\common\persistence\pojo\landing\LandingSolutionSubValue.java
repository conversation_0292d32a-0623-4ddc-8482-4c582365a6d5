package com.megabank.olp.common.persistence.pojo.landing;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The LandingSolutionSubValue is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "landing_solution_sub_value" )
public class LandingSolutionSubValue extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "landing_solution_sub_value";

	public static final String SOLUTION_SUB_VALUE_ID_CONSTANT = "solutionSubValueId";

	public static final String LANDING_SOLUTION_VALUE_CONSTANT = "landingSolutionValue";

	public static final String VALUE_CONSTANT = "value";

	private Long solutionSubValueId;

	private transient LandingSolutionValue landingSolutionValue;

	private String value;

	public LandingSolutionSubValue()
	{}

	public LandingSolutionSubValue( Long solutionSubValueId )
	{
		this.solutionSubValueId = solutionSubValueId;
	}

	@ManyToOne( fetch = FetchType.LAZY )
	@JoinColumn( name = "solution_value_id", nullable = false )
	public LandingSolutionValue getLandingSolutionValue()
	{
		return landingSolutionValue;
	}

	@Id
	@GeneratedValue( strategy = IDENTITY )
	@Column( name = "solution_sub_value_id", unique = true, nullable = false )
	public Long getSolutionSubValueId()
	{
		return solutionSubValueId;
	}

	@Column( name = "value", nullable = false )
	public String getValue()
	{
		return value;
	}

	public void setLandingSolutionValue( LandingSolutionValue landingSolutionValue )
	{
		this.landingSolutionValue = landingSolutionValue;
	}

	public void setSolutionSubValueId( Long solutionSubValueId )
	{
		this.solutionSubValueId = solutionSubValueId;
	}

	public void setValue( String value )
	{
		this.value = value;
	}
}