package com.megabank.olp.client.sender.micro.version.bean;

import com.megabank.olp.base.bean.BaseBean;

public class VersionResultBean extends BaseBean
{
	private String buildEnviron;

	private String buildDate;

	private String buildNumber;

	public VersionResultBean()
	{}

	public String getBuildDate()
	{
		return buildDate;
	}

	public String getBuildEnviron()
	{
		return buildEnviron;
	}

	public String getBuildNumber()
	{
		return buildNumber;
	}

	public void setBuildDate( String buildDate )
	{
		this.buildDate = buildDate;
	}

	public void setBuildEnviron( String buildEnviron )
	{
		this.buildEnviron = buildEnviron;
	}

	public void setBuildNumber( String buildNumber )
	{
		this.buildNumber = buildNumber;
	}

}
