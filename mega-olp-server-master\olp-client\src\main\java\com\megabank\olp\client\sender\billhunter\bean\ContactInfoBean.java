package com.megabank.olp.client.sender.billhunter.bean;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.megabank.olp.base.bean.BaseBean;

@XmlAccessorType( XmlAccessType.FIELD )
@XmlType( propOrder = { "fromBean", "toBean" } )
public class ContactInfoBean extends BaseBean
{
	@XmlElement( name = "From" )
	private FromBean fromBean;

	@XmlElement( name = "To" )
	private ToBean toBean;

	public ContactInfoBean()
	{}

	public FromBean getFromBean()
	{
		return fromBean;
	}

	public ToBean getToBean()
	{
		return toBean;
	}

	public void setFromBean( FromBean fromBean )
	{
		this.fromBean = fromBean;
	}

	public void setToBean( ToBean toBean )
	{
		this.toBean = toBean;
	}
}
