package com.megabank.olp.common.service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.megabank.olp.common.persistence.dao.generated.code.*;
import com.megabank.olp.common.persistence.pojo.code.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.megabank.olp.base.enums.ApplyStatusGroupEnum;
import com.megabank.olp.base.enums.CommonErrorEnum;
import com.megabank.olp.base.enums.LoanTypeEnum;
import com.megabank.olp.base.enums.NotificationStatusEnum;
import com.megabank.olp.base.enums.RecipientSystemEnum;
import com.megabank.olp.base.enums.UploadStatusEnum;
import com.megabank.olp.base.enums.UserSubTypeEnum;
import com.megabank.olp.base.enums.UserTypeEnum;
import com.megabank.olp.base.exception.MyRuntimeException;
import com.megabank.olp.base.layer.BaseService;
import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.common.persistence.dao.mixed.BranchBankDAO;
import com.megabank.olp.common.persistence.dao.mixed.OtherBankEddaDAO;
import com.megabank.olp.common.service.bean.code.BranchBankResBean;
import com.megabank.olp.common.service.bean.code.BranchResBean;
import com.megabank.olp.common.service.bean.code.CodeResBean;
import com.megabank.olp.common.utility.enums.AllowedUserEnum;
import com.megabank.olp.common.utility.enums.CodeTypeEnum;

@Service
@Transactional
public class CodeService extends BaseService
{

	private static final String CODE_LIST_CONSTANT = "codeList";

	private static final String CODE_TYPE_CONSTANT = "codeType";

	@Autowired
	private BranchBankDAO branchBankDAO;

	@Autowired
	private CodeAmountPerMonthDAO codeAmountPerMonthDAO;

	@Autowired
	private CodeBusinessDayDAO codeBusinessDayDAO;

	@Autowired
	private CodeBranchBankDAO codeBranchBankDAO;

	@Autowired
	private CodeCityDAO codeCityDAO;

	@Autowired
	private CodeContactTimeDAO codeContactTimeDAO;

	@Autowired
	private CodeContractNotificationDAO codeContractNotificationDAO;

	@Autowired
	private CodeEducationLevelDAO codeEducationLevelDAO;

	@Autowired
	private CodeGracePeriodDAO codeGracePeriodDAO;

	@Autowired
	private CodeGuarantyReasonDAO codeGuarantyReasonDAO;

	@Autowired
	private CodeHouseStatusDAO codeHouseStatusDAO;

	@Autowired
	private CodeJobSubTypeDAO codeJobSubTypeDAO;

	@Autowired
	private CodeJobTypeDAO codeJobTypeDAO;

	@Autowired
	private CodeLandingActionDAO codeLandingActionDAO;

	@Autowired
	private CodeLandingRequestTypeDAO codeLandingRequestTypeDAO;

	@Autowired
	private CodeLoanPeriodDAO codeLoanPeriodDAO;

	@Autowired
	private CodeLoanPurposeDAO codeLoanPurposeDAO;

	@Autowired
	private CodeLoanTypeDAO codeLoanTypeDAO;

	@Autowired
	private CodeMarriageStatusDAO codeMarriageStatusDAO;

	@Autowired
	private CodeMortgageTypeDAO codeMortgageTypeDAO;

	@Autowired
	private CodeNationalityDAO codeNationalityDAO;

	@Autowired
	private CodeNonPrivateUsageTypeDAO codeNonPrivateUsageTypeDAO;

	@Autowired
	private CodeNonPrivateUsageSubTypeDAO codeNonPrivateUsageSubTypeDAO;

	@Autowired
	private CodeNotificationDAO codeNotificationDAO;

	@Autowired
	private CodeOtherBankDAO codeOtherBankDAO;

	@Autowired
	private CodePrivateUsageTypeDAO codePrivateUsageTypeDAO;

	@Autowired
	private CodeProcessDAO codeProcessDAO;

	@Autowired
	private CodeRateAdjustmentNotificationDAO codeRateAdjustmentNotificationDAO;

	@Autowired
	private CodeRelationBorrowerTypeDAO codeRelationBorrowerTypeDAO;

	@Autowired
	private CodeRelationTypeDAO codeRelationTypeDAO;

	@Autowired
	private CodeRepresentativeTypeDAO codeRepresentativeTypeDAO;

	@Autowired
	private CodeResidenceStatusDAO codeResidenceStatusDAO;

	@Autowired
	private CodeServiceAssociateDeptDAO codeServiceAssociateDeptDAO;

	@Autowired
	private CodeSexDAO codeSexDAO;

	@Autowired
	private CodeTitleTypeDAO codeTitleTypeDAO;

	@Autowired
	private CodeTownDAO codeTownDAO;

	@Autowired
	private CodeTransmissionStatusDAO codeTransmissionStatusDAO;

	@Autowired
	private CodeUserSubTypeDAO codeUserSubTypeDAO;

	@Autowired
	private CodeUserTypeDAO codeUserTypeDAO;

	@Autowired
	private OtherBankEddaDAO otherBankEddaDAO;

	@Autowired
	private CodeListDAO codeListDAO;

	@Autowired
	private CodeCaseSourceDAO codeCaseSourceDAO;

	/**
	 * 取得對保撥款日
	 *
	 * @return
	 */
	public List<String> getAppropirationDateList()
	{
		List<CodeBusinessDay> codeBusinessDays = getCodeBusinessDays();

		return codeBusinessDays.stream().map( codeBusinessDay -> CommonDateStringUtils.transDate2String( codeBusinessDay.getBusinessDayCode() ) )
					.collect( Collectors.toList() );
	}

	/**
	 * 取得管理後台下拉式選單內容
	 *
	 * @param codeType
	 * @param branchBankId
	 * @return
	 */
	public List<CodeResBean> getBackendCodeList( String codeType, Long branchBankId, Long subBranchBankId, String originalBranchBankCode, String loanType, String recipient )
	{
		// 信用卡電銷處 code: 109
		if( RecipientSystemEnum.ILOAN.getContext().equalsIgnoreCase( recipient ) && LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType )
			&& (subBranchBankId != -1L || "109".equals( originalBranchBankCode ) ) )
		{
			return getIntroduceBranchList( branchBankId, subBranchBankId, originalBranchBankCode, loanType );
		}

		if( CodeTypeEnum.HL_BRANCH_BANK.getContext().equals( codeType ) )
			return getCodeBranchBank( LoanTypeEnum.HOUSE_LOAN.getContext(), branchBankId );

		if( CodeTypeEnum.PL_BRANCH_BANK.getContext().equals( codeType ) )
			return getCodeBranchBank( LoanTypeEnum.PERSONAL_LOAN.getContext(), branchBankId );

		throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH_ARGUMENTS,
									  new String[]{ CODE_LIST_CONSTANT, CODE_TYPE_CONSTANT + "=" + codeType } );

	}

	/**
	 * 取得貸款申請派案分行
	 *
	 * @param townCode
	 * @param bankType
	 * @return
	 */
	public List<BranchBankResBean> getBranchBankList( String townCode, String bankType )
	{
		if( CodeTypeEnum.HL_BRANCH_BANK.getContext().equals( bankType ) )
			return getBranchBankList( townCode, LoanTypeEnum.HOUSE_LOAN.getContext(), false );

		if( CodeTypeEnum.PL_BRANCH_BANK.getContext().equals( bankType ) )
			return getBranchBankList( townCode, LoanTypeEnum.PERSONAL_LOAN.getContext(), false );

		if( CodeTypeEnum.EMP_PL_BRANCH_BANK.getContext().equals( bankType ) )
			return getBranchBankList( townCode, LoanTypeEnum.PERSONAL_LOAN.getContext(), true );

		throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH_ARGUMENTS, new String[]{ "branchBankList", "bankType=" + bankType } );

	}

	public List<BranchResBean> getBranchList( String bno, Boolean withDefault )
	{
		List<CodeBranchBank> codeBranchBankList = codeBranchBankDAO.getPojosByBankCodeActiveBusinessUnit( bno );

		if( codeBranchBankList.isEmpty() && withDefault.booleanValue() )
			codeBranchBankList = codeBranchBankDAO.getPojosByBankCodeActiveBusinessUnit( "229" ); // 如果沒撈到就回覆「大安分行」

		List<BranchResBean> resBeans = new ArrayList<>();

		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			BranchResBean resBean = new BranchResBean();
			resBean.setCode( codeBranchBank.getBranchBankId().toString() );
			resBean.setName( codeBranchBank.getName() );
			resBean.setAddress( codeBranchBank.getAddress() );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	public List<BranchResBean> getActiveBranchList( )
	{
		List<CodeBranchBank> codeBranchBankList = codeBranchBankDAO.getPojosByDisabled( false );

		List<BranchResBean> resBeans = new ArrayList<>();

		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			BranchResBean resBean = new BranchResBean();
			resBean.setCode( codeBranchBank.getBranchBankId().toString() );
			resBean.setName( codeBranchBank.getName() );
			resBean.setAddress( codeBranchBank.getAddress() );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	/**
	 * 取得下拉式選單內容
	 *
	 * @param codeType
	 * @return
	 */
	public List<CodeResBean> getCodeList( String codeType )
	{

		if( CodeTypeEnum.AMOUNT_PER_MONTH.getContext().equals( codeType ) )
			return getCodeAmountPerMonth();

		if( CodeTypeEnum.APPLY_STATUS.getContext().equals( codeType ) )
			return getCodeApplyStatus();

		if( CodeTypeEnum.CALL_BACK_TIME.getContext().equals( codeType ) )
			return getCodeCallBackTime();

		if( CodeTypeEnum.CITY.getContext().equals( codeType ) )
			return getCodeCity();

		if( CodeTypeEnum.CONTACT_TIME.getContext().equals( codeType ) )
			return getCodeContactTime();

		if( CodeTypeEnum.CONTRACT_NOTIFICATION.getContext().equals( codeType ) )
			return getCodeContractNotification();

		if( CodeTypeEnum.EDUCATION_LEVEL.getContext().equals( codeType ) )
			return getCodeEducationLevel();

		if( CodeTypeEnum.EMP_PL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankCity( LoanTypeEnum.PERSONAL_LOAN.getContext(), true );

		if( CodeTypeEnum.GRACE_PERIOD.getContext().equals( codeType ) )
			return getCodeGracePeriod();

		if( CodeTypeEnum.GUARANTY_REASON.getContext().equals( codeType ) )
			return getCodeGuarantyReason();

		if( CodeTypeEnum.HL_BRANCH_BANK.getContext().equals( codeType ) )
			return getCodeBranchBank( LoanTypeEnum.HOUSE_LOAN.getContext() );

		if( CodeTypeEnum.HL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankCity( LoanTypeEnum.HOUSE_LOAN.getContext(), false );

		if( CodeTypeEnum.HL_LOAN_PERIOD.getContext().equals( codeType ) )
			return getCodeLoanPeriod( 1, 40 );

		if( CodeTypeEnum.HL_LOAN_PURPOSE.getContext().equals( codeType ) )
			return getCodeLoanPurpose( LoanTypeEnum.HOUSE_LOAN.getContext() );

		if( CodeTypeEnum.HL_USER_TYPE.getContext().equals( codeType ) )
			return getCodeUserType( LoanTypeEnum.HOUSE_LOAN.getContext() );

		if( CodeTypeEnum.HOUSE_STATUS.getContext().equals( codeType ) )
			return getCodeHouseStatus();

		if( CodeTypeEnum.JOB_TYPE.getContext().equals( codeType ) )
			return getCodeJobType();

		if( CodeTypeEnum.LANDING_ACTION.getContext().equals( codeType ) )
			return getCodeLandingAction();

		if( CodeTypeEnum.LANDING_REQUEST_TYPE.getContext().equals( codeType ) )
			return getCodeLandingRequestType();

		if( CodeTypeEnum.LOAN_TYPE.getContext().equals( codeType ) )
			return getCodeLoanType();

		if( CodeTypeEnum.MARRIAGE_STATUS.getContext().equals( codeType ) )
			return getCodeMarriageStatus();

		if( CodeTypeEnum.MORTGAGE_TYPE.getContext().equals( codeType ) )
			return getCodeMortgageType();

		if( CodeTypeEnum.NATIONALITY.getContext().equals( codeType ) )
			return getCodeNationality();

		if( CodeTypeEnum.NON_PRIVATE_USAGE_TYPE.getContext().equals( codeType ) )
			return getCodeNonPrivateUsageType();

		if( CodeTypeEnum.NON_PRIVATE_USAGE_SUBTYPE.getContext().equals( codeType ) )
			return getCodeNonPrivateUsageSubType();

		if( CodeTypeEnum.NOTIFICATION_STATUS.getContext().equals( codeType ) )
			return getCodeNotificationStatus();

		if( CodeTypeEnum.OTHER_BANK.getContext().equals( codeType ) )
			return getCodeOtherBank();

		if( CodeTypeEnum.PL_BRANCH_BANK.getContext().equals( codeType ) )
			return getCodeBranchBank( LoanTypeEnum.PERSONAL_LOAN.getContext() );

		if( CodeTypeEnum.PL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankCity( LoanTypeEnum.PERSONAL_LOAN.getContext(), false );

		if( CodeTypeEnum.PL_LOAN_PERIOD.getContext().equals( codeType ) )
			return getCodeLoanPeriod( 2, 7 );

		if( CodeTypeEnum.PL_LOAN_PURPOSE.getContext().equals( codeType ) )
			return getCodeLoanPurpose( LoanTypeEnum.PERSONAL_LOAN.getContext() );

		if( CodeTypeEnum.PL_USER_TYPE.getContext().equals( codeType ) )
			return getCodeUserType( LoanTypeEnum.PERSONAL_LOAN.getContext() );

		if( CodeTypeEnum.PRIVATE_USAGE_TYPE.getContext().equals( codeType ) )
			return getCodePrivateUsageType();

		if( CodeTypeEnum.PROCESS_STATUS.getContext().equals( codeType ) )
			return getCodeProcessStatus();

		if( CodeTypeEnum.RATE_ADJUSTMENT_NOTIFICATION.getContext().equals( codeType ) )
			return getCodeRateAdjustmentNotification();

		if( CodeTypeEnum.RECEIPT_NOTIFICATION.getContext().equals( codeType ) )
			return getCodeNotification();

		if( CodeTypeEnum.RELATION_BORROWER_TYPE.getContext().equals( codeType ) )
			return getCodeRelationBorrowerType();

		if( CodeTypeEnum.RELATION_TYPE.getContext().equals( codeType ) )
			return getCodeRelationType();

		if( CodeTypeEnum.REPRESENTATIVE_TYPE.getContext().equals( codeType ) )
			return getCodeRepresentativeType();

		if( CodeTypeEnum.RESIDENCE_STATUS.getContext().equals( codeType ) )
			return getCodeResidenceStatus();

		if( CodeTypeEnum.SERVICE_ASSOCIATE_DEPT.getContext().equals( codeType ) )
			return getCodeServiceAssociateDept();

		if( CodeTypeEnum.SEX.getContext().equals( codeType ) )
			return getCodeSex();

		if( CodeTypeEnum.TITLE_TYPE.getContext().equals( codeType ) )
			return getCodeTitleType();

		if( CodeTypeEnum.TRANSMISSION_STATUS.getContext().equals( codeType ) )
			return getCodeTransmissionStatus();

		if( CodeTypeEnum.UPLOAD_STATUS.getContext().equals( codeType ) )
			return getCodeUploadStatus();

		if( CodeTypeEnum.OTHER_BANK_EDDA.getContext().equals( codeType ) )
			return getCodeOtherBankEdda();// edda

		if( CodeTypeEnum.CASE_SOURCE.getContext().equals( codeType ) )
			return getCodeCaseSource();

		if( CodeTypeEnum.HOUSE_CONTACT_LOAN_PURPOSE.getContext().equals( codeType ) )
			return getHouseContactLoanPurpose();

		throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH_ARGUMENTS,
									  new String[]{ CODE_LIST_CONSTANT, CODE_TYPE_CONSTANT + "=" + codeType } );
	}

	/**
	 * 取得子類別下拉式選單
	 *
	 * @param codeType
	 * @param codeId
	 * @return
	 */
	public List<CodeResBean> getSubCodeList( String codeType, String codeId )
	{
		if( CodeTypeEnum.HL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankTown( LoanTypeEnum.HOUSE_LOAN.getContext(), codeId, false );

		if( CodeTypeEnum.HL_USER_TYPE.getContext().equals( codeType ) )
			return getCodeUserSubType( LoanTypeEnum.HOUSE_LOAN.getContext(), codeId );

		if( CodeTypeEnum.PL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankTown( LoanTypeEnum.PERSONAL_LOAN.getContext(), codeId, false );

		if( CodeTypeEnum.PL_USER_TYPE.getContext().equals( codeType ) )
			return getCodeUserSubType( LoanTypeEnum.PERSONAL_LOAN.getContext(), codeId );

		if( CodeTypeEnum.EMP_PL_BRANCH_BANK_CITY.getContext().equals( codeType ) )
			return getCodeBranchBankTown( LoanTypeEnum.PERSONAL_LOAN.getContext(), codeId, true );

		if( CodeTypeEnum.CITY.getContext().equals( codeType ) )
			return getCodeTown( codeId );

		if( CodeTypeEnum.JOB_TYPE.getContext().equals( codeType ) )
			return getCodeJobSubType( codeId );

		if( CodeTypeEnum.OTHER_BANK_EDDA_BRANCHCODE.getContext().equals( codeType ) )
			return getCodeOtherBankEddaBranchcode( codeId );// edda

		throw new MyRuntimeException( CommonErrorEnum.DATA_NOT_FOUND_WITH_ARGUMENTS,
									  new String[]{ CODE_LIST_CONSTANT, CODE_TYPE_CONSTANT + "=" + codeType } );

	}
	
	/**
	 * 取得管理後台下拉式選單內容
	 *
	 * @return
	 */
	public List<CodeResBean> getIntroduceBranchList( Long branchId, Long subBranchId, String originalBranchBankCode, String loanType )
	{
		List<CodeBranchBank> codeBranchBankList = null;
		
		// DS(direct-sale) 信用卡電銷人員(branch bank code:109) 僅能查詢到自己引介的案件
		if( -1L != subBranchId || "109".equals( originalBranchBankCode ) )
		{
			codeBranchBankList = new ArrayList<>();
		}
		// 消金處 or 資訊處 可查詢到所有單位引介的案件。
		else if( "943".equals( originalBranchBankCode )
			|| "900".equals( originalBranchBankCode ) )
		{
			codeBranchBankList = branchBankDAO.getList( loanType, null, null );
		}
		// 其他分行各分行所屬人員僅能查詢到自己分行引介的案件
		else
		{
			codeBranchBankList = branchBankDAO.getList( loanType, branchId, null );
		}
		
		List<CodeResBean> codeResBeans = new ArrayList<>();
		
		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeBranchBank.getBranchBankId().toString() );
			codeResBean.setName( codeBranchBank.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}
	
	/**
	 * 取得行銷人員的行銷分行
	 * @param empId
	 * @return
	 */
	public BranchResBean getEmpBranch( String empId )
	{
		List<CodeBranchBank> branchList = branchBankDAO.getBranchBankByEmpId( Objects.isNull( empId ) ? "" : empId );
		
		BranchResBean resBean = new BranchResBean();
		for( CodeBranchBank branch : branchList )
		{
			resBean.setCode( branch.getBankCode() );
			resBean.setName( branch.getName() );			
		}
		
		return resBean;
	}

	/**
	 * 取得允許使用者類型
	 *
	 * @param isEmployee
	 * @return
	 */
	private List<Integer> getAllowedUser( Boolean isEmployee )
	{
		List<Integer> allowedUser = new ArrayList<>();

		if( BooleanUtils.isTrue( isEmployee ) )
			allowedUser.add( AllowedUserEnum.EMPLOYEE.getContext() );

		allowedUser.add( AllowedUserEnum.USER_AND_EMPLOYEE.getContext() );

		return allowedUser;
	}

	/**
	 * 取得客戶可用分行列表
	 *
	 * @param townCode
	 * @param loanType
	 * @param isEmployee
	 * @return
	 */
	private List<BranchBankResBean> getBranchBankList( String townCode, String loanType, Boolean isEmployee )
	{
		List<CodeBranchBank> codeBranchBankList = branchBankDAO.getListByTownCode( loanType, townCode, getAllowedUser( isEmployee ) );

		List<BranchBankResBean> resBeans = new ArrayList<>();

		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			BranchBankResBean resBean = new BranchBankResBean();
			resBean.setCode( codeBranchBank.getBranchBankId().toString() );
			resBean.setName( codeBranchBank.getName() );
			resBean.setAddress( codeBranchBank.getAddress() );

			resBeans.add( resBean );
		}

		return resBeans;
	}

	/**
	 * 取得本行帳戶預期月平均交易金額
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeAmountPerMonth()
	{
		List<CodeAmountPerMonth> codeAmountPerMonthList = codeAmountPerMonthDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeAmountPerMonth codeAmountPerMonth : codeAmountPerMonthList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeAmountPerMonth.getAmountPerMonthCode() );
			codeResBean.setName( codeAmountPerMonth.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得案件申請狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeApplyStatus()
	{
		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( ApplyStatusGroupEnum applyStatusGroupEnum : ApplyStatusGroupEnum.values() )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( applyStatusGroupEnum.getContext() );
			codeResBean.setName( applyStatusGroupEnum.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得貸款承辦分行
	 *
	 * @param loanType
	 * @return
	 */
	private List<CodeResBean> getCodeBranchBank( String loanType )
	{
		List<CodeBranchBank> codeBranchBankList = branchBankDAO.getList( loanType );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeBranchBank.getBranchBankId().toString() );
			codeResBean.setName( codeBranchBank.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得管理後臺使用分行列表
	 *
	 * @param loanType
	 * @param branchBankId
	 * @return
	 */
	private List<CodeResBean> getCodeBranchBank( String loanType, Long branchBankId )
	{
		List<CodeBranchBank> codeBranchBankList = getCodeBranchBankList( loanType, branchBankId );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeBranchBank codeBranchBank : codeBranchBankList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeBranchBank.getBranchBankId().toString() );
			codeResBean.setName( codeBranchBank.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得分行區域
	 *
	 * @param loanType
	 * @param isEmployee
	 * @return
	 */
	private List<CodeResBean> getCodeBranchBankCity( String loanType, Boolean isEmployee )
	{
		List<CodeCity> codeCityList = branchBankDAO.getBranchBankCity( loanType, getAllowedUser( isEmployee ) );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeCity codeCity : codeCityList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeCity.getCityCode() );
			codeResBean.setName( codeCity.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	private List<CodeBranchBank> getCodeBranchBankList( String loanType, Long branchBankId )
	{
		CodeBranchBank codeBranchBank = codeBranchBankDAO.read( branchBankId );

		if( codeBranchBank.isHeadOffice() )
			return branchBankDAO.getList( loanType, null, null );

		return branchBankDAO.getList( loanType, branchBankId, codeBranchBank.isHeadOffice() );
	}

	/**
	 * 取得分行區域
	 *
	 * @param loanType
	 * @param cityCode
	 * @param isEmployee
	 * @return
	 */
	private List<CodeResBean> getCodeBranchBankTown( String loanType, String cityCode, Boolean isEmployee )
	{
		List<CodeTown> codeTownList = branchBankDAO.getBranchBankTownByCity( loanType, cityCode, getAllowedUser( isEmployee ) );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeTown codeTown : codeTownList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeTown.getTownCode() );
			codeResBean.setName( codeTown.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得明日起五個營業日(當時間為今日14時前，取今日起五個營業日)
	 *
	 * @return
	 */
	private List<CodeBusinessDay> getCodeBusinessDays()
	{
		boolean isBusinessHours = Calendar.getInstance().get( Calendar.HOUR_OF_DAY ) < 14;

		if( isBusinessHours )
			return codeBusinessDayDAO.get5PojosAfterToday();

		return codeBusinessDayDAO.get5PojosAfterDate( DateUtils.addDays( new Date(), 1 ) );
	}

	/**
	 * 取得房貸e把兆方便聯絡時間
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeCallBackTime()
	{
		List<CodeResBean> codeResBeans = new ArrayList<>();
		CodeResBean codeResBean1 = new CodeResBean();
		codeResBean1.setCode( "01" );
		codeResBean1.setName( "09:00~12:00" );
		codeResBeans.add( codeResBean1 );

		CodeResBean codeResBean2 = new CodeResBean();
		codeResBean2.setCode( "02" );
		codeResBean2.setName( "13:00~17:00" );
		codeResBeans.add( codeResBean2 );

		CodeResBean codeResBean3 = new CodeResBean();
		codeResBean3.setCode( "03" );
		codeResBean3.setName( "17:00過後" );
		codeResBeans.add( codeResBean3 );

		CodeResBean codeResBean4 = new CodeResBean();
		codeResBean4.setCode( "04" );
		codeResBean4.setName( "全天皆可" );
		codeResBeans.add( codeResBean4 );

		return codeResBeans;
	}

	/**
	 * 取得縣市
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeCity()
	{
		List<CodeCity> codeCityList = codeCityDAO.getAllPojosOrderByDisplay();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeCity codeCity : codeCityList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeCity.getCityCode() );
			codeResBean.setName( codeCity.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得信貸方便聯絡時間
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeContactTime()
	{
		List<CodeContactTime> codeContactTimeList = codeContactTimeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeContactTime codeContactTime : codeContactTimeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeContactTime.getContactTimeCode() );
			codeResBean.setName( codeContactTime.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得契約通知方式
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeContractNotification()
	{
		List<CodeContractNotification> codeContractNotificationList = codeContractNotificationDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeContractNotification codeContractNotification : codeContractNotificationList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeContractNotification.getContractNotificationCode() );
			codeResBean.setName( codeContractNotification.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得教育程度
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeEducationLevel()
	{
		List<CodeEducationLevel> codeEducationLevelList = codeEducationLevelDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeEducationLevel codeEducationLevel : codeEducationLevelList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeEducationLevel.getEducationLevelCode() );
			codeResBean.setName( codeEducationLevel.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得寬限期
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeGracePeriod()
	{
		List<CodeGracePeriod> codeGracePeriodList = codeGracePeriodDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeGracePeriod codeGracePeriod : codeGracePeriodList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeGracePeriod.getGracePeriodCode() );
			codeResBean.setName( codeGracePeriod.getName() + "年" );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得本案提徵保證人原因
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeGuarantyReason()
	{
		List<CodeGuarantyReason> codeGuarantyReasonList = codeGuarantyReasonDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeGuarantyReason codeGuarantyReason : codeGuarantyReasonList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeGuarantyReason.getGuarantyReasonCode() );
			codeResBean.setName( codeGuarantyReason.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得不動產狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeHouseStatus()
	{
		List<CodeHouseStatus> codeHouseStatusList = codeHouseStatusDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeHouseStatus codeHouseStatus : codeHouseStatusList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeHouseStatus.getHouseStatusCode() );
			codeResBean.setName( codeHouseStatus.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得職業子類別
	 *
	 * @param jobType
	 * @return
	 */
	private List<CodeResBean> getCodeJobSubType( String jobType )
	{
		List<CodeJobSubType> codeJobSubTypeList = codeJobSubTypeDAO.getPojosByJobType( jobType );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeJobSubType codeJobSubType : codeJobSubTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeJobSubType.getJobSubTypeId().toString() );
			codeResBean.setName( codeJobSubType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;

	}

	/**
	 * 取得職業類別
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeJobType()
	{
		List<CodeJobType> codeJobTypeList = codeJobTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeJobType codeJobType : codeJobTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeJobType.getJobType() );
			codeResBean.setName( codeJobType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得行銷管理頁操作類型
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeLandingAction()
	{
		List<CodeLandingAction> codeLandingActionList = codeLandingActionDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeLandingAction codeLandingAction : codeLandingActionList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeLandingAction.getLandingActionCode() );
			codeResBean.setName( codeLandingAction.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得申請單類型
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeLandingRequestType()
	{
		List<CodeLandingRequestType> codeRequestTypeList = codeLandingRequestTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeLandingRequestType codeRequestType : codeRequestTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeRequestType.getLandingRequestType() );
			codeResBean.setName( codeRequestType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得貸款借款期限
	 *
	 * @param fromIndex
	 * @param toIndex
	 * @return
	 */
	private List<CodeResBean> getCodeLoanPeriod( int fromIndex, int toIndex )
	{
		List<CodeLoanPeriod> codeLoanPeriodList = codeLoanPeriodDAO.getAllPojos();

		if( fromIndex <= 0 )
			fromIndex = 1;

		if( toIndex > codeLoanPeriodList.size() )
			toIndex = codeLoanPeriodList.size();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( int index = fromIndex - 1; index < toIndex; index++ )
		{
			CodeLoanPeriod codeLoanPeriod = codeLoanPeriodList.get( index );

			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeLoanPeriod.getLoadPeriodCode() );
			codeResBean.setName( codeLoanPeriod.getName() + "年" );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得借款用途
	 *
	 * @param loanType
	 * @return
	 */
	private List<CodeResBean> getCodeLoanPurpose( String loanType )
	{
		List<CodeLoanPurpose> codeLoanPurposeList = codeLoanPurposeDAO.getPojosByProperties( loanType );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeLoanPurpose codeLoanPurpose : codeLoanPurposeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeLoanPurpose.getLoanPurposeId().toString() );
			codeResBean.setName( codeLoanPurpose.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得貸款類型
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeLoanType()
	{
		List<CodeLoanType> codeLoanTypeList = codeLoanTypeDAO.getAllPojosOrderByDisplay();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeLoanType codeLoanType : codeLoanTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeLoanType.getLoanType() );
			codeResBean.setName( codeLoanType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得婚姻狀況
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeMarriageStatus()
	{
		List<CodeMarriageStatus> codeMarriageStatusList = codeMarriageStatusDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeMarriageStatus codeMarriageStatus : codeMarriageStatusList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeMarriageStatus.getMarriageStatusCode() );
			codeResBean.setName( codeMarriageStatus.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得本次房地產屬於
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeMortgageType()
	{
		List<CodeMortgageType> codeMortgageTypeList = codeMortgageTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeMortgageType codeMortgageType : codeMortgageTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeMortgageType.getMortgageType() );
			codeResBean.setName( codeMortgageType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得國籍
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeNationality()
	{
		List<CodeNationality> codeNationalityList = codeNationalityDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeNationality codeNationality : codeNationalityList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeNationality.getNationalityCode() );
			codeResBean.setName( "(" + codeNationality.getNationalityCode() + ")" + codeNationality.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得非自用住宅、自住用途
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeNonPrivateUsageSubType()
	{
		List<CodeNonPrivateUsageSubType> codeNonPrivateUsageSubTypeList = codeNonPrivateUsageSubTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeNonPrivateUsageSubType codeNonPrivateUsageSubType : codeNonPrivateUsageSubTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeNonPrivateUsageSubType.getNonPrivateUsageSubType() );
			codeResBean.setName( codeNonPrivateUsageSubType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得非自用住宅用途
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeNonPrivateUsageType()
	{
		List<CodeNonPrivateUsageType> codeNonPrivateUsageTypeList = codeNonPrivateUsageTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeNonPrivateUsageType codeNonPrivateUsageType : codeNonPrivateUsageTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeNonPrivateUsageType.getNonPrivateUsageType() );
			codeResBean.setName( codeNonPrivateUsageType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得貸款利息收據通知方式
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeNotification()
	{

		List<CodeNotification> codeNotificationList = codeNotificationDAO.getAllPojosOrderBydisplayOrder();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeNotification codeNotification : codeNotificationList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeNotification.getNotificationCode() );
			codeResBean.setName( codeNotification.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得通知狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeNotificationStatus()
	{
		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( NotificationStatusEnum notificationStatusEnum : NotificationStatusEnum.values() )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( notificationStatusEnum.getContext() );
			codeResBean.setName( notificationStatusEnum.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得他行認證銀行
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeOtherBank()
	{
		List<CodeOtherBank> codeOtherBankList = codeOtherBankDAO.getPojosByDisable( false );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeOtherBank codeOtherBank : codeOtherBankList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeOtherBank.getOtherBankCode() );
			codeResBean.setName( codeOtherBank.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得eDDA他行認證銀行(取交集)
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeOtherBankEdda()
	{
		List<CodeList> codeOtherBankEddaList = otherBankEddaDAO.getOtherBankEdda();// J-111-0642 新增陽信銀行

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeList codeOtherBankEdda : codeOtherBankEddaList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeOtherBankEdda.getCodeValue() );
			codeResBean.setName( codeOtherBankEdda.getCodeDesc() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得他行的分行代碼(eDDA登入)
	 *
	 * @param bankId
	 * @return
	 */
	private List<CodeResBean> getCodeOtherBankEddaBranchcode( String bankId )
	{
		List<CodeList> codeOtherBankEddaList = otherBankEddaDAO.getOtherBankEddaBranchcode( bankId );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeList codeOtherBankEdda : codeOtherBankEddaList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeOtherBankEdda.getCodeValue().substring( 3 ) );
			codeResBean.setName( codeOtherBankEdda.getCodeDesc() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得自用住宅證明方式
	 *
	 * @return
	 */
	private List<CodeResBean> getCodePrivateUsageType()
	{
		List<CodePrivateUsageType> codePrivateUsageTypeList = codePrivateUsageTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodePrivateUsageType codePrivateUsageType : codePrivateUsageTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codePrivateUsageType.getPrivateUsageType() );
			codeResBean.setName( codePrivateUsageType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得案件處理狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeProcessStatus()
	{
		List<CodeProcess> codeProcessList = codeProcessDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeProcess codeProcess : codeProcessList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeProcess.getProcessCode() );
			codeResBean.setName( codeProcess.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得利息更動通知方式
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeRateAdjustmentNotification()
	{
		List<CodeRateAdjustmentNotification> codeRateAdjustmentNotificationList = codeRateAdjustmentNotificationDAO.getPojosOrderByDisplay();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeRateAdjustmentNotification codeRateAdjustmentNotification : codeRateAdjustmentNotificationList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeRateAdjustmentNotification.getRateAdjustmentNotificaitonCode() );
			codeResBean.setName( codeRateAdjustmentNotification.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得與借款人關係
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeRelationBorrowerType()
	{
		List<CodeRelationBorrowerType> codeRelationBorrowerTypeList = codeRelationBorrowerTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeRelationBorrowerType codeRelationBorrowerType : codeRelationBorrowerTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeRelationBorrowerType.getRelationBorrowerType() );
			codeResBean.setName( codeRelationBorrowerType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得與申貸人關係類型
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeRelationType()
	{
		List<CodeRelationType> codeRelationTypeList = codeRelationTypeDAO.getAllPojosOrderByDisplay();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeRelationType codeRelationType : codeRelationTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeRelationType.getRelationType() );
			codeResBean.setName( codeRelationType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得負責人類別
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeRepresentativeType()
	{
		List<CodeRepresentativeType> codeRepresentativeTypeList = codeRepresentativeTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeRepresentativeType codeRepresentativeType : codeRepresentativeTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeRepresentativeType.getRepresentativeType() );
			codeResBean.setName( codeRepresentativeType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得現住房屋持有狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeResidenceStatus()
	{
		List<CodeResidenceStatus> codeResidenceStatusList = codeResidenceStatusDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeResidenceStatus codeResidenceStatus : codeResidenceStatusList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeResidenceStatus.getResidenceStatusCode() );
			codeResBean.setName( codeResidenceStatus.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得行銷單位
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeServiceAssociateDept()
	{
		List<CodeServiceAssociateDept> codeServiceAssociateDeptList = codeServiceAssociateDeptDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeServiceAssociateDept codeServiceAssociateDept : codeServiceAssociateDeptList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeServiceAssociateDept.getServiceAssociateDeptCode() );
			codeResBean.setName( codeServiceAssociateDept.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得性別
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeSex()
	{
		List<CodeSex> codeSexList = codeSexDAO.getAllPojosOrderByDesc();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeSex codeSex : codeSexList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeSex.getSexCode() );
			codeResBean.setName( codeSex.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得職稱
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeTitleType()
	{
		List<CodeTitleType> codeTitleTypeList = codeTitleTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeTitleType codeTitleType : codeTitleTypeList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeTitleType.getTitleType() );
			codeResBean.setName( codeTitleType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得區域
	 *
	 * @param cityCode
	 * @return
	 */
	private List<CodeResBean> getCodeTown( String cityCode )
	{
		List<CodeTown> codeTownList = codeTownDAO.getPojosByCityCode( cityCode );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeTown codeTown : codeTownList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeTown.getTownCode() );
			codeResBean.setName( codeTown.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得進件狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeTransmissionStatus()
	{
		List<CodeTransmissionStatus> codeTransmissionStatusList = codeTransmissionStatusDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeTransmissionStatus codeTransmissionStatus : codeTransmissionStatusList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeTransmissionStatus.getTransmissionStatusCode() );
			codeResBean.setName( codeTransmissionStatus.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得上傳狀態
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeUploadStatus()
	{
		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( UploadStatusEnum uploadStatusEnum : UploadStatusEnum.values() )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( uploadStatusEnum.getContext() );
			codeResBean.setName( uploadStatusEnum.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得身分子類別
	 *
	 * @param loanType
	 * @param userType
	 * @return
	 */
	private List<CodeResBean> getCodeUserSubType( String loanType, String userType )
	{
		List<CodeUserSubType> codeUserSubTypeList = codeUserSubTypeDAO.getPojosOrderByDisplay( userType );

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeUserSubType codeUserSubType : codeUserSubTypeList )
		{
			if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType )
				&& UserSubTypeEnum.C_GUARANTOR.getContext().equals( codeUserSubType.getUserSubType() ) )
				continue;

			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeUserSubType.getUserSubType() );
			codeResBean.setName( codeUserSubType.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得身分別
	 *
	 * @param loanType
	 * @return
	 */
	private List<CodeResBean> getCodeUserType( String loanType )
	{
		List<CodeUserType> codeUserTypeList = codeUserTypeDAO.getAllPojos();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeUserType codeUserType : codeUserTypeList )
		{
			if( LoanTypeEnum.PERSONAL_LOAN.getContext().equals( loanType )
				&& UserTypeEnum.PROVIDER.getContext().equals( codeUserType.getUserType() ) )
				continue;

			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeUserType.getUserType() );
			codeResBean.setName( codeUserType.getName() );

			// H-111-0103 依不同種類貸款，在 common/open/code/getCodeList 調整抓 dbo.code_user_type 不同欄位
			if( LoanTypeEnum.HOUSE_LOAN.getContext().equals( loanType ) && StringUtils.isNotBlank( codeUserType.getHlDesc() ) )
				codeResBean.setName( StringUtils.trim( codeUserType.getHlDesc() ) );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得得知本行信貸服務的管道
	 *
	 * @return
	 */
	private List<CodeResBean> getCodeCaseSource()
	{
		List<CodeCaseSource> codeCaseSourceList = codeCaseSourceDAO.getAllPojosOrderBydisplayOrder();

		List<CodeResBean> codeResBeans = new ArrayList<>();

		for( CodeCaseSource codeNotification : codeCaseSourceList )
		{
			CodeResBean codeResBean = new CodeResBean();
			codeResBean.setCode( codeNotification.getCaseSourceCode() );
			codeResBean.setName( codeNotification.getName() );

			codeResBeans.add( codeResBean );
		}

		return codeResBeans;
	}

	/**
	 * 取得房貸e把兆貸款用途
	 *
	 * @return
	 */
	private List<CodeResBean> getHouseContactLoanPurpose()
	{
		List<CodeResBean> codeResBeans = new ArrayList<>();
		CodeResBean codeResBean1 = new CodeResBean();
		codeResBean1.setCode( "01" );
		codeResBean1.setName( "我要購買房屋" );
		codeResBeans.add( codeResBean1 );

		CodeResBean codeResBean2 = new CodeResBean();
		codeResBean2.setCode( "02" );
		codeResBean2.setName( "我要投資周轉" );
		codeResBeans.add( codeResBean2 );

		CodeResBean codeResBean3 = new CodeResBean();
		codeResBean3.setCode( "03" );
		codeResBean3.setName( "我要以房養老" );
		codeResBeans.add( codeResBean3 );

		return codeResBeans;
	}
}
