package com.megabank.olp.client.sender.mydata.userdata;

import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import com.megabank.olp.base.enums.ErrorEnum;
import com.megabank.olp.client.sender.mydata.BaseMyDataClient;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileArgBean;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileReqBean;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileResBean;
import com.megabank.olp.client.sender.mydata.userdata.bean.MyDataFileResultBean;
import com.megabank.olp.client.utility.enums.ClientMyDataErrorEnum;

@Component
public class MyDataFileClient extends BaseMyDataClient<MyDataFileArgBean, MyDataFileReqBean, MyDataFileResBean, MyDataFileResultBean>
{
	@Override
	protected ErrorEnum getMyDataErrorEnum()
	{
		return ClientMyDataErrorEnum.GETTED_MY_DATA_COMMON;
	}

	@Override
	protected String[] getNotCaseErrorReturnCode()
	{
		return new String[]{ "0000" };
	}

	@Override
	protected MyDataFileResultBean getResultBean( MyDataFileResBean resBean )
	{
		MyDataFileResultBean resultBean = new MyDataFileResultBean();
		resultBean.setErrorCode( resBean.getReturnCode() );
		resultBean.setWaitSec( resBean.getWaitSec() );
		resultBean.setMyDataFileBeans( resBean.getMyDataFileBeans() );

		return resultBean;
	}

	@Override
	protected String getSimulatorCode( MyDataFileArgBean argBean )
	{
		return "0000";
	}

	@Override
	protected String getSuffixUrl()
	{
		return "/getUserData";
	}

	@Override
	protected MyDataFileReqBean transArg2Req( MyDataFileArgBean argBean )
	{
		MyDataFileReqBean reqBean = new MyDataFileReqBean();
		reqBean.setServiceId( argBean.getServiceId() );
		reqBean.setTxId( argBean.getTxId() );

		return reqBean;
	}

	@Override
	protected MyDataFileResultBean transRes2Result( MyDataFileResBean resBean, HttpHeaders httpHeaders )
	{
		return getResultBean( resBean );
	}

}
