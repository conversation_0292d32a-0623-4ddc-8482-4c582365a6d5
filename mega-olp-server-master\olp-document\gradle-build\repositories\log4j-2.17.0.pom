<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements. See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache license, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License. You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the license for the specific language governing permissions and
  ~ limitations under the license.
  --><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.logging.log4j</groupId>
  <artifactId>log4j</artifactId>
  <packaging>pom</packaging>
  <name>Apache Log4j 2</name>
  <version>2.17.0</version>
  <parent>
    <groupId>org.apache.logging</groupId>
    <artifactId>logging-parent</artifactId>
    <version>3</version>
    <relativePath />
  </parent>
  <description>Apache Log4j 2</description>
  <url>https://logging.apache.org/log4j/2.x/</url>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/LOG4J2</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-builds.apache.org/job/Logging/job/log4j/</url>
  </ciManagement>
  <inceptionYear>1999</inceptionYear>
  <developers>
    <developer>
      <id>rgoers</id>
      <name>Ralph Goers</name>
      <email><EMAIL></email>
      <organization>Nextiva</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/Phoenix</timezone>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email><EMAIL></email>
      <organization>Rocket Software</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/Denver</timezone>
    </developer>
    <developer>
      <id>sdeboy</id>
      <name>Scott Deboy</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/Los_Angeles</timezone>
    </developer>
    <developer>
      <id>rpopma</id>
      <name>Remko Popma</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>Asia/Tokyo</timezone>
      <properties>
        <picUrl>http://people.apache.org/~rpopma/img/profilepic.jpg</picUrl>
      </properties>
    </developer>
    <developer>
      <id>nickwilliams</id>
      <name>Nick Williams</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/Chicago</timezone>
    </developer>
    <developer>
      <id>mattsicker</id>
      <name>Matt Sicker</name>
      <email><EMAIL></email>
      <organization>CloudBees</organization>
      <roles>
        <role>PMC Chair</role>
      </roles>
      <timezone>America/Chicago</timezone>
    </developer>
    <developer>
      <id>bbrouwer</id>
      <name>Bruce Brouwer</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>America/Detroit</timezone>
    </developer>
    <developer>
      <id>mikes</id>
      <name>Mikael Ståldal</name>
      <email><EMAIL></email>
      <organization>Spotify</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>Europe/Stockholm</timezone>
    </developer>
    <developer>
      <id>ckozak</id>
      <name>Carter Kozak</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New York</timezone>
    </developer>
    <developer>
      <id>vy</id>
      <name>Volkan Yazıcı</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>Europe/Amsterdam</timezone>
    </developer>
  </developers>
  <!-- Contributors -->
  <contributors>
      <contributor>
        <name>Murad Ersoy</name>
        <email><EMAIL></email>
        <url>https://www.behance.net/muradersoy</url>
        <roles>
          <role>Illustrator and Designer</role>
          <role>created the new Log4j 2 logo.</role>
        </roles>
        <timezone>Europe/Istanbul</timezone>
        <properties>
          <picUrl>https://mir-s3-cdn-cf.behance.net/user/138/403dcf1521581.54d67f8fb01f7.jpg</picUrl>
        </properties>
      </contributor>
   </contributors>
  <mailingLists>
    <mailingList>
      <name>log4j-user</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
      <otherArchives>
        <otherArchive>http://mail-archives.apache.org/mod_mbox/logging-log4j-user/</otherArchive>
        <otherArchive>http://marc.info/?l=log4j-user</otherArchive>
        <otherArchive>http://dir.gmane.org/gmane.comp.jakarta.log4j.user</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>dev</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
      <otherArchives>
        <otherArchive>http://mail-archives.apache.org/mod_mbox/logging-dev/</otherArchive>
        <otherArchive>http://marc.info/?l=dev</otherArchive>
        <otherArchive>http://dir.gmane.org/gmane.comp.jakarta.log4j.devel</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/logging-log4j2.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/logging-log4j2.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=logging-log4j2.git</url>
    <tag>log4j-2.17.0-rc1</tag>
  </scm>
  <properties>
    <!-- make sure to update these for each release! -->
    <log4jParentDir>${basedir}</log4jParentDir>
    <Log4jReleaseVersion>2.17.0</Log4jReleaseVersion>
    <Log4jReleaseVersionJava7>2.12.2</Log4jReleaseVersionJava7>
    <Log4jReleaseVersionJava6>2.3</Log4jReleaseVersionJava6>
    <!--Log4jReleaseManager>Ralph Goers</Log4jReleaseManager-->
    <!--Log4jReleaseKey>B3D8E1BA</Log4jReleaseKey-->
    <Log4jReleaseManager>Ralph Goers</Log4jReleaseManager>
    <Log4jReleaseKey>B3D8E1BA</Log4jReleaseKey>
    <!-- note that any properties you want available in velocity templates must not use periods! -->
    <slf4jVersion>1.7.25</slf4jVersion>
    <logbackVersion>1.2.3</logbackVersion>
    <jackson1Version>1.9.13</jackson1Version>
    <jackson2Version>2.12.4</jackson2Version>
    <spring-boot.version>2.5.7</spring-boot.version>
    <springVersion>5.3.13</springVersion>
    <kubernetes-client.version>4.6.1</kubernetes-client.version>
    <flumeVersion>1.9.0</flumeVersion>
    <disruptorVersion>3.4.4</disruptorVersion>
    <conversantDisruptorVersion>1.2.15</conversantDisruptorVersion> <!-- Version 1.2.16 requires Java 9 -->
    <elastic.version>7.6.2</elastic.version>
    <mongodb3.version>3.12.7</mongodb3.version>
    <mongodb4.version>4.2.2</mongodb4.version>
    <!-- POM for jackson-dataformat-xml 2.12.4 depends on woodstox-core 6.2.4 -->
    <woodstox.version>6.2.6</woodstox.version>
    <groovy.version>3.0.8</groovy.version>
    <compiler.plugin.version>3.8.1</compiler.plugin.version>
    <pmd.plugin.version>3.10.0</pmd.plugin.version>
    <changes.plugin.version>2.12.1</changes.plugin.version>
    <javadoc.plugin.version>3.3.1</javadoc.plugin.version>
    <!-- surefire.plugin.version 2.18 yields http://jira.codehaus.org/browse/SUREFIRE-1121, which is fixed in 2.18.1 -->
    <!-- surefire.plugin.version 2.19 yields https://issues.apache.org/jira/browse/SUREFIRE-1193. -->
    <!-- all versions after 2.13 yield https://issues.apache.org/jira/browse/SUREFIRE-720 -->
    <surefire.plugin.version>3.0.0-M5</surefire.plugin.version>
    <failsafe.plugin.version>3.0.0-M5</failsafe.plugin.version>
    <checkstyle.plugin.version>3.0.0</checkstyle.plugin.version>
    <deploy.plugin.version>2.8.2</deploy.plugin.version>
    <rat.plugin.version>0.13</rat.plugin.version>
    <pdf.plugin.version>1.2</pdf.plugin.version>
    <cobertura.plugin.version>2.7</cobertura.plugin.version>
    <jacoco.plugin.version>0.8.6</jacoco.plugin.version>
    <release.plugin.version>2.5.3</release.plugin.version>
    <scm.plugin.version>1.9.5</scm.plugin.version>
    <jxr.plugin.version>2.5</jxr.plugin.version>
    <revapi.plugin.version>0.11.1</revapi.plugin.version>
    <revapi.skip>false</revapi.skip>
    <clirr.plugin.version>2.8</clirr.plugin.version>
    <site.plugin.version>3.8.2</site.plugin.version>
    <!-- Maven site depends on Velocity and the escaping rules are different in newer versions. -->
    <!-- See https://maven.apache.org/plugins/maven-site-plugin/migrate.html -->
    <velocity.plugin.version>1.5</velocity.plugin.version>
    <asciidoc.plugin.version>1.5.6</asciidoc.plugin.version>
    <remote.resources.plugin.version>1.5</remote.resources.plugin.version>
    <manifestfile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestfile>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <maven.doap.skip>false</maven.doap.skip>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <docLabel>Site Documentation</docLabel>
    <projectDir />
    <commonsLoggingVersion>1.2</commonsLoggingVersion>
    <javax.persistence>2.2.1</javax.persistence>
    <!-- The OSGi API version MUST always be the MINIMUM version Log4j supports -->
    <osgi.api.version>4.3.1</osgi.api.version>
    <activemq.version>5.16.3</activemq.version>
    <!-- Allow Clirr severity to be overriden by the command-line option -DminSeverity=level -->
    <minSeverity>info</minSeverity>
    <jctoolsVersion>1.2.1</jctoolsVersion>
    <junitVersion>4.13.2</junitVersion>
    <junitJupiterVersion>5.7.2</junitJupiterVersion>
    <mockitoVersion>3.11.2</mockitoVersion>
    <xmlunitVersion>2.8.3</xmlunitVersion>
    <argLine>-Xms256m -Xmx1024m</argLine>
    <javaTargetVersion>1.8</javaTargetVersion>
    <module.name />
  </properties>
  <pluginRepositories>
    <pluginRepository>
      <id>apache</id>
      <url>https://repository.apache.org/content/repositories/releases/</url>
    </pluginRepository>
<!--     <pluginRepository> -->
<!--       <id>apache.snapshots</id> -->
<!--       <name>Apache snapshots repository</name> -->
<!--       <url>http://repository.apache.org/content/groups/snapshots</url> -->
<!--       <snapshots> -->
<!--         <enabled>true</enabled> -->
<!--       </snapshots> -->
<!--     </pluginRepository>     -->
  </pluginRepositories>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>
       <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>${slf4jVersion}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logbackVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <type>test-jar</type>
        <version>${logbackVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.tycho</groupId>
        <artifactId>org.eclipse.osgi</artifactId>
        <version>3.13.0.v20180226-1711</version>
      </dependency>
      <dependency>
        <groupId>org.apache.felix</groupId>
        <artifactId>org.apache.felix.framework</artifactId>
        <version>5.6.12</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>3.6.3</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.11.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>2.9.0</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logbackVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logbackVersion}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api-java9</artifactId>
        <version>${project.version}</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core-java9</artifactId>
        <version>${project.version}</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-layout-template-json</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${project.version}</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j18-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commonsLoggingVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-flume-ng</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-iostreams</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jul</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-taglib</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jakarta-web</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sleepycat</groupId>
        <artifactId>je</artifactId>
        <version>5.0.73</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.core</artifactId>
        <version>${osgi.api.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.fusesource.jansi</groupId>
        <artifactId>jansi</artifactId>
        <version>2.3.4</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-sdk</artifactId>
        <version>${flumeVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-core</artifactId>
        <version>${flumeVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-embedded-agent</artifactId>
        <version>${flumeVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-node</artifactId>
        <version>${flumeVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume.flume-ng-channels</groupId>
        <artifactId>flume-file-channel</artifactId>
        <version>${flumeVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api-2.5</artifactId>
          </exclusion>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-core</artifactId>
        <version>1.2.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!-- Jackson 1 start -->
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-core-asl</artifactId>
        <version>${jackson1Version}</version>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-mapper-asl</artifactId>
        <version>${jackson1Version}</version>
        <scope>runtime</scope>
      </dependency>
      <!-- Jackson 1 end -->
      <!-- Jackson 2 start -->
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jaxb-annotations</artifactId>
        <version>${jackson2Version}</version>
        <optional>true</optional>
      </dependency>
      <!-- Jackson 2 end -->
      <dependency>
        <groupId>com.sun.mail</groupId>
        <artifactId>javax.mail</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.spec.javax.jms</groupId>
        <artifactId>jboss-jms-api_1.1_spec</artifactId>
        <version>1.0.1.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.zeromq</groupId>
        <artifactId>jeromq</artifactId>
        <version>0.4.3</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>2.5</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>${disruptorVersion}</version>
      </dependency>
      <dependency>
        <groupId>com.conversantmedia</groupId>
        <artifactId>disruptor</artifactId>
        <version>${conversantDisruptorVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.jctools</groupId>
        <artifactId>jctools-core</artifactId>
        <version>${jctoolsVersion}</version>
      </dependency>
      <!-- JUnit 5 engine -->
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>${junitJupiterVersion}</version>
        <scope>test</scope>
      </dependency>
      <!-- JUnit 4 to 5 migration support -->
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>${junitJupiterVersion}</version>
        <scope>test</scope>
      </dependency>
      <!-- JUnit 5 parameterized test support -->
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>${junitJupiterVersion}</version>
        <scope>test</scope>
      </dependency>
      <!-- JUnit 4 API dependency -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junitVersion}</version>
        <scope>test</scope>
      </dependency>
      <!-- JUnit 4 engine -->
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>${junitJupiterVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.20.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>4.0.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.4.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockitoVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>${mockitoVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aop</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-beans</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-expression</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-oxm</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>${springVersion}</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client</artifactId>
        <version>${kubernetes-client.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>2.5.2</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>1.4.200</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.persistence</groupId>
        <artifactId>org.eclipse.persistence.jpa</artifactId>
        <version>2.7.9</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.persistence</groupId>
        <artifactId>javax.persistence</artifactId>
        <version>${javax.persistence}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.lightcouch</groupId>
        <artifactId>lightcouch</artifactId>
        <version>0.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.cassandra</groupId>
        <artifactId>cassandra-driver-core</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <!-- 3.6.0 to 4.3.2 break binary compatibility. -->
        <version>3.5.5</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.json-unit</groupId>
        <artifactId>json-unit</artifactId>
        <version>2.27.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>${xmlunitVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>${xmlunitVersion}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.11.0</version>
        <scope>test</scope>
      </dependency>
      <!-- Used for testing JsonTemplateLayout -->
      <dependency>
        <groupId>co.elastic.logging</groupId>
        <artifactId>log4j2-ecs-layout</artifactId>
        <version>1.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>${elastic.version}</version>
      </dependency>
      <!-- Used for testing HttpAppender -->
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock</artifactId>
        <scope>test</scope>
        <!--  2.27.2 causes WatchHttpTest to fail. -->
        <version>2.26.3</version>
      </dependency>
      <!-- Used for compressing to formats other than zip and gz -->
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>1.21</version>
      </dependency>
      <dependency>
        <groupId>org.tukaani</groupId>
        <artifactId>xz</artifactId>
        <version>1.9</version>
        <scope>test</scope>
      </dependency>
      <!-- Used for the CSV layout -->
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-csv</artifactId>
        <version>1.9.0</version>
      </dependency>
      <!-- GC-free -->
      <dependency>
        <groupId>com.google.code.java-allocation-instrumenter</groupId>
        <artifactId>java-allocation-instrumenter</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.hdrhistogram</groupId>
        <artifactId>HdrHistogram</artifactId>
        <version>2.1.9</version>
      </dependency>
      <dependency>
        <groupId>org.apache-extras.beanshell</groupId>
        <artifactId>bsh</artifactId>
        <version>2.0b6</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jsr223</artifactId>
        <version>${groovy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-dateutil</artifactId>
        <version>${groovy.version}</version>
      </dependency>
      <dependency>
        <!-- Testing MongoDB -->
        <groupId>de.flapdoodle.embed</groupId>
        <artifactId>de.flapdoodle.embed.mongo</artifactId>
        <version>3.0.0</version>
        <scope>test</scope>
      </dependency>
      <!-- Testing LDAP -->
      <dependency>
        <groupId>org.zapodot</groupId>
        <artifactId>embedded-ldap-junit</artifactId>
        <version>0.8.1</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.5.0</version>
          <inherited>true</inherited>
          <extensions>true</extensions>
          <executions>
            <execution>
              <goals>
                <goal>manifest</goal>
              </goals>
              <phase>process-classes</phase>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-changes-plugin</artifactId>
          <version>${changes.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${release.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-plugin</artifactId>
          <version>${scm.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${checkstyle.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${javadoc.plugin.version}</version>
          <configuration>
            <bottom><![CDATA[<p align="center">Copyright &#169; {inceptionYear}-{currentYear} {organizationName}. All Rights Reserved.<br />
            Apache Logging, Apache Log4j, Log4j, Apache, the Apache feather logo, the Apache Logging project logo,
            and the Apache Log4j logo are trademarks of The Apache Software Foundation.</p>]]></bottom>
            <doclint>none</doclint>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${pmd.plugin.version}</version>
        </plugin>
        <!-- some nice default compiler options -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${compiler.plugin.version}</version>
          <configuration>
            <source>${maven.compiler.source}</source>
            <target>${maven.compiler.target}</target>
            <parameters>true</parameters>
            <showDeprecation>true</showDeprecation>
            <showWarnings>true</showWarnings>
            <encoding>UTF-8</encoding>
            <fork>true</fork>
            <meminitial>256</meminitial>
            <maxmem>1024</maxmem>
            <compilerArguments>
              <Xmaxwarns>10000</Xmaxwarns>
              <Xlint />
            </compilerArguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${surefire.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${failsafe.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>4.0.4</version>
          <configuration>
            <plugins>
              <plugin>
                <groupId>com.h3xstream.findsecbugs</groupId>
                <artifactId>findsecbugs-plugin</artifactId>
                <version>1.10.1</version>
              </plugin>
            </plugins>
            <excludeFilterFile>${log4jParentDir}/findbugs-exclude-filter.xml</excludeFilterFile>
            <fork>true</fork>
            <effort>Default</effort>
            <threshold>Normal</threshold>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.0.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <phase>verify</phase>
              <goals>
                <goal>jar-no-fork</goal>
                <goal>test-jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${jxr.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.eluder.coveralls</groupId>
          <artifactId>coveralls-maven-plugin</artifactId>
          <version>4.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${jacoco.plugin.version}</version>
          <executions>
            <execution>
                <id>prepare-agent</id>
                <goals>
                  <goal>prepare-agent</goal>
                </goals>
            </execution>
            <execution>
              <id>default-report</id>
              <phase>prepare-package</phase>
              <goals>
                <goal>report</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.1.0</version>
          <executions>
            <execution>
              <id>default-jar</id>
              <goals>
                <goal>jar</goal>
              </goals>
              <configuration>
                <archive>
                  <manifestFile>${manifestfile}</manifestFile>
                  <manifestEntries>
                    <Specification-Title>${project.name}</Specification-Title>
                    <Specification-Version>${project.version}</Specification-Version>
                    <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                    <Implementation-Title>${project.name}</Implementation-Title>
                    <Implementation-Version>${project.version}</Implementation-Version>
                    <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                    <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
                    <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                    <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                    <Automatic-Module-Name>${module.name}</Automatic-Module-Name>
                  </manifestEntries>
                </archive>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>io.fabric8</groupId>
          <artifactId>docker-maven-plugin</artifactId>
          <version>0.33.0</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.1.0</version>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.0.2</version>
        <executions>
          <execution>
            <id>copy-sitecss</id>
            <!-- fetch site.xml before creating site documentation -->
            <phase>pre-site</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/site</outputDirectory>
              <resources>
                <resource>
                  <directory>${log4jParentDir}/src/site/resources</directory>
                  <includes>
                    <include>**/*</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire.plugin.version}</version>
        <configuration>
          <systemPropertyVariables>
            <java.awt.headless>true</java.awt.headless>
          </systemPropertyVariables>
          <forkCount>1</forkCount>
          <reuseForks>false</reuseForks>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${failsafe.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <systemPropertyVariables>
            <java.awt.headless>true</java.awt.headless>
          </systemPropertyVariables>
          <argLine>-Xms256m -Xmx1024m</argLine>
          <forkCount>1</forkCount>
          <reuseForks>false</reuseForks>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.12</version>
        <executions>
          <execution>
            <id>timestamp-property</id>
            <goals>
              <goal>timestamp-property</goal>
            </goals>
            <phase>pre-site</phase>
            <configuration>
              <name>currentYear</name>
              <pattern>yyyy</pattern>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>${site.plugin.version}</version>
        <dependencies>
          <dependency>
            <groupId>org.asciidoctor</groupId>
            <artifactId>asciidoctor-maven-plugin</artifactId>
            <version>${asciidoc.plugin.version}</version>
          </dependency>
          <dependency>
            <groupId>org.asciidoctor</groupId>
            <artifactId>asciidoctor-maven-plugin</artifactId>
            <version>${asciidoc.plugin.version}</version>
          </dependency>
        </dependencies>
        <configuration>
          <!-- only build English site even on other language OS -->
          <locales>en</locales>
          <!-- Exclude the navigation file for Maven 1 sites
               and the changes file used by the changes-plugin,
               as they interfere with the site generation. -->
          <moduleExcludes>
            <xdoc>navigation.xml,changes.xml</xdoc>
          </moduleExcludes>
          <asciidoc>
            <attributes>
              <!-- copy any site properties wanted in asciidoc files -->
              <Log4jReleaseVersion>${Log4jReleaseVersion}</Log4jReleaseVersion>
              <Log4jReleaseManager>${Log4jReleaseManager}</Log4jReleaseManager>
              <Log4jReleaseKey>${Log4jReleaseKey}</Log4jReleaseKey>
            </attributes>
          </asciidoc>
        </configuration>
      </plugin>
      <!-- <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>clean</id>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin> -->
      <!-- We need to disable the standard ASF configuration to be able to publish our own notice and license files -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <skip>true</skip>
              <resourceBundles />
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pdf-plugin</artifactId>
        <version>${pdf.plugin.version}</version>
        <executions>
          <execution>
            <id>pdf</id>
            <phase>site</phase>
            <goals>
              <goal>pdf</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.reporting.outputDirectory}</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <!-- RAT report -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${rat.plugin.version}</version>
        <configuration>
          <excludes>
            <!-- Matches other RAT configurations in this POM -->
            <exclude>src/main/resources/META-INF/services/**/*</exclude>
            <!-- IntelliJ files -->
            <exclude>.idea/**/*</exclude>
            <exclude>src/test/resources/**/*</exclude>
            <!-- IDE settings imports -->
            <exclude>src/ide/**</exclude>
            <!-- does it even make sense to apply a license to a GPG signature? -->
            <exclude>**/*.asc</exclude>
            <!-- jQuery is MIT-licensed, but RAT can't figure it out -->
            <exclude>src/site/resources/js/jquery.js</exclude>
            <exclude>src/site/resources/js/jquery.min.js</exclude>
            <!-- Generated files -->
            <exclude>log4j-distribution/target/**/*</exclude>
            <exclude>log4j-distribution/.project</exclude>
            <exclude>log4j-distribution/.settings/**</exclude>
            <exclude>velocity.log</exclude>
            <!-- Other -->
            <exclude>felix-cache/**</exclude>
            <exclude>**/README.md</exclude>
            <exclude>RELEASE-NOTES.md</exclude>
            <exclude>SECURITY.md</exclude>
            <exclude>.java-version</exclude>
            <exclude>**/*.yml</exclude>
            <exclude>**/*.yaml</exclude>
            <exclude>**/*.json</exclude>
            <excllude>**/images/*.drawio</excllude>
            <exclude>**/fluent-bit.conf</exclude>
            <exclude>**/rabbitmq.config</exclude>
            <exclude>**/MANIFEST.MF</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!-- DOAP (RDF) metadata generation -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-doap-plugin</artifactId>
        <version>1.2</version>
        <dependencies>
          <!-- Fix java.lang.IllegalArgumentException: Invalid version number: Version number may be negative or greater than 255 -->
          <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
            <version>4.6.1</version>
          </dependency>
        </dependencies>
        <configuration>
          <doapOptions>
            <programmingLanguage>Java</programmingLanguage>
            <category>library</category>
          </doapOptions>
          <asfExtOptions>
            <charter>
              The Apache Logging Services Project creates and maintains open-source software related to the logging of
              application behavior and released at no charge to the public.
            </charter>
            <pmc>https://logging.apache.org</pmc>
          </asfExtOptions>
          <skip>${maven.doap.skip}</skip>
        </configuration>
        <executions>
          <execution>
            <id>site</id>
            <phase>site</phase>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <!-- Changes report -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${changes.plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <statusIds>Resolved, Closed</statusIds>
          <columnNames>Type,Key,Summary,Assignee,Status,Resolution,Fix Version</columnNames>
          <useJql>true</useJql>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.9</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependencies</report>
              <report>dependency-info</report>
              <report>dependency-convergence</report>
              <report>dependency-management</report>
              <report>project-team</report>
              <report>mailing-list</report>
              <report>issue-tracking</report>
              <report>license</report>
              <report>scm</report>
              <report>summary</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <!-- you'd think these would be the defaults, right? -->
          <customBundle>${project.basedir}/src/site/custom/project-info-report.properties</customBundle>
          <webAccessUrl>${project.scm.url}</webAccessUrl>
          <anonymousConnection>${project.scm.connection}</anonymousConnection>
          <developerConnection>${project.scm.developerConnection}</developerConnection>
          <scmTag>log4j-${Log4jReleaseVersion}</scmTag>
        </configuration>
      </plugin>
      <!-- Surefire report -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${surefire.plugin.version}</version>
        <reportSets>
          <reportSet>
            <id>integration-tests</id>
            <reports>
              <report>failsafe-report-only</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <!-- RAT report -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${rat.plugin.version}</version>
        <configuration>
          <excludes>
            <!-- Matches other RAT configurations in this POM -->
            <exclude>src/main/resources/META-INF/services/**/*</exclude>
            <!-- IntelliJ files -->
            <exclude>.idea/**/*</exclude>
            <exclude>src/test/resources/**/*</exclude>
            <!-- IDE settings imports -->
            <exclude>src/ide/**</exclude>
            <!-- does it even make sense to apply a license to a GPG signature? -->
            <exclude>**/*.asc</exclude>
            <!-- jQuery is MIT-licensed, but RAT can't figure it out -->
            <exclude>src/site/resources/js/jquery.js</exclude>
            <exclude>src/site/resources/js/jquery.min.js</exclude>
            <!-- Generated files -->
            <exclude>log4j-distribution/target/**/*</exclude>
            <exclude>log4j-distribution/.project</exclude>
            <exclude>log4j-distribution/.settings/**</exclude>
            <exclude>velocity.log</exclude>
            <!-- Other -->
            <exclude>felix-cache/**</exclude>
            <exclude>**/README.md</exclude>
            <exclude>SECURITY.md</exclude>
            <exclude>RELEASE-NOTES.md</exclude>
            <exclude>**/*.yml</exclude>
            <exclude>**/*.yaml</exclude>
            <exclude>**/*.json</exclude>
            <excllude>**/images/*.drawio</excllude>
            <exclude>**/fluent-bit.conf</exclude>
            <exclude>**/rabbitmq.config</exclude>
            <exclude>**/MANIFEST.MF</exclude>
            <exclude>.java-version</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
  <distributionManagement>
    <downloadUrl>https://logging.apache.org/log4j/2.x/download.html</downloadUrl>
    <!-- site is only included to make maven-site-plugin stop complaining -->
    <site>
      <id>www.example.com</id>
      <url>scp://www.example.com/www/docs/project/</url>
    </site>
  </distributionManagement>
  <modules>
    <module>log4j-api-java9</module>
    <module>log4j-api</module>
    <module>log4j-core-java9</module>
    <module>log4j-core</module>
    <module>log4j-layout-template-json</module>
    <module>log4j-core-its</module>
    <module>log4j-1.2-api</module>
    <module>log4j-slf4j-impl</module>
    <module>log4j-slf4j18-impl</module>
    <module>log4j-to-slf4j</module>
    <module>log4j-jcl</module>
    <module>log4j-flume-ng</module>
    <module>log4j-taglib</module>
    <module>log4j-jmx-gui</module>
    <module>log4j-samples</module>
    <module>log4j-bom</module>
    <module>log4j-jdbc-dbcp2</module>
    <module>log4j-jpa</module>
    <module>log4j-couchdb</module>
    <module>log4j-mongodb3</module>
    <module>log4j-mongodb4</module>
    <module>log4j-cassandra</module>
    <module>log4j-web</module>
    <module>log4j-jakarta-web</module>
    <module>log4j-perf</module>
    <module>log4j-iostreams</module>
    <module>log4j-jul</module>
    <module>log4j-jpl</module>
    <module>log4j-liquibase</module>
    <module>log4j-appserver</module>
    <module>log4j-osgi</module>
    <module>log4j-docker</module>
    <module>log4j-kubernetes</module>
    <module>log4j-spring-boot</module>
    <module>log4j-spring-cloud-config</module>
  </modules>
  <profiles>
    <profile>
      <id>pdf</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pdf-plugin</artifactId>
            <version>${pdf.plugin.version}</version>
            <executions>
              <execution>
                <id>pdf</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>pdf</goal>
                </goals>
                <configuration>
                  <outputDirectory>${project.reporting.outputDirectory}</outputDirectory>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>release-notes</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-changes-plugin</artifactId>
            <version>${changes.plugin.version}</version>
            <configuration>
              <template>announcement.vm</template>
              <templateDirectory>src/changes</templateDirectory>
              <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
              <announcementDirectory>.</announcementDirectory>
              <announcementFile>RELEASE-NOTES.md</announcementFile>
              <issueManagementSystems>
                <issueManagementSystem>changes.xml</issueManagementSystem>
                <!--<issueManagementSystem>JIRA</issueManagementSystem> -->
              </issueManagementSystems>
              <version>${Log4jReleaseVersion}</version>
              <announceParameters>
                <releaseVersion>${Log4jReleaseVersion}</releaseVersion>
                <releaseCount>${Log4jReleaseCount}</releaseCount>
              </announceParameters>
              <useJql>true</useJql>
            </configuration>
            <executions>
              <execution>
                <id>create-release-notes</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>announcement-generate</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <configuration>
                  <skipAssembly>true</skipAssembly>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <modules>
        <module>log4j-distribution</module>
      </modules>
    </profile>
    <profile>
      <id>rat</id>
      <build>
        <plugins>
          <!-- RAT report -->
          <plugin>
            <groupId>org.apache.rat</groupId>
            <artifactId>apache-rat-plugin</artifactId>
            <version>${rat.plugin.version}</version>
            <configuration>
              <excludes>
                <!-- Matches other RAT configurations in this POM -->
                <exclude>src/main/resources/META-INF/services/**/*</exclude>
                <!-- IntelliJ files -->
                <exclude>.idea/**/*</exclude>
                <exclude>src/test/resources/**/*</exclude>
                <!-- IDE settings imports -->
                <exclude>src/ide/**</exclude>
                <!-- does it even make sense to apply a license to a GPG signature? -->
                <exclude>**/*.asc</exclude>
                <!-- jQuery is MIT-licensed, but RAT can't figure it out -->
                <exclude>src/site/resources/js/jquery.js</exclude>
                <exclude>src/site/resources/js/jquery.min.js</exclude>
                <!-- Generated files -->
                <exclude>log4j-distribution/target/**/*</exclude>
                <exclude>log4j-distribution/.project</exclude>
                <exclude>log4j-distribution/.settings/**</exclude>
                <exclude>velocity.log</exclude>
                <!-- Other -->
                <exclude>felix-cache/**</exclude>
                <exclude>**/README.md</exclude>
                <exclude>RELEASE-NOTES.md</exclude>
                <exclude>SECURITY.md</exclude>
                <exclude>**/*.yml</exclude>
                <exclude>**/*.yaml</exclude>
                <exclude>**/*.json</exclude>
                <excllude>**/images/*.drawio</excllude>
                <exclude>**/fluent-bit.conf</exclude>
                <exclude>**/rabbitmq.config</exclude>
                <exclude>**/MANIFEST.MF</exclude>
                <exclude>.java-version</exclude>
              </excludes>
            </configuration>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!-- http://www.yourkit.com/docs/80/help/agent.jsp -->
      <id>yourkit-mac</id>
      <!--
      <activation>
        <os>
          <family>Mac</family>
        </os>
        <file>
          <exists>${yourkit.home}/bin/mac/libyjpagent.jnilib</exists>
        </file>
      </activation>
      -->
      <properties>
        <yourkit.home>/Applications/YJP.app</yourkit.home>
      </properties>
      <dependencies>
        <dependency>
          <groupId>com.yourkit</groupId>
          <artifactId>yjp-controller-api-redist</artifactId>
          <version>2013</version>
          <scope>system</scope>
          <systemPath>${yourkit.home}/lib/yjp-controller-api-redist.jar</systemPath>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>-agentpath:"${yourkit.home}/bin/mac/libyjpagent.jnilib"</argLine>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-failsafe-plugin</artifactId>
            <configuration>
              <argLine>-agentpath:"${yourkit.home}/bin/mac/libyjpagent.jnilib"</argLine>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>jdk8orGreater</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.revapi</groupId>
            <artifactId>revapi-maven-plugin</artifactId>
            <version>${revapi.plugin.version}</version>
            <dependencies>
              <dependency>
                <groupId>org.revapi</groupId>
                <artifactId>revapi-java</artifactId>
                <version>0.18.2</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <goals><goal>check</goal></goals>
                <configuration>
                  <checkDependencies>false</checkDependencies>
                  <skip>${revapi.skip}</skip>
                  <failOnMissingConfigurationFiles>false</failOnMissingConfigurationFiles>
                  <analysisConfigurationFiles>
                    <path>revapi.json</path>
                  </analysisConfigurationFiles>
                  <analysisConfiguration><![CDATA[
[
  {
     "extension": "revapi.java",
     "configuration": {
       "missing-classes": {
         "behavior": "report",
         "ignoreMissingAnnotations": false
       },
       "reportUsesFor": [
          "java.missing.newClass",
          "java.class.nonPublicPartOfAPI"
       ],
       "filter": {
         "classes": {
           "regex": true,
           "include": [
             "org\\.apache\\.logging\\.log4j(\\..+)?"
           ]
         },
         "packages": {
           "regex": true,
           "include": [
             "org\\.apache\\.logging\\.log4j(\\..+)?"
           ]
         }
       }
     }
  },
  {
    "extension": "revapi.ignore",
    "configuration": [
      {
  		"code": "java.method.returnTypeTypeParametersChanged",
  		"old": "method org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
  		"new": "method <B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B>>> B org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
  		"justification": "The compiler erases types (https://docs.oracle.com/javase/specs/jls/se8/html/jls-4.html#jls-4.6)"
	  },
      {
        "code": "java.generics.elementNowParameterized",
        "old": "method org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
        "new": "method <B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B>>> B org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
        "justification": "The compiler erases types (https://docs.oracle.com/javase/specs/jls/se8/html/jls-4.html#jls-4.6)"
      },
      {
        "code": "java.generics.formalTypeParameterAdded",
        "old": "method org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
        "new": "method <B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B>>> B org.apache.logging.log4j.core.appender.OutputStreamAppender::newBuilder()",
        "typeParameter": "B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B extends org.apache.logging.log4j.core.appender.OutputStreamAppender.Builder<B>>",
        "justification": "The compiler erases types (https://docs.oracle.com/javase/specs/jls/se8/html/jls-4.html#jls-4.6)"
      }
    ]
  }
]
              ]]></analysisConfiguration>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.revapi</groupId>
            <artifactId>revapi-maven-plugin</artifactId>
            <version>${revapi.plugin.version}</version>
            <reportSets>
              <reportSet>
                <inherited>false</inherited>
                <reports>
                  <report>report-aggregate</report>
                </reports>
              </reportSet>
              <reportSet>
                <reports>
                  <report>report</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>java8-doclint-disabled</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <javadoc.opts>-Xdoclint:none</javadoc.opts>
      </properties>
    </profile>
  </profiles>
</project>
