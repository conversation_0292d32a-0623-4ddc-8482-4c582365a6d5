package com.megabank.olp.common.persistence.pojo.code;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.megabank.olp.base.bean.BaseBean;

/**
 * The CodeTransmissionStatus is generated by Hibernate Tools 4.3.4.Final
 */
@Entity
@Table( name = "code_transmission_status" )
public class CodeTransmissionStatus extends BaseBean
{
	public static final String TABLENAME_CONSTANT = "code_transmission_status";

	public static final String TRANSMISSION_STATUS_CODE_CONSTANT = "transmissionStatusCode";

	public static final String NAME_CONSTANT = "name";

	private String transmissionStatusCode;

	private String name;

	public CodeTransmissionStatus()
	{}

	public CodeTransmissionStatus( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}

	@Column( name = "name", nullable = false )
	public String getName()
	{
		return name;
	}

	@Id
	@Column( name = "transmission_status_code", unique = true, nullable = false, length = 20 )
	public String getTransmissionStatusCode()
	{
		return transmissionStatusCode;
	}

	public void setName( String name )
	{
		this.name = name;
	}

	public void setTransmissionStatusCode( String transmissionStatusCode )
	{
		this.transmissionStatusCode = transmissionStatusCode;
	}
}