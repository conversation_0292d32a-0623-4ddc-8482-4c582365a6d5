package com.megabank.olp.client.service.iloan;

import java.util.Date;

import com.megabank.olp.client.sender.iloan.getted.caseDetailStatus.CaseDetailStatusILoanClient;
import com.megabank.olp.client.sender.iloan.getted.caseDetailStatus.bean.ILoanCaseDetailStatusArgBean;
import com.megabank.olp.client.sender.iloan.getted.caseDetailStatus.bean.ILoanCaseDetailStatusResultBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import com.megabank.olp.base.utility.date.CommonDateStringUtils;
import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusArgBean;
import com.megabank.olp.client.sender.eloan.getted.casestatus.bean.EloanCaseStatusResultBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoArgBean;
import com.megabank.olp.client.sender.eloan.getted.custinfo.bean.EloanCustInfoResultBean;
import com.megabank.olp.client.sender.eloan.submitted.EloanSubmittedResultBean;
import com.megabank.olp.client.sender.eloan.submitted.attachment.bean.AttachmentSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.mydata.bean.MyDataSubmittedArgBean;
import com.megabank.olp.client.sender.eloan.submitted.signing.bean.SigningContractCompletedArgBean;
import com.megabank.olp.client.sender.iloan.getted.caseStatus.CaseStatusIloanClient;
import com.megabank.olp.client.sender.iloan.getted.custInfo.CustInfoIloanClient;
import com.megabank.olp.client.sender.iloan.submitted.attachement.AttachmentSubmittedIloanClient;
import com.megabank.olp.client.sender.iloan.submitted.mydata.MyDataSubmittedIloanClient;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.PersonalLoanApplySubmittedIloanClient;
import com.megabank.olp.client.sender.iloan.submitted.personalloan.bean.PersonalLoanApplySubmittedIloanArgBean;
import com.megabank.olp.client.sender.iloan.submitted.signing.ContractCompletedIloanClient;

@Service
@Profile( { "sit", "stress", "uat", "prod" } )
public class SenderILoanServiceImp implements SenderILoanService
{
	@Autowired
	CaseStatusIloanClient caseStatusIloanClient;

	@Autowired
	CustInfoIloanClient custInfoIloanClient;

	@Autowired
	AttachmentSubmittedIloanClient attachmentSubmittedClient;

	@Autowired
	ContractCompletedIloanClient contractCompletedClient;

	@Autowired
	PersonalLoanApplySubmittedIloanClient personalLoanApplySubmittedClient;

	@Autowired
	MyDataSubmittedIloanClient myDataSubmittedIloanClient;

	@Autowired
	CaseDetailStatusILoanClient caseDetailStatusILoanClient;

	@Override
	public EloanCaseStatusResultBean getCaseStatusList( String idNo, Date birthDate )
	{
		EloanCaseStatusArgBean argBean = new EloanCaseStatusArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( CommonDateStringUtils.transDate2String( birthDate ) );
		argBean.setQueryWindow( 0 );

		return caseStatusIloanClient.send( argBean );
	}

	@Override
	public EloanCustInfoResultBean getILoanCustInfo( String idNo, Date birthDate )
	{
		EloanCustInfoArgBean argBean = new EloanCustInfoArgBean();
		argBean.setIdNo( idNo );
		argBean.setBirthDate( CommonDateStringUtils.transDate2String( birthDate ) );

		return custInfoIloanClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitAttachment( AttachmentSubmittedArgBean argBean )
	{
		return attachmentSubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitPersonalLoanApply( PersonalLoanApplySubmittedIloanArgBean argBean )
	{
		return personalLoanApplySubmittedClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitMyData( MyDataSubmittedArgBean argBean )
	{
		return myDataSubmittedIloanClient.send( argBean );
	}

	@Override
	public EloanSubmittedResultBean submitSigningContract( SigningContractCompletedArgBean argBean )
	{
		return contractCompletedClient.send( argBean );
	}

	@Override
	public ILoanCaseDetailStatusResultBean getCaseDetailStatus( String caseNo )
	{
		ILoanCaseDetailStatusArgBean argBean = new ILoanCaseDetailStatusArgBean();
		argBean.setCaseNo( caseNo );

		return caseDetailStatusILoanClient.send( argBean );
	}
}
