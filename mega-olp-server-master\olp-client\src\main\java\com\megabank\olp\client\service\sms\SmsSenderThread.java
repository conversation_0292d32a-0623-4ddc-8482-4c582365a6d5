package com.megabank.olp.client.service.sms;

import com.megabank.olp.client.sender.sms.SmsSenderClient;
import com.megabank.olp.client.sender.sms.bean.SmsSenderArgBean;
import com.megabank.olp.client.utility.enums.ClientSmsErrorEnum;
import com.megabank.olp.system.service.SystemService;
import com.megabank.olp.system.utility.enums.SystemErrorEnum;

/**
 *
 * @version 1.0
 * <AUTHOR>
 * @company mycompany
 * @copyright Copyright (c) 2020
 */
public class SmsSenderThread extends Thread
{
	private SmsSenderClient smsSenderClient;

	private SystemService systemService;

	private String contractNo;

	private String branchBankCode;

	private String mobileNumber;

	private String idNo;

	private String message;

	public SmsSenderThread( SmsSenderClient smsSenderClient, SystemService systemService )
	{
		this.smsSenderClient = smsSenderClient;
		this.systemService = systemService;
	}

	public String getBranchBankCode()
	{
		return branchBankCode;
	}

	public String getContractNo()
	{
		return contractNo;
	}

	public String getIdNo()
	{
		return idNo;
	}

	public String getMessage()
	{
		return message;
	}

	public String getMobileNumber()
	{
		return mobileNumber;
	}

	public SmsSenderClient getSmsSenderClient()
	{
		return smsSenderClient;
	}

	@Override
	public void run()
	{
		try
		{
			SmsSenderArgBean argBean = new SmsSenderArgBean();
			argBean.setContractNo( contractNo );
			argBean.setBranchBankCode( branchBankCode );
			argBean.setMobileNumber( mobileNumber );
			argBean.setIdNo( idNo );
			argBean.setMessage( message );

			boolean isSuccess = smsSenderClient.send( argBean ).isSuccess();

			if( !isSuccess )
				systemService.saveExceptionLog( ClientSmsErrorEnum.SEND_SMS_FAILED.getCode(), null, null, "olp-client", "寄送簡訊失敗" );
		}
		catch( Exception ex )
		{
			systemService.saveExceptionLog( SystemErrorEnum.CONNECTED_OTHER_SYSTEM.getCode(), ex, null, "olp-client", "連結 sms 系統錯誤" );
		}

	}

	public void setBranchBankCode( String branchBankCode )
	{
		this.branchBankCode = branchBankCode;
	}

	public void setContractNo( String contractNo )
	{
		this.contractNo = contractNo;
	}

	public void setIdNo( String idNo )
	{
		this.idNo = idNo;
	}

	public void setMessage( String message )
	{
		this.message = message;
	}

	public void setMobileNumber( String mobileNumber )
	{
		this.mobileNumber = mobileNumber;
	}

	public void setSmsSenderClient( SmsSenderClient smsSenderClient )
	{
		this.smsSenderClient = smsSenderClient;
	}

}
