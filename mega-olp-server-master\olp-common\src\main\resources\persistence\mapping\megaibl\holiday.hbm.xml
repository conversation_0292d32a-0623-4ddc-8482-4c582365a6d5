<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<sql-query name="holiday.getBusinessDay">
	<return-scalar column="businessDay" type="date"/>
	<![CDATA[
		SELECT MYDATE AS businessDay
		FROM MEGAIB.HOLIDAY 
		WHERE WKFLAG = '0' 
			AND MYDATE > CURRENT DATE
		ORDER BY MYDATE 
		FETCH FIRST 30 ROWS ONLY
	]]>
	</sql-query>
</hibernate-mapping>
