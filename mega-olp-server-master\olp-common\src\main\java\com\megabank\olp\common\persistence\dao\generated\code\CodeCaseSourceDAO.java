package com.megabank.olp.common.persistence.dao.generated.code;

import com.megabank.olp.base.bean.OrderBean;
import com.megabank.olp.base.layer.BasePojoDAO;
import com.megabank.olp.common.persistence.pojo.code.CodeCaseSource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CodeCaseSourceDAO extends BasePojoDAO<CodeCaseSource, String>
{
	public List<CodeCaseSource> getAllPojosOrderBydisplayOrder()
	{
		OrderBean orderBean = new OrderBean( CodeCaseSource.DISPLAY_ORDER_CONSTANT );

		return getAllPojosOrderBy( orderBean );
	}

	@Override
	protected Class<CodeCaseSource> getPojoClass()
	{
		return CodeCaseSource.class;
	}
}
